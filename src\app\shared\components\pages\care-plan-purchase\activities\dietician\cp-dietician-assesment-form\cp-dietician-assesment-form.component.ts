import { CommonModule, Location } from '@angular/common';
import { Component, inject, OnInit, ViewChild } from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import moment from 'moment';
import {
  BsDatepickerDirective,
  BsDatepickerConfig,
  BsDatepickerModule,
} from 'ngx-bootstrap/datepicker';
import { ToastrService } from 'ngx-toastr';
import { AdminService } from 'src/app/admin/service/admin.service';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { SelectSearchComponent } from 'src/app/shared/select-search/select-search.component';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';
import { CpDietConusltationFormComponent } from '../cp-diet-conusltation-form/cp-diet-conusltation-form.component';
import { CpDietAnthropometricFormComponent } from '../cp-diet-anthropometric-form/cp-diet-anthropometric-form.component';
import { CpDietDietaryFormComponent } from '../cp-diet-dietary-form/cp-diet-dietary-form.component';
import { CpDietLifestyleFormComponent } from '../cp-diet-lifestyle-form/cp-diet-lifestyle-form.component';
import { CpDietDiagnosisFormComponent } from '../cp-diet-diagnosis-form/cp-diet-diagnosis-form.component';
import { CpDietFollowFormComponent } from '../cp-diet-follow-form/cp-diet-follow-form.component';
import { CpDietNutritionCarePlanComponent } from '../cp-diet-nutrition-care-plan/cp-diet-nutrition-care-plan.component';
import { CpDietAttachementsResourcesComponent } from '../cp-diet-attachements-resources/cp-diet-attachements-resources.component';
import { CpDietConsentRemarkComponent } from '../cp-diet-consent-remark/cp-diet-consent-remark.component';
import { CpDietPatientFeedbackComponent } from '../cp-diet-patient-feedback/cp-diet-patient-feedback.component';
import { CpDietPlanComponent } from '../cp-diet-plan/cp-diet-plan.component';

@Component({
  selector: 'app-cp-dietician-assesment-form',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    BsDatepickerModule,
    CpDietAnthropometricFormComponent,
    CpDietDietaryFormComponent,
    CpDietLifestyleFormComponent,
    CpDietDiagnosisFormComponent,
    CpDietFollowFormComponent,
    CpDietNutritionCarePlanComponent,
    CpDietAttachementsResourcesComponent,
    CpDietConsentRemarkComponent,
    CpDietPatientFeedbackComponent,
    CpDietPlanComponent,
  ],
  templateUrl: './cp-dietician-assesment-form.component.html',
  styleUrl: './cp-dietician-assesment-form.component.scss',
})
export class CpDieticianAssesmentFormComponent implements OnInit {
  util = inject(UtilFunctions);
  patientId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['patientId']
  );
  activityId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['activityId']
  );
  carePlanId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['planId']
  );
  monthNumber = this.adminService.decrypt(
    this.activeRoute.snapshot.params['monthNumber']
  );
  user = this.authService.getDataFromSession('user');

  consultationId = this.adminService.decrypt(
    this.activeRoute.snapshot.queryParams['consultationId']
  );

  appointmentId = this.adminService.decrypt(
    this.activeRoute.snapshot.queryParams['appointmentId']
  );
  formType = this.activeRoute.snapshot.queryParams['formType'];
  isUpdate = this.activeRoute.snapshot.queryParams['isUpdate'] || false;

  isConsultationActive: boolean = false;
  isAnthropometricActive: boolean = false;
  isDietaryAssesmentActive: boolean = false;
  isLifeStyleActive: boolean = false;
  isDiagnosisActive: boolean = false;
  isCarePlanActive: boolean = false;
  isDietPlanActive: boolean = false;
  isFollowupActive: boolean = false;
  isAttachmentsActive: boolean = false;
  isConsentRemarkActive: boolean = false;
  isPatientFeedbackActive: boolean = false;

  currentTabName: string | null = null;
  isCompleted: boolean = false;

  diagnosisForm!: FormGroup;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private location: Location,
    private toastr: ToastrService,
    private authService: AuthService,
    private adminService: AdminService,
    private activeRoute: ActivatedRoute
  ) {}

  ngOnInit(): void {
    if (this.formType == 'Care') {
      this.currentTabName = 'Anthropometric';
      this.isAnthropometricActive = true;
    } else {
      this.currentTabName = 'Care Plan';
      this.isCarePlanActive = true;
    }
    this.getFullDietData(this.consultationId, this.patientId);
    this.getCompletedStatus(this.appointmentId);
  }

  back() {
    this.location.back();
    sessionStorage.removeItem('consultationId');
  }

  async getFullDietData(consultationId: any, patientId: any) {
    if (consultationId) {
      try {
        const data: any = await this.adminService.getFullDietData({
          patientId: Number(patientId),
          dietConsultationId: Number(consultationId),
        });
        console.log('Full diet Data', data);
        if (data?.length > 0) {
          // this.anthropometricData = data[0];
          // this.anthrometricForm.patchValue({
          //   consultationId: this.anthropometricData.consultationId || '',
          //   waistCircumferenceCm:
          //     this.anthropometricData.waistCircumferenceCm || '',
          //   hipCircumferenceCm:
          //     this.anthropometricData.hipCircumferenceCm || '',
          //   whrRatio: this.anthropometricData.whrRatio || '',
          //   bodyFatPercent: this.anthropometricData.bodyFatPercent || '',
          //   visceralFat: this.anthropometricData.visceralFat || '',
          //   idealBodyWeight:
          //     Number(this.anthropometricData.idealBodyWeight) || '',
          //   targetWeight: Number(this.anthropometricData.targetWeight) || '',
          // });
        } else {
          // this.anthropometricData = null;
        }
      } catch (error) {
        this.toastr.error('Failed to fetch anthropometric data.');
        console.error(error);
      }
    }
  }

  async getCompletedStatus(appointmentId: any) {
    try {
      const data: any =
        await this.adminService.getCompletedStatusByAppointmentId({
          appointmentId: appointmentId,
        });
      if (data) {
        if (data[0].status == 'Completed') {
          this.isCompleted = true;
        } else {
          this.isCompleted = false;
        }
      }
    } catch (error) {
      this.toastr.error('Failed to fetch Completed Status Info.');
      console.error(error);
    }
  }

  setActiveTab(tabname: string) {
    this.isConsultationActive = false;
    this.isAnthropometricActive = false;
    this.isDietaryAssesmentActive = false;
    this.isLifeStyleActive = false;
    this.isDiagnosisActive = false;
    this.isCarePlanActive = false;
    this.isDietPlanActive = false;
    this.isFollowupActive = false;
    this.isAttachmentsActive = false;
    this.isConsentRemarkActive = false;
    this.isPatientFeedbackActive = false;

    switch (tabname) {
      case 'consultation':
        this.isConsultationActive = true;
        this.currentTabName = 'Consultation';
        break;
      case 'anthropometric':
        this.isAnthropometricActive = true;
        this.currentTabName = 'Anthropometric';
        break;
      case 'dietaryAssesment':
        this.isDietaryAssesmentActive = true;
        this.currentTabName = 'Dietary Assesment';
        break;
      case 'lifestyle':
        this.isLifeStyleActive = true;
        this.currentTabName = 'LifeStyle';
        break;
      case 'diagnosisPlan':
        this.isDiagnosisActive = true;
        this.currentTabName = 'Diagnosis';
        break;
      case 'carePlan':
        this.isCarePlanActive = true;
        this.currentTabName = 'Care Plan';
        break;
      case 'dietPlan':
        this.isDietPlanActive = true;
        this.currentTabName = 'Diet Plan';
        break;
      case 'followup':
        this.isFollowupActive = true;
        this.currentTabName = 'Follow-Up';
        break;
      case 'attachments':
        this.isAttachmentsActive = true;
        this.currentTabName = 'Attachments-Resources';
        break;
      case 'consent-remark':
        this.isConsentRemarkActive = true;
        this.currentTabName = 'Consent-Remarks';
        break;
      case 'patientFeedback':
        this.isPatientFeedbackActive = true;
        this.currentTabName = 'Patient-Feedback';
        break;
    }
  }

  onChangeConsultationId(data: any) {
    this.consultationId = data;
    this.authService.saveDataToSession('consultationId', this.consultationId);
  }

  onCompletionChange() {
    if (this.consultationId && this.isCompleted) {
      const payload = {
        consultationId: Number(this.consultationId),
      };
      this.adminService.updateConsultationStatus(payload).subscribe({
        next: (res) => {
          this.toastr.success('Status updated to Completed');
          // if (res.status) {
          // } else {
          //   this.toastr.error(res.message);
          // }
        },
        error: (err) => {
          this.toastr.error('Something went wrong');
        },
      });
    }
  }
}
