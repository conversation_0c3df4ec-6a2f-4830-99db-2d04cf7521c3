<!-- Content -->
<div class="row pt-4">
  <!-- Health Records -->
  <div class="col-12 d-flex">
    <div class="flex-fill dashboard-card">
      <div class="dashboard-card-head">
        <div
          class="header-title w-100 d-flex justify-content-between align-items-center"
        >
          <h5>Health Records</h5>

          <div
            class="d-flex justify-content-end align-items-center flex-grow-1"
          >
            <button
              class="btn ai-btn mx-2 w-25"
              style="font-size: 0.75rem !important; padding: 16px !important"
              [routerLink]="['/patient/book-tele-medicine-appointment']"
            >
              Book Appointment
            </button>
            <div *ngIf="aiScanCount.data() && aiScanCount.data().length == 0">
              <div
                class="w-100"
                *ngIf="aiScanCount.data() && aiScanCount.data().length == 0"
              >
                <button
                  class="btn ai-btn w-100"
                  [class.loading]="isLoading"
                  [disabled]="isLoading"
                  (click)="generateReport()"
                >
                  <div class="btn-content">
                    <i class="fas fa-robot ai-icon"></i>
                    <span class="btn-text">I want a full health scan</span>
                    <span class="loading-dots">
                      <span>.</span>
                      <span>.</span>
                      <span>.</span>
                    </span>
                  </div>

                  <!-- Animated particles -->
                  <div
                    class="particle"
                    *ngFor="let particle of particles; trackBy: trackParticle"
                  ></div>
                </button>
              </div>
            </div>

            <div *ngIf="aiScanCount.data() && aiScanCount.data().length > 0">
              <div
                class="pb-2 w-100"
                *ngIf="
                  aiScanCount.data() &&
                  aiScanCount.data().length > 0 &&
                  aiScanCount.data()[0].scanCount == 1
                "
              >
                <button
                  class="btn ai-btn w-100"
                  [class.loading]="isLoading"
                  [disabled]="isLoading"
                  (click)="generateReport()"
                >
                  <div class="btn-content">
                    <i class="fas fa-robot ai-icon"></i>
                    <span class="btn-text">Generate my AI scan report</span>
                    <span class="loading-dots">
                      <span>.</span>
                      <span>.</span>
                      <span>.</span>
                    </span>
                  </div>

                  <!-- Animated particles -->
                  <div
                    class="particle"
                    *ngFor="let particle of particles; trackBy: trackParticle"
                  ></div>
                </button>
              </div>
              <div
                class="pb-2 w-100"
                *ngIf="
                  aiScanCount.data() &&
                  aiScanCount.data().length > 0 &&
                  aiScanCount.data()[0].scanCount == 2
                "
              >
                <button
                  class="btn ai-btn w-100"
                  [class.loading]="isLoading"
                  [disabled]="isLoading"
                  (click)="generateReport()"
                >
                  <div class="btn-content">
                    <i class="fas fa-robot ai-icon"></i>
                    <span class="btn-text">Complete AI Scan Report</span>
                    <span class="loading-dots">
                      <span>.</span>
                      <span>.</span>
                      <span>.</span>
                    </span>
                  </div>

                  <!-- Animated particles -->
                  <div
                    class="particle"
                    *ngFor="let particle of particles; trackBy: trackParticle"
                  ></div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dashboard-card-body">
        <div class="row">
          <div class="col-sm-7" *ngIf="recentVitals">
            <div class="row">
              <div class="col-lg-6">
                <div class="health-records icon-orange">
                  <span><i class="fa-heart fa-solid"></i>Heart Rate</span>
                  <h3>
                    {{ recentVitals.pulseRate ? recentVitals?.pulseRate : "" }}
                    Bpm
                    <!-- <sup> 2%</sup> -->
                  </h3>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="health-records icon-amber">
                  <span
                    ><i class="fa-solid fa-temperature-high"></i>Body
                    Temprature</span
                  >
                  <h3>
                    {{
                      recentVitals.temperature ? recentVitals?.temperature : ""
                    }}
                    {{ recentVitals.tempUnit ? recentVitals?.tempUnit : "" }}
                  </h3>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="health-records icon-dark-blue">
                  <span
                    ><i class="fa-notes-medical fa-solid"></i>Glucose
                    Level</span
                  >
                  <h3>
                    {{
                      recentVitals.bloodSugar ? recentVitals?.bloodSugar : ""
                    }}
                    <!-- <sup> 6%</sup> -->
                  </h3>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="health-records icon-blue">
                  <span><i class="fa-highlighter fa-solid"></i>SPo2</span>
                  <h3>
                    {{
                      recentVitals.oxygenSaturation
                        ? recentVitals?.oxygenSaturation
                        : ""
                    }}
                    %
                  </h3>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="health-records icon-red">
                  <span><i class="fa-solid fa-syringe"></i>Blood Pressure</span>
                  <h3>
                    {{
                      recentVitals.diastolicBp ? recentVitals?.diastolicBp : ""
                    }}
                    -
                    {{
                      recentVitals.systolicBp ? recentVitals?.systolicBp : ""
                    }}
                    mmHg
                    <!-- <sup> 2%</sup> -->
                  </h3>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="health-records icon-purple">
                  <span><i class="fa-solid fa-user-pen"></i>BMI </span>
                  <h3>{{ recentVitals.bmi ? recentVitals?.bmi : "" }} kg/m2</h3>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="health-records icon-green">
                  <span><i class="fa-solid fa-ruler-vertical"></i>Height</span>
                  <h3>
                    {{ recentVitals.height ? recentVitals?.height : "" }} cm
                  </h3>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="health-records icon-teal">
                  <span><i class="fa-solid fa-weight"></i>Weight</span>
                  <h3>
                    {{ recentVitals.weight ? recentVitals?.weight : "" }} kg
                  </h3>
                </div>
              </div>
              <div class="col-md-12">
                <div class="report-gen-date">
                  <p>
                    Report generated on last visit :
                    {{
                      recentVitals.recordedAt
                        ? (recentVitals?.recordedAt | date : "dd-MMM-yyyy")
                        : ""
                    }}
                    <span><i class="fa-copy fa-solid"></i></span>
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="col-sm-5" *ngIf="isHealthScoreLoading">
            <div class="chart-over-all-report">
              <h5>Overall Report</h5>
              <div class="circle-progressbar">
                <div class="circle-progress-percent" style="font-size: 3rem">
                  <span style="font-size: 1.5rem">Analyzing...</span>
                  <br />
                  <span><i class="fa-spinner fa-spin fa-solid"></i></span>
                </div>
              </div>
            </div>
          </div>

          <div class="col-sm-5" *ngIf="healthScore && !isHealthScoreLoading">
            <div class="chart-over-all-report">
              <h5>Overall Report</h5>
              <div class="circle-progressbar">
                <div class="circle-progress-percent">
                  Last visit
                  <br />
                  <span class="">
                    {{
                      healthScore.visitDate
                        ? (healthScore?.visitDate | date : "dd MMM yyyy")
                        : "N/A"
                    }}
                  </span>
                </div>
                <div class="circle-progress-text" style="color: transparent">
                  {{
                    healthScore.visitDate
                      ? (healthScore?.visitDate | date : "dd-MMM-yyyy")
                      : "N/A"
                  }}
                </div>
              </div>
              <circle-progress
                class="circle-progress-bar"
                [percent]="
                  healthScore.healthScore ? healthScore?.healthScore.score : 0
                "
                [clockwise]="true"
                innerStrokeColor="#e7e8ea"
                outerStrokeColor="#04BD6C"
              >
              </circle-progress>
              <span class="health-percentage" style="font-size: 0.85rem"
                >Your health is
                {{
                  healthScore.healthScore ? healthScore?.healthScore.score : 0
                }}%
                {{
                  healthScore.healthScore
                    ? healthScore?.healthScore.riskLevel
                    : "Normal"
                }}
                Risk</span
              >
              <a [routerLink]="routes.medicaldetails" class="btn btn-dark w-100"
                >View Details<i class="fa-chevron-right fa-solid ms-2"></i
              ></a>
            </div>
          </div>
          <div
            class="col-sm-5"
            *ngIf="healthScore == null && !isHealthScoreLoading"
          >
            <div
              class="chart-over-all-report d-flex align-items-center justify-content-center"
              style="height: 250px"
            >
              <h5 class="text-muted m-0 text-center">No Vitals Found</h5>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--Book Appointment & Favorites -->
  <div class="d-none">
    <div class="w-100 favourites-dashboard">
      <div *ngIf="aiScanCount.data() && aiScanCount.data().length == 0">
        <div
          class="pb-2 w-100"
          *ngIf="aiScanCount.data() && aiScanCount.data().length == 0"
        >
          <button
            class="btn ai-btn w-100"
            [class.loading]="isLoading"
            [disabled]="isLoading"
            (click)="generateReport()"
          >
            <div class="btn-content">
              <i class="fas fa-robot ai-icon"></i>
              <span class="btn-text">I want a full health scan</span>
              <span class="loading-dots">
                <span>.</span>
                <span>.</span>
                <span>.</span>
              </span>
            </div>

            <!-- Animated particles -->
            <div
              class="particle"
              *ngFor="let particle of particles; trackBy: trackParticle"
            ></div>
          </button>
        </div>
      </div>

      <div *ngIf="aiScanCount.data() && aiScanCount.data().length > 0">
        <div
          class="pb-2 w-100"
          *ngIf="
            aiScanCount.data() &&
            aiScanCount.data().length > 0 &&
            aiScanCount.data()[0].scanCount == 1
          "
        >
          <button
            class="btn ai-btn w-100"
            [class.loading]="isLoading"
            [disabled]="isLoading"
            (click)="generateReport()"
          >
            <div class="btn-content">
              <i class="fas fa-robot ai-icon"></i>
              <span class="btn-text">Generate my AI scan report</span>
              <span class="loading-dots">
                <span>.</span>
                <span>.</span>
                <span>.</span>
              </span>
            </div>

            <!-- Animated particles -->
            <div
              class="particle"
              *ngFor="let particle of particles; trackBy: trackParticle"
            ></div>
          </button>
        </div>
        <div
          class="pb-2 w-100"
          *ngIf="
            aiScanCount.data() &&
            aiScanCount.data().length > 0 &&
            aiScanCount.data()[0].scanCount == 2
          "
        >
          <button
            class="btn ai-btn w-100"
            [class.loading]="isLoading"
            [disabled]="isLoading"
            (click)="generateReport()"
          >
            <div class="btn-content">
              <i class="fas fa-robot ai-icon"></i>
              <span class="btn-text">Complete AI Scan Report</span>
              <span class="loading-dots">
                <span>.</span>
                <span>.</span>
                <span>.</span>
              </span>
            </div>

            <!-- Animated particles -->
            <div
              class="particle"
              *ngFor="let particle of particles; trackBy: trackParticle"
            ></div>
          </button>
        </div>
      </div>
      <a routerLink="/patient/appointments" class="book-appointment-head">
        <h3><span>Book a new</span>Appointment</h3>
        <span class="add-icon"
          ><a routerLink="/patient/appointments"
            ><i class="fa-circle-plus fa-solid"></i></a
        ></span>
      </a>
      <div class="w-100 dashboard-card">
        <div class="dashboard-card-head">
          <div class="header-title">
            <h5>Favourites</h5>
          </div>
          <!-- <div class="card-view-link">
            <a [routerLink]="routes.favourites">View All</a>
          </div> -->
        </div>

        <div class="dashboard-card-body">
          <LoadingSpinner
            [isLoading]="
              patientFavourites.isLoading() || patientFavourites.isPending()
            "
          ></LoadingSpinner>

          <ErrorAlert
            [error]="patientFavourites.error()"
            *ngIf="patientFavourites.isError()"
          ></ErrorAlert>

          <ng-container
            *ngIf="
              patientFavourites.isSuccess() &&
              patientFavourites.data() &&
              patientFavourites.data().length > 0
            "
          >
            <div
              class="doctor-fav-list"
              *ngFor="let favourite of patientFavourites.data().slice(0, 5)"
            >
              <div class="doctor-info-profile">
                <a href="javascript:void(0);" class="table-avatar">
                  <img
                    [src]="
                      favourite.image ||
                      util.getNameBasedDoctorAvatar(favourite.doctorName)
                    "
                    alt="Img"
                  />
                </a>
                <div class="doctor-name-info">
                  <h5>
                    <a href="javascript:void(0);"
                      >Dr. {{ favourite.doctorName }}</a
                    >
                  </h5>
                  <span>Appointments: {{ favourite.visitCount }}</span>
                </div>
              </div>
              <!-- <a href="javascript:void(0);" class="cal-plus-icon"
                ><i class="isax isax-calendar5"></i
              ></a> -->
            </div>
          </ng-container>

          <ng-container
            *ngIf="
              patientFavourites.isSuccess() &&
              patientFavourites.data() &&
              patientFavourites.data().length == 0
            "
          >
            <div class="doctor-fav-list">
              <div class="doctor-info-profile">
                <a href="javascript:void(0);" class="table-avatar">
                  <img [src]="util.getNormalAvatar()" alt="Img" />
                </a>
                <div class="doctor-name-info">
                  <h5><a href="javascript:void(0);">No Favourites</a></h5>
                  <!-- <span>Endodontists</span> -->
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
  <!-- Appointment & Analytics -->
  <div class="d-none row mx-0 px-0">
    <div class="col-xl-5 d-flex flex-column">
      <div class="flex-fill dashboard-card">
        <div class="dashboard-card-head">
          <div class="header-title">
            <h5>Upcoming Appointment</h5>
          </div>
          <div class="card-view-link">
            <div class="nav-control text-end owl-nav slide-nav"></div>
          </div>
        </div>
        <div class="dashboard-card-body">
          <div class="apponiment-dates d-flex flex-column overflow-auto">
            <div
              class="appointment-dash-card"
              *ngFor="let upApp of upcomingAppointments"
              (click)="viewAppointment(upApp.id)"
            >
              <div class="doctor-fav-list">
                <div class="doctor-info-profile">
                  <a href="javascript:void(0);" class="table-avatar">
                    <img
                      [src]="upApp.image || 'assets/images/avatar-image.webp'"
                      alt="Img"
                    />
                  </a>
                  <div class="doctor-name-info">
                    <h5>
                      <a href="javascript:void(0);"
                        >Dr.{{ upApp.doctorName }}</a
                      >
                    </h5>

                    <a
                      class="text-primary"
                      [routerLink]="['/patient/view-appointment', upApp.id]"
                      >View</a
                    >
                  </div>
                </div>
                <span class="badge bg-orange"
                  ><i class="isax isax-video5 me-1"></i
                  >{{ upApp.durationMinutes }} Min</span
                >
              </div>
              <div class="date-time pt-2">
                <h6 class="py-1">
                  {{ upApp.appointmentDate | date : "EEEE, MMM yyyy" }}
                </h6>
                <p class="m-0 pb-1">
                  <i class="isax isax-clock5"></i>
                  {{ convertTo12HourFormat(upApp.appointmentTime) }}
                </p>
                <p class="m-0">
                  <span><i class="fa-location-dot fa-solid"></i></span
                  >{{ upApp.location }}
                </p>
              </div>
              <!-- <div class="card-btns gap-3">
                <a [routerLink]="routes.chat" class="btn btn-gray btn-md"
                  ><i class="isax isax-messages-25"></i>Chat Now</a
                >
                <a
                  [routerLink]="routes.patientAppointment"
                  class="btn btn-md btn-primary-gradient rounded-pill"
                  ><i class="isax isax-calendar-tick5"></i>Attend</a
                >
              </div> -->
            </div>
            <div *ngIf="upcomingAppointments.length <= 0">
              <p class="text-center">No Appointments scheduled</p>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="w-100 dashboard-card">
        <div class="dashboard-card-head">
          <div class="header-title">
            <h5>Notifications</h5>
          </div>
          <div class="card-view-link">
            <a href="javascript:void(0);">View All</a>
          </div>
        </div>
        <div class="dashboard-card-body">
          <div class="table-responsive">
            <table class="table dashboard-table">
              <tbody>
                <tr>
                  <td>
                    <div class="table-noti-info">
                      <div class="color-violet table-noti-icon">
                        <i class="fa-bell fa-solid"></i>
                      </div>

                      <div class="table-noti-message">
                        <h6>
                          <a href="javascript:void(0);"
                            >Booking Confirmed on
                            <span> 21 Mar 2024 </span> 10:30 AM</a
                          >
                        </h6>
                        <span class="message-time">Just Now</span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="table-noti-info">
                      <div class="color-blue table-noti-icon">
                        <i class="fa-solid fa-star"></i>
                      </div>

                      <div class="table-noti-message">
                        <h6>
                          <a href="javascript:void(0);"
                            >You have a <span> New </span> Review for your
                            Appointment
                          </a>
                        </h6>
                        <span class="message-time">5 Days ago</span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="table-noti-info">
                      <div class="color-red table-noti-icon">
                        <i class="fa-calendar-check fa-solid"></i>
                      </div>

                      <div class="table-noti-message">
                        <h6>
                          <a href="javascript:void(0);"
                            >You have Appointment with <span> Ahmed </span> by
                            01:20 PM
                          </a>
                        </h6>
                        <span class="message-time">12:55 PM</span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="table-noti-info">
                      <div class="color-yellow table-noti-icon">
                        <i class="fa-money-bill-1-wave fa-solid"></i>
                      </div>

                      <div class="table-noti-message">
                        <h6>
                          <a href="javascript:void(0);"
                            >Sent an amount of <span> $200 </span> for an
                            Appointment by 01:20 PM
                          </a>
                        </h6>
                        <span class="message-time">2 Days ago</span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="table-noti-info">
                      <div class="color-blue table-noti-icon">
                        <i class="fa-solid fa-star"></i>
                      </div>

                      <div class="table-noti-message">
                        <h6>
                          <a href="javascript:void(0);"
                            >You have a <span> New </span> Review for your
                            Appointment
                          </a>
                        </h6>
                        <span class="message-time">5 Days ago</span>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div> -->
    </div>
    <div class="col-xl-7 d-flex flex-column">
      <div class="w-100 dashboard-card">
        <div class="dashboard-card-head">
          <div class="header-title">
            <h5>Analytics</h5>
          </div>
          <!-- <div class="d-flex flex-wrap dropdown-links align-items-center">
            <div class="dropdown header-dropdown header-dropdown-two">
              <a
                class="dropdown-toggle"
                data-bs-toggle="dropdown"
                href="javascript:void(0);"
              >
                Mar 14 - Mar 21
              </a>
              <div class="dropdown-menu dropdown-menu-end">
                <a href="javascript:void(0);" class="dropdown-item">
                  This Week
                </a>
                <a href="javascript:void(0);" class="dropdown-item">
                  This Month
                </a>
                <a href="javascript:void(0);" class="dropdown-item">
                  This Year
                </a>
              </div>
            </div>
          </div> -->
        </div>
        <div class="dashboard-card-body">
          <div class="chart-tabs">
            <ul class="nav" role="tablist">
              <li class="nav-item" role="presentation">
                <a
                  class="nav-link active"
                  href="javascript:void(0);"
                  data-bs-toggle="tab"
                  data-bs-target="#heart-rate"
                  aria-selected="false"
                  role="tab"
                  tabindex="-1"
                  >Heart Rate</a
                >
              </li>
              <li class="nav-item" role="presentation">
                <a
                  class="nav-link"
                  href="javascript:void(0);"
                  data-bs-toggle="tab"
                  data-bs-target="#blood-pressure"
                  aria-selected="true"
                  role="tab"
                  >Blood Pressure</a
                >
              </li>
            </ul>
          </div>
          <div class="pt-0 tab-content">
            <!-- Chart -->
            <div
              class="active fade show tab-pane"
              id="heart-rate"
              role="tabpanel"
            >
              <div id="heart-rate-chart">
                <apx-chart
                  [series]="chartOptions1.series"
                  [chart]="chartOptions1.chart"
                  [dataLabels]="chartOptions1.dataLabels"
                  [plotOptions]="chartOptions1.plotOptions"
                  [xaxis]="chartOptions1.xaxis"
                  [stroke]="chartOptions1.stroke"
                  [fill]="chartOptions1.fill"
                ></apx-chart>
              </div>
            </div>
            <!-- /Chart -->

            <!-- Chart -->
            <div class="fade tab-pane" id="blood-pressure" role="tabpanel">
              <div id="blood-pressure-chart">
                <apx-chart
                  [series]="chartOptions2.series"
                  [chart]="chartOptions2.chart"
                  [dataLabels]="chartOptions2.dataLabels"
                  [plotOptions]="chartOptions2.plotOptions"
                  [xaxis]="chartOptions2.xaxis"
                  [stroke]="chartOptions2.stroke"
                  [fill]="chartOptions2.fill"
                ></apx-chart>
              </div>
            </div>
            <!-- /Chart -->
          </div>
        </div>
      </div>
      <div class="w-100 dashboard-card">
        <div class="dashboard-card-head">
          <div class="header-title">
            <h5>Past Appointments</h5>
          </div>
          <div class="card-view-link">
            <div class="nav-control text-end owl-nav slide-nav2"></div>
          </div>
        </div>
        <div class="dashboard-card-body">
          <div class="past-appointments-slider">
            <owl-carousel-o [options]="doctorSliderOptions">
              <ng-template
                carouselSlide
                *ngFor="let pastApp of pastAppointments"
              >
                <div
                  class="appointment-dash-card past-appointment"
                  (click)="
                    viewVisitDetails(pastApp, user.userId, pastApp.doctorId)
                  "
                >
                  <div class="doctor-fav-list">
                    <div class="doctor-info-profile">
                      <a href="javascript:void(0);" class="table-avatar">
                        <img
                          [src]="
                            pastApp.image || 'assets/images/avatar-image.webp'
                          "
                          alt="Img"
                        />
                      </a>
                      <div class="doctor-name-info">
                        <h5>
                          <a href="javascript:void(0);"
                            >Dr.{{ pastApp.doctorName }}</a
                          >
                        </h5>
                        <span class="text-warning">
                          <a
                            routerLink="/patient/reviews/add/{{
                              pastApp.doctorId
                            }}"
                            class="text-warning"
                            >Add Review</a
                          >
                        </span>
                      </div>
                    </div>
                    <span class="badge bg-orange"
                      ><i class="isax isax-video5 me-1"></i
                      >{{ pastApp.durationMinutes }} Min</span
                    >
                  </div>
                  <div class="appointment-date-info">
                    <!-- <h6>Thursday, Mar 2024</h6> -->
                    <h6>
                      {{ pastApp.appointmentDate | date : "EEEE, MMM yyyy" }}
                    </h6>
                    <ul>
                      <li>
                        <span><i class="isax isax-clock5"></i></span>Time :
                        {{ convertTo12HourFormat(pastApp.appointmentTime) }} -
                        ({{ pastApp.durationMinutes }} Min)
                      </li>
                      <li>
                        <span><i class="fa-location-dot fa-solid"></i></span
                        >{{ pastApp.location }}
                      </li>
                    </ul>
                  </div>
                  <!-- <div class="card-btns">
                    <a
                      [routerLink]="routes.patientAppointment"
                      class="btn btn-outline-primary me-3 ms-0"
                      >Reschedule</a
                    >
                    <a
                      [routerLink]="routes.patientAppointmentDetails"
                      class="btn btn-primary prime-btn"
                      >View Details</a
                    >
                  </div> -->
                </div>
              </ng-template>
            </owl-carousel-o>
          </div>
        </div>
      </div>
      <div class="w-100 dashboard-card">
        <div class="dashboard-card-head">
          <div class="header-title">
            <h5>Family Members</h5>
          </div>
          <!-- <div class="card-view-link">
            <a
              href="javascript:void(0);"
              class="add-new"
              data-bs-toggle="modal"
              data-bs-target="#add_dependent"
              ><i class="fa-circle-plus fa-solid me-2"></i>Add New</a
            >
            <a [routerLink]="routes.dependentList">View All</a>
          </div> -->
        </div>
        <div class="dashboard-card-body" *ngIf="filteredUsers.length > 0">
          <div class="doctor-fav-list" *ngFor="let user of filteredUsers">
            <div class="doctor-info-profile">
              <a href="javascript:void(0);" class="table-avatar">
                <img src="assets/images/avatar-image.webp" alt="Img" />
              </a>
              <div class="doctor-name-info">
                <h5>
                  <a href="javascript:void(0);">{{ user.name }}</a>
                </h5>
                <span
                  >{{ user.relationship }} -
                  {{ util.calculateAge(user.dob) }} old</span
                >
              </div>
            </div>
            <!-- <div class="d-flex align-items-center">
              <a href="javascript:void(0);" class="cal-plus-icon me-2"
                ><i class="isax isax-calendar5"></i
              ></a>
              <a [routerLink]="routes.dependentList" class="cal-plus-icon"
                ><i class="isax isax-eye4"></i
              ></a>
            </div> -->
          </div>
        </div>

        <div class="dashboard-card-body" *ngIf="filteredUsers.length === 0">
          No Family Members
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Reports -->
<!-- <div class="row">
    <div class="col-xl-12 d-flex">
      <div class="w-100 dashboard-card">
        <div class="dashboard-card-head">
          <div class="header-title">
            <h5>Reports</h5>
          </div>
        </div>
        <div class="dashboard-card-body">
          <div class="account-detail-table">
            <nav class="border-0 mb-3 mt-3 patient-dash-tab pb-0">
              <ul class="nav nav-tabs-bottom">
                <li class="nav-item">
                  <a
                    class="nav-link active"
                    href="#family-tab"
                    data-bs-toggle="tab"
                    >Family Members</a
                  >
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="#medical-tab" data-bs-toggle="tab"
                    >Medical Records</a
                  >
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="#prsc-tab" data-bs-toggle="tab"
                    >Prescriptions</a
                  >
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="#invoice-tab" data-bs-toggle="tab"
                    >Invoices</a
                  >
                </li>
              </ul>
            </nav>
            <div class="pt-0 tab-content">
              <div id="family-tab" class="active fade show tab-pane">
                <div class="custom-new-table">
                  <div class="table-responsive">
                    <table class="table table-center table-hover mb-0">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Relation</th>
                          <th>Access</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let member of familyMembers
                              | paginate
                                : {
                                    itemsPerPage: 5,
                                    currentPage: familyPage
                                  }
                          "
                        >
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src='assets/images/avatar-image.webp'
                                  alt="User Image"
                                />
                              </a>
                              <a>{{
                                member.fullName ? member.fullName : "Henry"
                              }}</a>
                            </h2>
                          </td>
                          <td>
                            {{
                              member.relationship
                                ? member.relationship
                                : "Other"
                            }}
                          </td>
                          <td>
                            {{
                              member.accessLevel
                                ? member.accessLevel
                                : "Read-Only"
                            }}
                          </td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#view_report"
                              >
                                <i class="isax isax-edit-2"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <pagination-controls
                *ngIf="familyMembers.length > 5"
                (pageChange)="familyPage = $event"
              ></pagination-controls>
              <div class="fade tab-pane" id="medical-tab">
                <div class="custom-table">
                  <div class="table-responsive">
                    <table class="table table-center mb-0">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Date</th>
                          <th>Description</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <ng-container *ngIf="medicalRecords.length > 0">
                          @for(record of medicalRecords;track $index){
                          <tr>
                            <td>
                              <a href="javascript:void(0);" class="lab-icon">
                                <span
                                  ><i class="fa-solid fa-paperclip"></i></span
                                >Lab Report
                              </a>
                            </td>
                            <td>24 Mar 2024</td>
                            <td>Glucose Test V12</td>
                            <td>
                              <div class="action-item">
                                <a href="javascript:void(0);">
                                  <i class="fa-solid fa-pen-to-square"></i>
                                </a>
                                <a href="javascript:void(0);">
                                  <i class="fa-solid fa-download"></i>
                                </a>
                                <a href="javascript:void(0);">
                                  <i class="fa-solid fa-trash-can"></i>
                                </a>
                              </div>
                            </td>
                          </tr>
                          }
                        </ng-container>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div class="fade tab-pane" id="prsc-tab">
                <div class="custom-table">
                  <div class="table-responsive">
                    <table class="table table-center mb-0">
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Name</th>
                          <th>Date</th>
                          <th>Prescriped By</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td class="link-primary">
                            <a
                              href="javascript:void(0);"
                              data-bs-toggle="modal"
                              data-bs-target="#view_prescription"
                              >#P1236</a
                            >
                          </td>
                          <td>
                            <a
                              href="javascript:void(0);"
                              class="lab-icon prescription"
                              >Prescription</a
                            >
                          </td>
                          <td>21 Mar 2024, 10:30 AM</td>
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src="https://i.pravatar.cc/50?img=68"
                                  alt="User Image"
                                />
                              </a>
                              <a [routerLink]="routes.doctorProfile1"
                                >Edalin Hendry</a
                              >
                            </h2>
                          </td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#view_prescription"
                              >
                                <i class="isax isax-link-2"></i>
                              </a>
                              <a href="javascript:void(0);">
                                <i class="isax isax-import"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td class="link-primary">
                            <a
                              href="javascript:void(0);"
                              data-bs-toggle="modal"
                              data-bs-target="#view_prescription"
                              >#P3656</a
                            >
                          </td>
                          <td>
                            <a
                              href="javascript:void(0);"
                              class="lab-icon prescription"
                              >Prescription</a
                            >
                          </td>
                          <td>28 Mar 2024, 11:40 AM</td>
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src="https://i.pravatar.cc/50?img=60"
                                  alt="User Image"
                                />
                              </a>
                              <a [routerLink]="routes.doctorProfile1"
                                >John Homes</a
                              >
                            </h2>
                          </td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#view_prescription"
                              >
                                <i class="isax isax-link-2"></i>
                              </a>
                              <a href="javascript:void(0);">
                                <i class="isax isax-import"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td class="link-primary">
                            <a
                              href="javascript:void(0);"
                              data-bs-toggle="modal"
                              data-bs-target="#view_prescription"
                              >#P1246</a
                            >
                          </td>
                          <td>
                            <a
                              href="javascript:void(0);"
                              class="lab-icon prescription"
                              >Prescription</a
                            >
                          </td>
                          <td>11 Apr 2024, 09:00 AM</td>
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src="https://i.pravatar.cc/50?img=59"
                                  alt="User Image"
                                />
                              </a>
                              <a [routerLink]="routes.doctorProfile1"
                                >Shanta Neill</a
                              >
                            </h2>
                          </td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#view_prescription"
                              >
                                <i class="isax isax-link-2"></i>
                              </a>
                              <a href="javascript:void(0);">
                                <i class="isax isax-import"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td class="link-primary">
                            <a
                              href="javascript:void(0);"
                              data-bs-toggle="modal"
                              data-bs-target="#view_prescription"
                              >#P6985</a
                            >
                          </td>
                          <td>
                            <a
                              href="javascript:void(0);"
                              class="lab-icon prescription"
                              >Prescription</a
                            >
                          </td>
                          <td>15 Apr 2024, 02:30 PM</td>
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src="https://i.pravatar.cc/50?img=62"
                                  alt="User Image"
                                />
                              </a>
                              <a [routerLink]="routes.doctorProfile1"
                                >Anthony Tran</a
                              >
                            </h2>
                          </td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#view_prescription"
                              >
                                <i class="isax isax-link-2"></i>
                              </a>
                              <a href="javascript:void(0);">
                                <i class="isax isax-import"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td class="link-primary">
                            <a
                              href="javascript:void(0);"
                              data-bs-toggle="modal"
                              data-bs-target="#view_prescription"
                              >#P3659</a
                            >
                          </td>
                          <td>
                            <a
                              href="javascript:void(0);"
                              class="lab-icon prescription"
                              >Prescription</a
                            >
                          </td>
                          <td>23 Apr 2024, 06:40 PM</td>
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src="https://i.pravatar.cc/50?img=18"
                                  alt="User Image"
                                />
                              </a>
                              <a [routerLink]="routes.doctorProfile1"
                                >Susan Lingo</a
                              >
                            </h2>
                          </td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#view_prescription"
                              >
                                <i class="isax isax-link-2"></i>
                              </a>
                              <a href="javascript:void(0);">
                                <i class="isax isax-import"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <div class="fade tab-pane" id="invoice-tab">
                <div class="custom-table">
                  <div class="table-responsive">
                    <table class="table table-center mb-0">
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Doctor</th>
                          <th>Appointment Date</th>
                          <th>Booked on</th>
                          <th>Amount</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>
                            <a
                              href="javascript:void(0);"
                              data-bs-toggle="modal"
                              data-bs-target="#invoice_view"
                              class="link-primary"
                              >#INV1236</a
                            >
                          </td>
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src="https://i.pravatar.cc/50?img=12"
                                  alt="User Image"
                                />
                              </a>
                              <a [routerLink]="routes.doctorProfile1"
                                >Edalin Hendry</a
                              >
                            </h2>
                          </td>
                          <td>24 Mar 2024</td>
                          <td>21 Mar 2024</td>
                          <td>$300</td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#invoice_view"
                              >
                                <i class="isax isax-link-2"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <a
                              href="javascript:void(0);"
                              data-bs-toggle="modal"
                              data-bs-target="#invoice_view"
                              class="link-primary"
                              >#NV3656</a
                            >
                          </td>
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src="https://i.pravatar.cc/50?img=60"
                                  alt="User Image"
                                />
                              </a>
                              <a [routerLink]="routes.doctorProfile1"
                                >John Homes</a
                              >
                            </h2>
                          </td>
                          <td>17 Mar 2024</td>
                          <td>14 Mar 2024</td>
                          <td>$450</td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#invoice_view"
                              >
                                <i class="isax isax-link-2"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <a
                              href="javascript:void(0);"
                              data-bs-toggle="modal"
                              data-bs-target="#invoice_view"
                              class="link-primary"
                              >#INV1246</a
                            >
                          </td>
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src="https://i.pravatar.cc/50?img=15"
                                  alt="User Image"
                                />
                              </a>
                              <a [routerLink]="routes.doctorProfile1"
                                >Shanta Neill</a
                              >
                            </h2>
                          </td>
                          <td>11 Mar 2024</td>
                          <td>07 Mar 2024</td>
                          <td>$250</td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#invoice_view"
                              >
                                <i class="isax isax-link-2"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <a
                              href="javascript:void(0);"
                              data-bs-toggle="modal"
                              data-bs-target="#invoice_view"
                              class="link-primary"
                              >#INV6985</a
                            >
                          </td>
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src="https://i.pravatar.cc/50?img=5"
                                  alt="User Image"
                                />
                              </a>
                              <a [routerLink]="routes.doctorProfile1"
                                >Anthony Tran</a
                              >
                            </h2>
                          </td>
                          <td>26 Feb 2024</td>
                          <td>23 Feb 2024</td>
                          <td>$320</td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#invoice_view"
                              >
                                <i class="isax isax-link-2"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <a
                              href="javascript:void(0);"
                              data-bs-toggle="modal"
                              data-bs-target="#invoice_view"
                              class="link-primary"
                              >#INV3659</a
                            >
                          </td>
                          <td>
                            <h2 class="table-avatar">
                              <a
                                [routerLink]="routes.doctorProfile1"
                                class="avatar avatar-sm me-2"
                              >
                                <img
                                  class="rounded-3 avatar-img"
                                  src="https://i.pravatar.cc/50?img=14"
                                  alt="User Image"
                                />
                              </a>
                              <a [routerLink]="routes.doctorProfile1"
                                >Susan Lingo</a
                              >
                            </h2>
                          </td>
                          <td>18 Feb 2024</td>
                          <td>15 Feb 2024</td>
                          <td>$480</td>
                          <td>
                            <div class="action-item">
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#invoice_view"
                              >
                                <i class="isax isax-link-2"></i>
                              </a>
                              <a
                                href="javascript:void(0);"
                                data-bs-toggle="modal"
                                data-bs-target="#delete_modal"
                              >
                                <i class="isax isax-trash"></i>
                              </a>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div> -->

<!-- Visits - Activity Based -->
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div
          class="card-header bg-primary text-white d-flex justify-content-between align-items-center"
        >
          <h5 class="card-title mb-0 text-white">
            <i class="fas fa-calendar-check me-2"></i>
            Your Activity Visits
          </h5>
          <button
            class="btn btn-light btn-sm ms-2"
            (click)="loadMoreLabVisits()"
          >
            View All
          </button>
        </div>
        <div class="card-body p-0">
          <!-- Table Section -->
          <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
              <thead class="table-dark sticky-top">
                <tr>
                  <th scope="col" class="text-center">#</th>

                  <th scope="col">Diagnosis</th>
                  <th scope="col">Visit Type</th>
                  <th scope="col" class="text-center">Status</th>
                  <th scope="col" class="text-end">Date</th>
                  <!-- <th scope="col" class="text-center">Actions</th> -->
                </tr>
              </thead>
              <tbody
                *ngIf="
                  allActivityVisits.isSuccess() &&
                  allActivityVisits.data() &&
                  allActivityVisits.data().length > 0
                "
              >
                <tr
                  *ngFor="let visit of allActivityVisits.data(); let i = index"
                  class="align-middle"
                >
                  <td class="text-center fw-bold">{{ i + 1 }}</td>

                  <!-- Name -->
                  <td class="pointer">
                    <div
                      class="fw-bold text-primary pointer"
                      (click)="onVisitsNameClick(visit)"
                    >
                      {{
                        visit.diagnosis[0]?.diagnosisTitle ||
                          visit.complaints[0]?.complaintTitle ||
                          visit.appointmentComplaintTitle[0]?.complaintTitle ||
                          "General"
                      }}
                    </div>
                  </td>

                  <!-- Visit Type -->
                  <td>
                    <div>
                      {{ visit.visitType ? visit.visitType : "N/A" }}
                    </div>
                  </td>

                  <!-- Status -->
                  <td class="text-center">
                    <span class="">
                      {{ visit.visitStatus ? visit.visitStatus : "N/A" }}
                    </span>
                  </td>

                  <!-- Date -->
                  <td class="text-end">
                    <div class="">
                      <div>
                        {{
                          visit.visitDate
                            ? (visit.visitDate | date : "dd MMM yyyy")
                            : "N/A"
                        }}
                      </div>
                    </div>
                  </td>

                  <!-- Actions -->
                  <!-- <td class="text-center">
                      <div class="btn-group" role="group">
                        <button
                          class="btn btn-outline-primary btn-sm"
                          (click)="onViewReportsClick(item)"
                          ngbTooltip="View Report"
                        >
                          <i class="fas fa-eye"></i>
                        </button>
                      
                      </div>
                    </td> -->
                </tr>
              </tbody>
            </table>
          </div>

          <!-- No Data State -->
          <div
            *ngIf="
              allActivityVisits.isSuccess() &&
              allActivityVisits.data().length === 0
            "
            class="text-center py-5"
          >
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No records found</h5>
            <p class="text-muted">
              Try adjusting your filters or search criteria
            </p>
          </div>

          <ErrorAlert
            error="No records found"
            *ngIf="filteredData.length === 0 && !allActivityVisits.isSuccess()"
            color="blue"
          ></ErrorAlert>

          <!-- <LoadingSpinner
            [isLoading]="
              allActivityVisits.isLoading() || allActivityVisits.isPending()
            "
          ></LoadingSpinner> -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Lab Tests -->
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div
          class="card-header bg-primary text-white d-flex justify-content-between align-items-center"
        >
          <h5 class="card-title mb-0 text-white">
            <i class="fas fa-shopping-cart me-2"></i>
            Your Lab Test Purchase History
          </h5>
          <button
            class="btn btn-light btn-sm ms-2"
            (click)="loadMoreLabTests()"
          >
            View All
          </button>
        </div>
        <div class="card-body p-0">
          <!-- Filters Section -->
          <!-- <div class="row p-3 bg-light border-bottom">
            <div class="col-md-3">
              <label class="form-label">Status Filter</label>
              <select
                class="form-select form-select-sm"
                [(ngModel)]="statusFilter"
                (change)="filterData()"
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="Completed">Completed</option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">Package Filter</label>
              <select
                class="form-select form-select-sm"
                [(ngModel)]="packageFilter"
                (change)="filterData()"
              >
                <option value="">All Packages</option>
                <option *ngFor="let pkg of uniquePackages" [value]="pkg">
                  {{ pkg }}
                </option>
              </select>
            </div>
            <div class="col-md-4">
              <label class="form-label">Search</label>
              <input
                type="text"
                class="form-control form-control-sm"
                placeholder="Search by package name, patient name..."
                [(ngModel)]="searchText"
                (input)="filterData()"
              />
            </div>
            <div class="col-md-2 d-flex align-items-end">
              <button
                class="btn btn-outline-secondary btn-sm w-100"
                (click)="clearFilters()"
              >
                <i class="fas fa-times me-1"></i>Clear
              </button>
            </div>
          </div> -->

          <!-- Table Section -->
          <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
              <thead class="table-dark sticky-top">
                <tr>
                  <th scope="col" class="text-center">#</th>
                  <!-- <th scope="col">
                    Purchase ID
                    <i class="fas fa-sort ms-1"></i>
                  </th> -->
                  <th scope="col">
                    Purchase Date
                    <!-- <i class="fas fa-sort ms-1"></i> -->
                  </th>
                  <th scope="col">Package Details</th>
                  <!-- <th scope="col">Patient Info</th> -->
                  <th scope="col" class="text-center">Status</th>
                  <th scope="col" class="text-end">Pricing</th>
                  <th scope="col" class="text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="let item of filteredData; let i = index"
                  class="align-middle"
                >
                  <td class="text-center fw-bold">{{ i + 1 }}</td>

                  <!-- Purchase ID -->
                  <!-- <td>
                    <span class="badge bg-info text-dark">{{
                      item.purchaseId
                    }}</span>
                    <br />
                    <small class="text-muted"
                      >PPG-{{ item.purchasePackageId }}</small
                    >
                  </td> -->

                  <!-- Purchase Date -->
                  <td>
                    <div>{{ formatDate(item.purchaseDate) }}</div>
                    <small class="text-muted">{{
                      formatTime(item.purchaseDate)
                    }}</small>
                  </td>

                  <!-- Package Details -->
                  <td>
                    <div
                      class="fw-bold text-primary"
                      (click)="onPackageNameClick(item.packageId)"
                    >
                      {{ item.packageName.trim() }}
                    </div>
                    <!-- <small class="text-muted"
                      >Package ID: {{ item.packageId }}</small
                    > -->
                    <br />
                    <span class="badge bg-success"
                      >Qty: {{ item.quantity }}</span
                    >
                  </td>

                  <!-- Patient Info -->
                  <!-- <td>
                    <div class="fw-bold">{{ item.fullName }}</div>
                    <small class="text-muted d-block">{{
                      item.patientId
                    }}</small>
                    <small class="text-muted d-block">
                      <i class="fas fa-phone me-1"></i>{{ item.mobile }}
                    </small>
                    <small class="text-muted d-block">
                      <i class="fas fa-map-marker-alt me-1"></i
                      >{{ item.cityName }}
                    </small>
                  </td> -->

                  <!-- Status -->
                  <td class="text-center">
                    <span class="badge" [ngClass]="getStatusClass(item.status)">
                      {{ item.status | titlecase }}
                    </span>
                  </td>

                  <!-- Pricing -->
                  <td class="text-end">
                    <div class="pricing-info">
                      <div class="fw-bold text-success">₹{{ item.price }}</div>
                      <small class="text-muted text-decoration-line-through"
                        >₹{{ item.originalPrice }}</small
                      >
                      <br />
                      <span class="badge bg-warning text-dark"
                        >{{ item.discount }}% OFF</span
                      >
                      <br />
                      <!-- <small class="text-muted">GST: {{ item.gst }}%</small> -->
                    </div>
                  </td>

                  <!-- Actions -->
                  <td class="text-center">
                    <div class="btn-group" role="group">
                      <button
                        class="btn btn-outline-primary btn-sm"
                        (click)="onViewReportsClick(item)"
                        ngbTooltip="View Report"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                      <!-- <button
                        class="btn btn-outline-success btn-sm"
                        (click)="downloadInvoice(item)"
                        title="Download Invoice"
                      >
                        <i class="fas fa-download"></i>
                      </button>
                      <button
                        class="btn btn-outline-info btn-sm"
                        *ngIf="item.status === 'pending'"
                        (click)="updateStatus(item)"
                        title="Update Status"
                      >
                        <i class="fas fa-edit"></i>
                      </button> -->
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- No Data State -->
          <div
            *ngIf="
              filteredData.length === 0 &&
              purchasedPackages.isSuccess() &&
              purchasedPackages.data().length === 0
            "
            class="text-center py-5"
          >
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No records found</h5>
            <p class="text-muted">
              Try adjusting your filters or search criteria
            </p>
          </div>

          <ErrorAlert
            error="No records found"
            *ngIf="filteredData.length === 0 && !purchasedPackages.isSuccess()"
            color="blue"
          ></ErrorAlert>

          <!-- <LoadingSpinner
            [isLoading]="
              purchasedPackages.isLoading() || purchasedPackages.isPending()
            "
          ></LoadingSpinner> -->
        </div>
      </div>
    </div>
  </div>
</div>
