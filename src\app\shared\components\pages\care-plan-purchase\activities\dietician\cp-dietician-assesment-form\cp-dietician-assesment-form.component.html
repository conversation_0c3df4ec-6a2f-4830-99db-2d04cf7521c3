<main [class.readonly-mode]="this.user.roleName.includes(util.ROLES.PATIENT)">
  <header class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h3 class="mb-0 text-primary">
        <i class="fas fa-apple-alt text-warning me-3 mt-1"></i>
        Dietician Consultation
      </h3>
    </div>

    <div class="d-flex gap-3 align-items-center">
      <div class="form-check">
        <input
          type="checkbox"
          class="form-check-input border border-primary"
          id="markCompleted"
          [(ngModel)]="isCompleted"
          (change)="onCompletionChange()"
        />
        <label
          class="form-check-label fw-semibold text-dark ms-1"
          for="markCompleted"
        >
          Mark as Completed
        </label>
      </div>

      <!-- Back Button -->
      <button class="btn btn-primary rounded px-4" (click)="back()">
        Back
      </button>
    </div>
  </header>

  <hr />
  <section>
    <nav class="settings-tab mb-1">
      <ul class="nav nav-tabs-bottom" role="tablist">
        <!-- <li class="nav-item" role="presentation">
          <a
            class="nav-link"
            [class.active]="isConsultationActive"
            (click)="setActiveTab('consultation')"
            >Patient Consultation</a
          >
        </li> -->
        <!-- With Care Plan Forms -->
        <ng-container *ngIf="formType == 'Care'">
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              [class.active]="isAnthropometricActive"
              (click)="setActiveTab('anthropometric')"
              [class.disabled]="!consultationId"
              [attr.aria-disabled]="!consultationId ? true : null"
              [attr.tabindex]="!consultationId ? -1 : 0"
              >Anthropometric</a
            >
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              [class.active]="isDietaryAssesmentActive"
              (click)="setActiveTab('dietaryAssesment')"
              [class.disabled]="!consultationId"
              [attr.aria-disabled]="!consultationId ? true : null"
              [attr.tabindex]="!consultationId ? -1 : 0"
              >Dietary Assesment</a
            >
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              [class.active]="isLifeStyleActive"
              (click)="setActiveTab('lifestyle')"
              [class.disabled]="!consultationId"
              [attr.aria-disabled]="!consultationId ? true : null"
              [attr.tabindex]="!consultationId ? -1 : 0"
              >LifeStyle</a
            >
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              [class.active]="isDiagnosisActive"
              (click)="setActiveTab('diagnosisPlan')"
              [class.disabled]="!consultationId"
              [attr.aria-disabled]="!consultationId ? true : null"
              [attr.tabindex]="!consultationId ? -1 : 0"
              >Nutrition Diagnosis</a
            >
          </li>
        </ng-container>
        <!-- Without Care Plan Forms -->
        <ng-container *ngIf="formType == 'Without-Care'">
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              [class.active]="isCarePlanActive"
              (click)="setActiveTab('carePlan')"
              [class.disabled]="!consultationId"
              [attr.aria-disabled]="!consultationId ? true : null"
              [attr.tabindex]="!consultationId ? -1 : 0"
              >Nutrition Care Plan</a
            >
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              [class.active]="isDietPlanActive"
              (click)="setActiveTab('dietPlan')"
              [class.disabled]="!consultationId"
              [attr.aria-disabled]="!consultationId ? true : null"
              [attr.tabindex]="!consultationId ? -1 : 0"
              >Diet Plan</a
            >
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              [class.active]="isFollowupActive"
              (click)="setActiveTab('followup')"
              [class.disabled]="!consultationId"
              [attr.aria-disabled]="!consultationId ? true : null"
              [attr.tabindex]="!consultationId ? -1 : 0"
              >Follow-Up</a
            >
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              [class.active]="isAttachmentsActive"
              (click)="setActiveTab('attachments')"
              [class.disabled]="!consultationId"
              [attr.aria-disabled]="!consultationId ? true : null"
              [attr.tabindex]="!consultationId ? -1 : 0"
              >Attachments & Resources</a
            >
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              [class.active]="isConsentRemarkActive"
              (click)="setActiveTab('consent-remark')"
              [class.disabled]="!consultationId"
              [attr.aria-disabled]="!consultationId ? true : null"
              [attr.tabindex]="!consultationId ? -1 : 0"
              >Consent & Remarks</a
            >
          </li>
          <li class="nav-item" role="presentation">
            <a
              class="nav-link"
              [class.active]="isPatientFeedbackActive"
              (click)="setActiveTab('patientFeedback')"
              [class.disabled]="!consultationId"
              [attr.aria-disabled]="!consultationId ? true : null"
              [attr.tabindex]="!consultationId ? -1 : 0"
              >Patient Feedback</a
            >
          </li>
        </ng-container>
      </ul>
    </nav>

    <div class="">
      <!-- <div *ngIf="isConsultationActive" class="mt-2">
        <diet-conusltation-form
          [patientId]="patientId"
          [carePlanId]="carePlanId"
          [monthNumber]="monthNumber"
          [consultationIdFromParent]="consultationId"
          [isUpdate]="isUpdate"
          (consultationId)="onChangeConsultationId($event)"
        />
      </div> -->
      <div *ngIf="isAnthropometricActive" class="mt-2">
        <app-cp-diet-anthropometric-form
          [patientId]="patientId"
          [carePlanId]="carePlanId"
          [monthNumber]="monthNumber"
          [consultationId]="consultationId"
          [appointmentId]="appointmentId"
          [isUpdate]="isUpdate"
          [isCompleted]="isCompleted"
        />
      </div>
      <div *ngIf="isDietaryAssesmentActive">
        <app-cp-diet-dietary-form
          [patientId]="patientId"
          [carePlanId]="carePlanId"
          [monthNumber]="monthNumber"
          [consultationId]="consultationId"
          [appointmentId]="appointmentId"
          [isUpdate]="isUpdate"
          [isCompleted]="isCompleted"
        />
      </div>
      <div *ngIf="isLifeStyleActive">
        <app-cp-diet-lifestyle-form
          [patientId]="patientId"
          [carePlanId]="carePlanId"
          [monthNumber]="monthNumber"
          [consultationId]="consultationId"
          [appointmentId]="appointmentId"
          [isUpdate]="isUpdate"
          [isCompleted]="isCompleted"
        />
      </div>
      <div *ngIf="isDiagnosisActive">
        <app-cp-diet-diagnosis-form
          [patientId]="patientId"
          [carePlanId]="carePlanId"
          [monthNumber]="monthNumber"
          [consultationId]="consultationId"
          [isUpdate]="isUpdate"
          [appointmentId]="appointmentId"
          [isCompleted]="isCompleted"
        />
      </div>
      <div *ngIf="isCarePlanActive">
        <app-cp-diet-nutrition-care-plan
          [patientId]="patientId"
          [carePlanId]="carePlanId"
          [monthNumber]="monthNumber"
          [consultationId]="consultationId"
          [isUpdate]="isUpdate"
          [appointmentId]="appointmentId"
          [isCompleted]="isCompleted"
        />
      </div>
      <div *ngIf="isDietPlanActive">
        <app-cp-diet-plan
          [patientId]="patientId"
          [consultationId]="consultationId"
          [isUpdate]="isUpdate"
          [appointmentId]="appointmentId"
          [isCompleted]="isCompleted"
        />
      </div>
      <div *ngIf="isFollowupActive">
        <app-cp-diet-follow-form
          [patientId]="patientId"
          [carePlanId]="carePlanId"
          [monthNumber]="monthNumber"
          [consultationId]="consultationId"
          [isUpdate]="isUpdate"
          [appointmentId]="appointmentId"
          [isCompleted]="isCompleted"
        />
      </div>
      <div *ngIf="isAttachmentsActive">
        <attachements-resources
          [patientId]="patientId"
          [consultationId]="consultationId"
          [appointmentId]="appointmentId"
          [isUpdate]="isUpdate"
          [isCompleted]="isCompleted"
        />
      </div>
      <div *ngIf="isConsentRemarkActive">
        <app-cp-diet-consent-remark
          [patientId]="patientId"
          [consultationId]="consultationId"
          [isUpdate]="isUpdate"
          [appointmentId]="appointmentId"
          [isCompleted]="isCompleted"
        />
      </div>
      <div *ngIf="isPatientFeedbackActive">
        <app-cp-diet-patient-feedback
          [patientId]="patientId"
          [consultationId]="consultationId"
          [isUpdate]="isUpdate"
          [appointmentId]="appointmentId"
          [isCompleted]="isCompleted"
        />
      </div>
    </div>
  </section>
</main>
