import { Component, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { endOfWeek, format, startOfWeek } from 'date-fns';
import moment from 'moment';
import Swal from 'sweetalert2';
import { FacilityService } from '../../../facility/facility.service';
import { DoctorService } from '../../../doctor/services/doctor.service';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { AdminManagerService } from '../../../admin-manager/services/admin-manager.service';
import { CommonModule } from '@angular/common';
import { SearchableSelectComponent } from 'src/app/shared/searchable-select/searchable-select.component';
import { PageWithSearchPaginationComponent } from 'src/app/shared/page-with-search-pagination/page-with-search-pagination.component';
import { LoadingSpinnerComponent } from 'src/app/shared/loading-spinner/loading-spinner.component';
import { ErrorAlertComponent } from 'src/app/shared/error-alert/error-alert.component';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { SubmitBtnComponent } from 'src/app/shared/components/re-use/submit-btn/submit-btn.component';
import { AdminFriendlyPageWrapperComponent } from 'src/app/shared/components/re-use/admin-friendly-page-wrapper/admin-friendly-page-wrapper.component';

enum ViewMode {
  Day = 'day',
  Week = 'week',
  Month = 'month',
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  shifts: Shift[];
}

interface Shift {
  id: any;
  title: string;
  date: string; // ISO date format e.g., '2025-04-17'
  color?: string;
  startTime?: any;
  endTime?: any;
  appointmentType?: any;
  appointmentDateId?: any;
  doctorId: number;
  doctorName?: string;
  facilityId: number;
  departmentId: number;
}

@Component({
  selector: 'app-availability',
  imports: [
    CommonModule,
    SearchableSelectComponent,
    ReactiveFormsModule,
    PageWithSearchPaginationComponent,
    LoadingSpinnerComponent,
    ErrorAlertComponent,
    BsDatepickerModule,
    SubmitBtnComponent,
    AdminFriendlyPageWrapperComponent,
  ],
  templateUrl: './availability.component.html',
  styleUrl: './availability.component.scss',
})
export class AvailabilityComponent implements OnInit {
  user: any;

  currentDate: Date = new Date();
  selectedDate: Date = new Date();
  viewMode: ViewMode = ViewMode.Week;
  viewModes = ViewMode;

  weekDays: string[] = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  calendarDays: CalendarDay[] = [];

  weekViewDays: CalendarDay[] = [];
  dayViewHours: { hour: number; shifts: Shift[] }[] = [];

  shifts: Shift[] = [];

  // Hover
  hoveredWeekIndex: number = -1;
  hoveredWeekUserIndex: number = -1;
  hoveredDayDoctorIndex: number = -1;
  dayAddBtn: boolean = false;

  // Doctors
  doctors: any[] = [];
  filteredDoctors: any[] = [];
  filteredDoctorsList = signal<any[]>([]);
  searchQuery: string = '';

  // Shifts
  allShifts: Shift[] = [];
  newShifts: Shift[] = [];
  timeSlots = signal<any[]>([]);

  // Form
  scheduleForm!: FormGroup;
  scheduleMonthForm!: FormGroup;

  showModal: boolean = false;
  showMonthModal: boolean = false;
  viewEvent: boolean = false;

  currentWeekDays = signal<string[]>([]);
  selectedDays = signal<string[]>([]);

  clickedDoctor: any;
  clickedDate: any;
  clickedEvent: any;

  doctorsList = signal<any[]>([]);

  currentView: 'day' | 'month' | 'week' = 'day';

  currentFacility: any;

  isPageLoading = signal<boolean>(true);
  isPublishing = signal<boolean>(false);
  isSaving = signal<boolean>(false);
  isUpdating = signal<boolean>(false);
  isDeleting = signal<boolean>(false);

  appointmentTypes: any[] = [
    {
      name: 'Clinic Visit',
      value: 'clinic',
    },
    {
      name: 'Online Consultation',
      value: 'online',
    },
    {
      name: 'Nurse Home Visit',
      value: 'nurse',
    },
    {
      name: 'Hospital Visit',
      value: 'hospital',
    },
    {
      name: 'Blood Sample Collection ',
      value: 'sample',
    },
  ];

  constructor(
    private amService: AdminManagerService,
    private authService: AuthService,
    private toastr: ToastrService,
    private fb: FormBuilder,
    private doctorService: DoctorService,
    private facilityService: FacilityService
  ) {
    this.user = this.authService.getDataFromSession('user');
    this.buildForm();
    this.buildMonthForm();
    this.timeSlots.set(this.generateTimeSlots());
    this.getUsersByDept('ROLE_DOCTOR');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  ngOnInit(): void {
    this.changeView(this.viewMode);
    // this.getUsersByDept('ROLE_DOCTOR');
    this.getAllShifts(this.user.facilityId);
    this.getFacilityData();
  }

  async getFacilityData() {
    try {
      const res: any = await this.facilityService.getFacilityById(
        this.user.facilityId
      );
      this.currentFacility = res[0];
    } catch (error) {
      console.log('--Error while fetching facility details--', error);
      this.toastr.error('Error while fetching facility details');
      return;
    }
  }

  onDayChange(event: any) {
    this.selectedDate = event;
    console.log('Selected Day:', event);
    this.generateDayView();
    // Emit or load day view here
  }
  onMonthChange(event: any) {
    // this.selectedMonth = event.target.value;
    console.log('Selected Month:', event);
    this.selectedDate = event;
    this.generateMonthView();
    // Emit or load month view here
  }

  // onWeekChange(date: Date): void {
  //   const weekStart = startOfWeek(date, { weekStartsOn: 1 }); // Monday
  //   const weekEnd = endOfWeek(date, { weekStartsOn: 1 }); // Sunday

  //   // console.log(weekStart);
  //   // console.log(weekEnd);

  //   this.selectedDate = date;
  //   this.generateWeekView();
  // }

  onWeekChange(date: Date): void {
    this.selectedDate = date;
    this.generateWeekView();
  }

  // Build form
  buildForm = () => {
    this.scheduleForm = this.fb.group({
      date: [''],
      startTime: ['', Validators.required],
      endTime: ['', Validators.required],
      appointmentType: ['clinic', Validators.required],
      color: ['#6d24b2'],
    });
  };
  buildMonthForm = () => {
    this.scheduleMonthForm = this.fb.group({
      date: [''],
      doctorId: ['', Validators.required],
      startTime: ['', Validators.required],
      endTime: ['', Validators.required],
      appointmentType: ['clinic', Validators.required],
      color: ['#6d24b2'],
    });
  };

  changeView(mode: ViewMode): void {
    this.viewMode = mode;

    switch (mode) {
      case ViewMode.Day:
        this.generateDayView();
        break;
      case ViewMode.Week:
        this.generateWeekView();
        break;
      case ViewMode.Month:
        this.generateMonthView();
        break;
    }
  }

  generateDayView(): void {
    this.dayViewHours = [];

    // Generate hours for the day
    for (let i = 0; i < 24; i++) {
      const hourStart = new Date(this.selectedDate);
      hourStart.setHours(i, 0, 0, 0);

      const hourEnd = new Date(this.selectedDate);
      hourEnd.setHours(i, 59, 59, 999);

      // Find shifts that overlap with this hour
      const hourShifts = this.shifts.filter((shift) => {
        return shift.startTime <= hourEnd && shift.endTime >= hourStart;
      });

      this.dayViewHours.push({
        hour: i,
        shifts: hourShifts,
      });
    }
  }

  generateWeekView1(): void {
    this.weekViewDays = [];

    // Start from Sunday of the current week
    const startOfWeek = new Date(this.selectedDate);
    startOfWeek.setDate(
      this.selectedDate.getDate() - this.selectedDate.getDay()
    );

    const day = this.selectedDate.getDay();
    const diff = this.selectedDate.getDate() - day + (day === 0 ? -6 : 1); // adjust when Sunday (0)
    startOfWeek.setDate(diff);

    // Generate the 7 days of the week
    for (let i = 0; i < 7; i++) {
      const currentDay = new Date(startOfWeek);
      currentDay.setDate(startOfWeek.getDate() + i);

      // Start and end of day
      const dayStart = new Date(currentDay);
      dayStart.setHours(0, 0, 0, 0);

      const dayEnd = new Date(currentDay);
      dayEnd.setHours(23, 59, 59, 999);

      // Find shifts for this day
      const dayShifts = this.shifts.filter((shift) => {
        return shift.startTime <= dayEnd && shift.endTime >= dayStart;
      });

      this.weekViewDays.push({
        date: new Date(currentDay),
        isCurrentMonth: currentDay.getMonth() === this.selectedDate.getMonth(),
        isToday: this.isToday(currentDay),
        shifts: dayShifts,
      });
    }
  }

  generateWeekView(): void {
    this.weekViewDays = [];

    // Get proper week boundaries (Mon → Sun)
    const weekStart = startOfWeek(this.selectedDate, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(this.selectedDate, { weekStartsOn: 1 });

    // Loop through each day of the week
    for (let i = 0; i < 7; i++) {
      const currentDay = new Date(weekStart);
      currentDay.setDate(weekStart.getDate() + i);

      const dayStart = new Date(currentDay);
      dayStart.setHours(0, 0, 0, 0);

      const dayEnd = new Date(currentDay);
      dayEnd.setHours(23, 59, 59, 999);

      const dayShifts = this.shifts.filter(
        (shift) => shift.startTime <= dayEnd && shift.endTime >= dayStart
      );

      this.weekViewDays.push({
        date: currentDay,
        isCurrentMonth: currentDay.getMonth() === this.selectedDate.getMonth(),
        isToday: this.isToday(currentDay),
        shifts: dayShifts,
      });
    }
  }

  generateMonthView(): void {
    this.calendarDays = [];

    // Get first day of the month
    const firstDayOfMonth = new Date(
      this.selectedDate.getFullYear(),
      this.selectedDate.getMonth(),
      1
    );
    // Get last day of the month
    const lastDayOfMonth = new Date(
      this.selectedDate.getFullYear(),
      this.selectedDate.getMonth() + 1,
      0
    );

    // Get the first day to display (might be from previous month)
    const firstCalendarDay = new Date(firstDayOfMonth);
    firstCalendarDay.setDate(
      firstCalendarDay.getDate() - firstCalendarDay.getDay()
    );

    // Get the last day to display (might be from next month)
    const lastCalendarDay = new Date(lastDayOfMonth);
    const daysToAdd = 6 - lastCalendarDay.getDay();
    lastCalendarDay.setDate(lastCalendarDay.getDate() + daysToAdd);

    // Generate calendar days
    const currentDay = new Date(firstCalendarDay);
    while (currentDay <= lastCalendarDay) {
      // Start and end of day
      const dayStart = new Date(currentDay);
      dayStart.setHours(0, 0, 0, 0);

      const dayEnd = new Date(currentDay);
      dayEnd.setHours(23, 59, 59, 999);

      // Find shifts for this day
      const dayShifts = this.shifts.filter((shift) => {
        return shift.startTime <= dayEnd && shift.endTime >= dayStart;
      });

      this.calendarDays.push({
        date: new Date(currentDay),
        isCurrentMonth: currentDay.getMonth() === this.selectedDate.getMonth(),
        isToday: this.isToday(currentDay),
        shifts: dayShifts,
      });

      currentDay.setDate(currentDay.getDate() + 1);
    }
  }

  async getUsersByDept(roleName: string) {
    try {
      this.isPageLoading.set(true);
      const availableDate = moment(new Date()).format('YYYY-MM-DD');
      const todayDateTime = moment(new Date()).format('YYYY-MM-DD HH:mm');

      const params = {
        availableDate,
        todayDateTime,
      };

      let res: any;
      if (
        this.user.roleName.includes('ROLE_HEALTH_ADVISOR') ||
        this.user.roleName.includes('ROLE_EMR_SUPER_ADMIN')
      ) {
        res = await this.doctorService.getAllDoctorDetailsAcrossFacilities(
          params
        );
      } else {
        res = await this.doctorService.getAllDoctorDetailsByFacility({
          ...params,
          facilityId: this.user.facilityId,
          nurseId: this.user.userId,
        });
      }
      console.log('--Doctor  Details Res : ', res);
      this.doctors = res.map((u: any) => {
        return {
          ...u,
          userId: u.id,
        };
      });
      // this.doctorsList.set(
      //   res.map((d: any) => {
      //     return {
      //       id: d.userId,
      //       name: d.firstName,
      //     };
      //   })
      // );
      // console.log('--Docs: ', this.doctorsList());
      this.applyFilters();
    } catch (error) {
      console.log('--Error while getting doctors details : ', error);
      this.toastr.error('While getting doctors details', 'Error');
    } finally {
      this.isPageLoading.set(false);
    }
  }

  applyFilters() {
    let filtered = [...this.doctors];

    // Search filter
    if (this.searchQuery.trim()) {
      const lowerSearch = this.searchQuery.toLowerCase();
      filtered = filtered.filter((facility: any) =>
        Object.values(facility).some(
          (value: any) =>
            value && value.toString().toLowerCase().includes(lowerSearch)
        )
      );
    }

    // Update the total count for pagination
    // this.totalFilteredCount = filtered.length;

    // Now apply pagination
    // const start = (this.currentPage - 1) * this.itemsPerPage;
    // const end = start + Number(this.itemsPerPage);

    // console.log('--start: ', start);
    // console.log('--end: ', end);

    // this.filteredDeptUsers = filtered.slice(start, end);
    this.filteredDoctors = filtered;
  }

  // Shifts

  // Modal
  onClose = () => {
    this.scheduleForm.reset();
    this.scheduleForm.patchValue({
      appointmentType: 'clinic',
      color: '#6d24b2',
    });
    this.showModal = false;
    this.clickedDate = null;
    this.clickedDoctor = null;
    this.currentWeekDays.set([]);
    this.viewEvent = false;
  };

  onDayClick = (day: string) => {
    const existingDay = this.selectedDays().find((d: string) => d == day);
    if (existingDay) {
      this.selectedDays.set(
        this.selectedDays().filter((d: string) => d != day)
      );
    } else {
      this.selectedDays.set([...this.selectedDays(), day]);
    }
  };

  onDateClick(doctor: any, date: any) {
    this.showModal = true;
    this.clickedDoctor = doctor;
    this.clickedDate = date;

    console.log('Doc : ', doctor);
    console.log('Date : ', date);

    // Get week days
    const clickedDate = moment(date);
    const startOfWeek = clickedDate.clone().startOf('week');
    const weekDates: string[] = [];
    for (let i = 0; i < 7; i++) {
      weekDates.push(startOfWeek.clone().add(i, 'days').format('YYYY-MM-DD'));
    }
    console.log('Week Dates:', weekDates);
    this.currentWeekDays.set(weekDates);
    this.selectedDays.set([clickedDate.format('YYYY-MM-DD')]);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  onEventClick(doctor: any, date: any, event: any) {
    this.showModal = true;
    this.viewEvent = true;
    this.clickedDoctor = doctor;
    this.clickedDate = date;
    this.clickedEvent = event;

    this.scheduleForm.patchValue({
      date: event.date,
      startTime: event.startTime,
      endTime: event.endTime,
      color: event.color,
      appointmentType: event.appointmentType,
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Form Submission
  onFormSubmit = () => {
    if (!this.selectedDays().length) {
      this.toastr.error('Please select atleast one day');
      return;
    }

    if (this.scheduleForm.invalid) {
      this.toastr.error('Please fill all the fields');
      this.scheduleForm.markAllAsTouched();
      return;
    }

    console.log('All Shifts : ', this.allShifts);

    const start = moment(this.startTime?.value, 'HH:mm');
    const end = moment(this.endTime?.value, 'HH:mm');

    if (end.isBefore(start)) {
      console.log('End time cannot be before start time');
      this.toastr.error('End time cannot be before start time');
      return;
    }

    for (const selectedDate of this.selectedDays()) {
      const formattedDate = moment(new Date(selectedDate)).format('YYYY-MM-DD');
      const doctorId = this.clickedDoctor.userId;

      console.log('Selected Date : ', formattedDate);
      console.log('Doctor Id : ', doctorId);

      const hasConflict = this.allShifts.some((shift) => {
        return (
          shift.date === formattedDate &&
          shift.doctorId === doctorId &&
          moment(shift.startTime, 'HH:mm').isBefore(end) &&
          moment(shift.endTime, 'HH:mm').isAfter(start)
        );
      });

      if (hasConflict) {
        // this.toastr.error(`You have shift conflicts, please correct it`);
        Swal.fire('You have shift conflicts, please correct it', '', 'error');
        return;
      }
    }

    const payloads = this.selectedDays().map((d: any) => {
      return {
        id: crypto.randomUUID(),
        title: `${this.convertTo12HourFormat(
          this.startTime?.value
        )} - ${this.convertTo12HourFormat(this.endTime?.value)}`,
        date: moment(new Date(d)).format('YYYY-MM-DD'),
        color: this.color?.value,
        startTime: this.startTime?.value,
        endTime: this.endTime?.value,
        appointmentType: this.appointmentType?.value,
        appointmentDateId: null,
        doctorId: this.clickedDoctor.userId,
        facilityId: this.clickedDoctor.facilityId,
        departmentId: this.clickedDoctor.departmentId,
      };
    });

    // const payload = {
    //   id: crypto.randomUUID(),
    //   title: `${this.convertTo12HourFormat(
    //     this.startTime?.value
    //   )} - ${this.convertTo12HourFormat(this.endTime?.value)}`,
    //   date: moment(this.date?.value).format('YYYY-MM-DD'),
    //   color: this.color?.value,
    //   startTime: this.startTime?.value,
    //   endTime: this.endTime?.value,
    //   appointmentType: this.appointmentType?.value,
    // };

    console.log('--Submit form : ', ...payloads);

    this.newShifts = [...this.newShifts, ...payloads];
    this.allShifts = [...this.allShifts, ...payloads];

    console.log('After created shist --> all shifts: ', this.allShifts);

    this.scheduleForm.reset();
    this.scheduleForm.patchValue({
      appointmentType: 'clinic',
      color: '#6d24b2',
    });
    this.showModal = false;
  };

  // Shifts
  getAllShifts = async (fId: any) => {
    try {
      const res: any = await this.doctorService.getAllShiftsAcrossFacility();
      // console.log('--Old Shifts response : ', res);
      const shifts: any[] = res.map((s: any) => {
        return {
          id: s.shiftId,
          title: `${this.convertTo12HourFormat(
            s.shiftStart
          )} - ${this.convertTo12HourFormat(s.shiftEnd)}`,
          date: s.availableDate,
          color: s.color ? s.color : '#0e82fd',
          startTime: s.shiftStart,
          endTime: s.shiftEnd,
          appointmentType: s.appointmentType,
          appointmentDateId: s.availabilityDateId,
          doctorId: s.doctorId,
          doctorName: s.doctorName,
          facilityId: s.facilityId,
          departmentId: s.departmentId,
        };
      });
      this.allShifts = [...this.newShifts, ...shifts];
    } catch (error) {
      console.log('--Error while getting all shifts : ', error);
      this.toastr.error('Error while getting all shifts');
    }
  };

  createNewShifts = async () => {
    this.isPublishing.set(true);
    if (!this.newShifts.length) {
      this.toastr.error('No new shifts available');
      this.isPublishing.set(false);
      return;
    }

    const allDates: string[] = Array.from(
      new Set(this.newShifts.map((s: any) => s.date))
    );

    console.log('All Dates:', allDates);
    console.log('All New Shifts:', this.newShifts);

    const payloads: any[] = [];

    allDates.forEach((date: string) => {
      // Get unique combinations of doctorId, departmentId, facilityId for the date
      const uniqueCombos = [
        ...new Set(
          this.newShifts
            .filter((shift: any) => shift.date === date)
            .map(
              (shift: any) =>
                `${shift.doctorId}_${shift.departmentId}_${shift.facilityId}`
            )
        ),
      ];

      uniqueCombos.forEach((combo: string) => {
        const [doctorIdStr, departmentIdStr, facilityIdStr] = combo.split('_');
        const doctorId = Number(doctorIdStr);
        const departmentId = Number(departmentIdStr);
        const facilityId = Number(facilityIdStr);

        const shifts = this.newShifts
          .filter(
            (s: any) =>
              s.date == date &&
              s.doctorId == doctorId &&
              s.departmentId == departmentId &&
              s.facilityId == facilityId
          )
          .map((s: any) => ({
            shiftStart: s.startTime,
            shiftEnd: s.endTime,
            appointmentType: s.appointmentType,
          }));

        payloads.push({
          doctorId,
          departmentId,
          facilityId,
          date,
          createdDate: new Date().toISOString(),
          shifts,
        });
      });
    });

    // const payload = {
    //   doctorId: this.user().userId,
    //   date: moment(this.date?.value).format('YYYY-MM-DD'),
    //   createdDate: new Date().toISOString(),
    //   shifts: this.newShifts.map((s: any) => {
    //     return {
    //       shiftStart: s.startTime,
    //       shiftEnd: s.endTime,
    //       appointmentType: s.appointmentType,
    //     };
    //   }),
    // };

    console.log('--New Shifts : ', payloads);

    payloads.forEach(async (payload: any, index: any) => {
      try {
        const res: any = await this.doctorService.addNewShift(payload);
        console.log('--New Shifts response : ', res);

        if (payloads.length == index + 1) {
          this.toastr.success('Shifts created successfully');
          this.newShifts = [];
          this.ngOnInit();
        }
      } catch (error) {
        console.log('--Error while publishing shifts : ', error);
        this.toastr.error('Error while publishing shifts');
      } finally {
        this.isPublishing.set(false);
      }
    });
  };

  updateNewShifts = async (shift: any) => {
    this.isUpdating.set(true);
    const start = moment(this.startTime?.value, 'HH:mm');
    const end = moment(this.endTime?.value, 'HH:mm');

    if (end.isBefore(start)) {
      console.log('End time cannot be before start time');
      this.toastr.error('End time cannot be before start time');
      this.isUpdating.set(false);
      return;
    }

    const formattedDate = moment(this.date?.value).format('YYYY-MM-DD');
    const doctorId = this.clickedDoctor?.userId;

    // 🔍 Conflict check
    const hasConflict = this.allShifts.some((existing) => {
      return (
        existing.id !== shift.id && // Exclude current shift being edited
        existing.date === formattedDate &&
        existing.doctorId === doctorId &&
        moment(existing.startTime, 'HH:mm').isBefore(end) &&
        moment(existing.endTime, 'HH:mm').isAfter(start)
      );
    });

    if (hasConflict) {
      // this.toastr.error(`You have shift conflicts, please correct it`);
      Swal.fire('You have shift conflicts, please correct it', '', 'error');
      this.isUpdating.set(false);
      return;
    }

    if (this.isValidUUID(shift.id)) {
      const payload = {
        id: shift.id,
        title: `${this.convertTo12HourFormat(
          this.startTime?.value
        )} - ${this.convertTo12HourFormat(this.endTime?.value)}`,
        date: moment(this.date?.value).format('YYYY-MM-DD'),
        color: this.color?.value,
        startTime: this.startTime?.value,
        endTime: this.endTime?.value,
        appointmentType: this.appointmentType?.value,
        doctorId: this.clickedDoctor.userId,
        departmentId: this.clickedDoctor.departmentId,
        facilityId: this.clickedDoctor.facilityId,
      };

      console.log('--Update : ', payload);

      this.newShifts = this.newShifts.map((s: any) =>
        s.id == shift.id ? payload : s
      );
      this.allShifts = this.allShifts.map((s: any) =>
        s.id == shift.id ? payload : s
      );
      this.toastr.success('Shift updated successfully');
      this.showModal = false;
      this.viewEvent = false;
      this.clickedDoctor = null;
      this.clickedEvent = null;
      this.scheduleForm.reset();
      this.scheduleForm.patchValue({
        appointmentType: 'clinic',
        color: '#6d24b2',
      });
      this.isUpdating.set(false);
      return;
    }

    const payload = {
      id: shift.appointmentDateId,
      doctorId: this.clickedDoctor.userId,
      shiftId: shift.id,
      appointmentType: this.appointmentType?.value,
      shiftStart: this.startTime?.value,
      shiftEnd: this.endTime?.value,
      createdDate: new Date().toISOString(),
      updatedDate: new Date().toISOString(),
    };

    console.log('--Update : ', payload);

    try {
      const res: any = await this.doctorService.updateShift(payload);
      console.log('--Shift update response : ', res);
      this.toastr.success('Shift updated successfully');
      this.showModal = false;
      this.viewEvent = false;
      this.scheduleForm.reset();
      this.clickedDoctor = null;
      this.clickedEvent = null;
      this.scheduleForm.patchValue({
        appointmentType: 'clinic',
        color: '#6d24b2',
      });
      this.ngOnInit();
    } catch (error) {
      console.log('--Error while update shifts : ', error);
      this.toastr.error('Error while update shifts');
    } finally {
      this.isUpdating.set(false);
    }
  };

  deleteNewShifts = async (shiftId: any) => {
    this.isDeleting.set(true);
    if (this.isValidUUID(shiftId)) {
      this.newShifts = this.newShifts.filter((s: any) => s.id !== shiftId);
      this.allShifts = this.allShifts.filter((s: any) => s.id !== shiftId);
      this.toastr.success('Shift deleted successfully');
      this.showModal = false;
      this.viewEvent = false;
      this.scheduleForm.reset();
      this.clickedDoctor = null;
      this.clickedEvent = null;
      this.scheduleForm.patchValue({
        appointmentType: 'clinic',
        color: '#6d24b2',
      });
      this.isDeleting.set(false);
      return;
    }

    try {
      const res: any = await this.doctorService.deleteShift({
        shiftId: shiftId,
      });
      console.log('--Shift delete response : ', res);
      this.toastr.success('Shift deleted successfully');
      this.showModal = false;
      this.viewEvent = false;
      this.scheduleForm.reset();
      this.clickedDoctor = null;
      this.clickedEvent = null;
      this.scheduleForm.patchValue({
        appointmentType: 'clinic',
        color: '#6d24b2',
      });
      this.ngOnInit();
    } catch (error) {
      console.log('--Error while deleting shifts : ', error);
      this.toastr.error('Error while deleting shifts');
    } finally {
      this.isDeleting.set(false);
    }
  };

  // For month View
  addShiftViaMonthView(date: any) {
    this.showMonthModal = true;

    this.doctorsList.set(
      this.doctors.map((d: any) => {
        return {
          id: d.userId,
          name: d.firstName,
        };
      })
    );

    this.dateM.setValue(new Date(date));

    // Get week days
    const clickedDate = moment(date);
    const startOfWeek = clickedDate.clone().startOf('week');
    const weekDates: string[] = [];
    for (let i = 0; i < 7; i++) {
      weekDates.push(startOfWeek.clone().add(i, 'days').format('YYYY-MM-DD'));
    }
    console.log('Week Dates:', weekDates);
    this.currentWeekDays.set(weekDates);
    this.selectedDays.set([clickedDate.format('YYYY-MM-DD')]);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  onMonthDateClicked() {}

  onMonthEventClicked(doctorId: any, date: any, event: any) {
    this.showMonthModal = true;
    this.viewEvent = true;
    this.clickedDoctor = this.doctors.find((d: any) => d.userId === doctorId);
    this.clickedDate = date;
    this.clickedEvent = event;

    this.scheduleMonthForm.patchValue({
      date: event.date,
      startTime: event.startTime,
      endTime: event.endTime,
      color: event.color,
      appointmentType: event.appointmentType,
      doctorId: doctorId,
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  onMonthClose() {
    this.showMonthModal = false;
    this.viewEvent = false;
    this.scheduleMonthForm.reset();
    this.scheduleMonthForm.patchValue({
      appointmentType: 'clinic',
      color: '#6d24b2',
    });
    this.clickedDate = null;
    this.clickedDoctor = null;
    this.currentWeekDays.set([]);
  }

  onMonthFormSubmited() {
    if (!this.selectedDays().length) {
      this.toastr.error('Please select atleast one day');
      return;
    }

    if (this.scheduleMonthForm.invalid) {
      this.toastr.error('Please fill all the fields');
      this.scheduleMonthForm.markAllAsTouched();
      return;
    }

    console.log('ALl Shifts : ', this.allShifts);

    const start = moment(this.startTimeM?.value, 'HH:mm');
    const end = moment(this.endTimeM?.value, 'HH:mm');

    if (end.isBefore(start)) {
      console.log('End time cannot be before start time');
      this.toastr.error('End time cannot be before start time');
      return;
    }

    for (const selectedDate of this.selectedDays()) {
      const formattedDate = moment(new Date(selectedDate)).format('YYYY-MM-DD');
      const doctorId = this.doctorIdM.value;

      const hasConflict = this.allShifts.some((shift) => {
        return (
          shift.date === formattedDate &&
          shift.doctorId === doctorId &&
          moment(shift.startTime, 'HH:mm').isBefore(end) &&
          moment(shift.endTime, 'HH:mm').isAfter(start)
        );
      });

      if (hasConflict) {
        // this.toastr.error(`You have shift conflicts, please correct it`);
        Swal.fire('You have shift conflicts, please correct it', '', 'error');
        return;
      }
    }

    const payloads = this.selectedDays().map((d: any) => {
      return {
        id: crypto.randomUUID(),
        title: `${this.convertTo12HourFormat(
          this.startTimeM?.value
        )} - ${this.convertTo12HourFormat(this.endTimeM?.value)}`,
        date: moment(new Date(d)).format('YYYY-MM-DD'),
        color: this.color?.value,
        startTime: this.startTimeM?.value,
        endTime: this.endTimeM?.value,
        appointmentType: this.appointmentTypeM?.value,
        appointmentDateId: null,
        doctorId: this.doctorIdM.value,
        facilityId: this.clickedDoctor.facilityId,
        departmentId: this.clickedDoctor.departmentId,
      };
    });

    // const payload = {
    //   id: crypto.randomUUID(),
    //   title: `${this.convertTo12HourFormat(
    //     this.startTime?.value
    //   )} - ${this.convertTo12HourFormat(this.endTime?.value)}`,
    //   date: moment(this.date?.value).format('YYYY-MM-DD'),
    //   color: this.color?.value,
    //   startTime: this.startTime?.value,
    //   endTime: this.endTime?.value,
    //   appointmentType: this.appointmentType?.value,
    // };

    console.log('--Submit form : ', ...payloads);

    this.newShifts = [...this.newShifts, ...payloads];
    this.allShifts = [...this.allShifts, ...payloads];

    this.scheduleMonthForm.reset();
    this.scheduleMonthForm.patchValue({
      appointmentType: 'clinic',
      color: '#6d24b2',
    });
    this.showMonthModal = false;
  }

  async publishNewShiftsMonth() {}

  async updateNewShiftsMonth(shift: any) {
    const start = moment(this.startTimeM?.value, 'HH:mm');
    const end = moment(this.endTimeM?.value, 'HH:mm');

    if (end.isBefore(start)) {
      console.log('End time cannot be before start time');
      this.toastr.error('End time cannot be before start time');
      return;
    }

    const formattedDate = moment(this.dateM?.value).format('YYYY-MM-DD');
    const doctorId = this.doctorIdM.value;

    // 🔍 Conflict check
    const hasConflict = this.allShifts.some((existing) => {
      return (
        existing.id !== shift.id && // Exclude current shift being edited
        existing.date === formattedDate &&
        existing.doctorId === doctorId &&
        moment(existing.startTime, 'HH:mm').isBefore(end) &&
        moment(existing.endTime, 'HH:mm').isAfter(start)
      );
    });

    if (hasConflict) {
      // this.toastr.error(`You have shift conflicts, please correct it`);
      Swal.fire('You have shift conflicts, please correct it', '', 'error');
      return;
    }

    if (this.isValidUUID(shift.id)) {
      const payload = {
        id: shift.id,
        title: `${this.convertTo12HourFormat(
          this.startTimeM?.value
        )} - ${this.convertTo12HourFormat(this.endTimeM?.value)}`,
        date: moment(this.dateM?.value).format('YYYY-MM-DD'),
        color: this.colorM?.value,
        startTime: this.startTimeM?.value,
        endTime: this.endTimeM?.value,
        appointmentType: this.appointmentTypeM?.value,
        doctorId: this.doctorIdM.value,
      };

      this.newShifts = this.newShifts.map((s: any) =>
        s.id == shift.id ? payload : s
      );
      this.allShifts = this.allShifts.map((s: any) =>
        s.id == shift.id ? payload : s
      );
      this.toastr.success('Shift updated successfully');
      this.showMonthModal = false;
      this.viewEvent = false;
      this.clickedDoctor = null;
      this.clickedEvent = null;
      this.scheduleMonthForm.reset();
      this.scheduleMonthForm.patchValue({
        appointmentType: 'clinic',
        color: '#6d24b2',
      });
      this.onMonthClose();
      return;
    }

    const payload = {
      id: shift.appointmentDateId,
      doctorId: this.doctorIdM.value,
      shiftId: shift.id,
      appointmentType: this.appointmentTypeM?.value,
      shiftStart: this.startTimeM?.value,
      shiftEnd: this.endTimeM?.value,
      createdDate: new Date().toISOString(),
      updatedDate: new Date().toISOString(),
    };

    console.log('--Update : ', payload);

    try {
      const res: any = await this.doctorService.updateShift(payload);
      console.log('--Shift update response : ', res);
      this.toastr.success('Shift updated successfully');
      this.showMonthModal = false;
      this.viewEvent = false;
      this.scheduleMonthForm.reset();
      this.clickedDoctor = null;
      this.clickedEvent = null;
      this.scheduleMonthForm.patchValue({
        appointmentType: 'clinic',
        color: '#6d24b2',
      });
      this.ngOnInit();
    } catch (error) {
      console.log('--Error while update shifts : ', error);
      this.toastr.error('Error while update shifts');
    }
  }

  async deleteNewShiftsMonth(shiftId: any) {
    if (this.isValidUUID(shiftId)) {
      this.newShifts = this.newShifts.filter((s: any) => s.id !== shiftId);
      this.allShifts = this.allShifts.filter((s: any) => s.id !== shiftId);
      this.toastr.success('Shift deleted successfully');
      this.showMonthModal = false;
      this.viewEvent = false;
      this.scheduleMonthForm.reset();
      this.clickedDoctor = null;
      this.clickedEvent = null;
      this.scheduleMonthForm.patchValue({
        appointmentType: 'clinic',
        color: '#6d24b2',
      });
      return;
    }

    try {
      const res: any = await this.doctorService.deleteShift({
        shiftId: shiftId,
      });
      console.log('--Shift delete response : ', res);
      this.toastr.success('Shift deleted successfully');
      this.showMonthModal = false;
      this.viewEvent = false;
      this.scheduleMonthForm.reset();
      this.clickedDoctor = null;
      this.clickedEvent = null;
      this.scheduleMonthForm.patchValue({
        appointmentType: 'clinic',
        color: '#6d24b2',
      });
      this.ngOnInit();
    } catch (error) {
      console.log('--Error while deleting shifts : ', error);
      this.toastr.error('Error while deleting shifts');
    }
  }

  // Form validation
  validateFormField = (fieldControl: string, fieldName: string) => {
    const field = this.scheduleForm.get(fieldControl);

    if (
      (field?.touched || field?.touched) &&
      (field?.invalid || field?.errors)
    ) {
      if (field?.errors?.['required']) {
        return `${fieldName} is required.`;
      }
    }

    return null;
  };

  findAndReturnDoctorName(docId: any): string {
    const doc = this.doctors.find((d: any) => d.userId === docId);

    const name =
      (doc?.firstName ? doc.firstName : 'N/A') +
      ' ' +
      (doc?.lastName ? doc.lastName : 'N/A');

    return name;
  }

  // Form Getters
  get date() {
    return this.scheduleForm.get('date') as FormControl;
  }
  get startTime() {
    return this.scheduleForm.get('startTime') as FormControl;
  }
  get endTime() {
    return this.scheduleForm.get('endTime') as FormControl;
  }
  get appointmentType() {
    return this.scheduleForm.get('appointmentType') as FormControl;
  }
  get color() {
    return this.scheduleForm.get('color') as FormControl;
  }

  get dateM() {
    return this.scheduleMonthForm.get('date') as FormControl;
  }
  get startTimeM() {
    return this.scheduleMonthForm.get('startTime') as FormControl;
  }
  get endTimeM() {
    return this.scheduleMonthForm.get('endTime') as FormControl;
  }
  get appointmentTypeM() {
    return this.scheduleMonthForm.get('appointmentType') as FormControl;
  }
  get colorM() {
    return this.scheduleMonthForm.get('color') as FormControl;
  }
  get doctorIdM() {
    return this.scheduleMonthForm.get('doctorId') as FormControl;
  }

  // Utils
  selectDate(date: Date): void {
    this.selectedDate = new Date(date);
    this.changeView(this.viewMode);
  }

  previousPeriod(): void {
    switch (this.viewMode) {
      case ViewMode.Day:
        this.selectedDate.setDate(this.selectedDate.getDate() - 1);
        break;
      case ViewMode.Week:
        this.selectedDate.setDate(this.selectedDate.getDate() - 7);
        break;
      case ViewMode.Month:
        this.selectedDate.setMonth(this.selectedDate.getMonth() - 1);
        break;
    }
    this.changeView(this.viewMode);
  }

  nextPeriod(): void {
    switch (this.viewMode) {
      case ViewMode.Day:
        this.selectedDate.setDate(this.selectedDate.getDate() + 1);
        break;
      case ViewMode.Week:
        this.selectedDate.setDate(this.selectedDate.getDate() + 7);
        break;
      case ViewMode.Month:
        this.selectedDate.setMonth(this.selectedDate.getMonth() + 1);
        break;
    }
    this.changeView(this.viewMode);
  }

  today(): void {
    this.selectedDate = new Date();
    this.changeView(this.viewMode);
  }

  isToday(date: Date): boolean {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  }

  formatHour(hour: number): string {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 === 0 ? 12 : hour % 12;
    return `${displayHour} ${period}`;
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  }

  formatTime(date: Date): string {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  }

  getPeriodTitle1(): string {
    switch (this.viewMode) {
      case ViewMode.Day:
        return this.selectedDate.toLocaleDateString('en-US', {
          weekday: 'long',
          month: 'long',
          day: 'numeric',
          year: 'numeric',
        });
      case ViewMode.Week:
        const startOfWeek = new Date(this.selectedDate);
        startOfWeek.setDate(
          this.selectedDate.getDate() - this.selectedDate.getDay()
        );

        const day = this.selectedDate.getDay();
        const diff = this.selectedDate.getDate() - day + (day === 0 ? -6 : 1); // adjust when Sunday (0)
        startOfWeek.setDate(diff);

        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);

        return `${startOfWeek.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        })} - ${endOfWeek.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        })}`;
      case ViewMode.Month:
        return this.selectedDate.toLocaleDateString('en-US', {
          month: 'long',
          year: 'numeric',
        });
      default:
        return '';
    }
  }

  getPeriodTitle(): string {
    switch (this.viewMode) {
      case ViewMode.Week:
        const weekStart = startOfWeek(this.selectedDate, { weekStartsOn: 1 });
        const weekEnd = endOfWeek(this.selectedDate, { weekStartsOn: 1 });

        return `${format(weekStart, 'MMM d')} - ${format(
          weekEnd,
          'MMM d, yyyy'
        )}`;
      case ViewMode.Day:
        return format(this.selectedDate, 'EEEE, MMMM d, yyyy');
      case ViewMode.Month:
        return format(this.selectedDate, 'MMMM yyyy');
      default:
        return '';
    }
  }

  convertTo24HourFormat(time12h: string): string {
    const [time, modifier] = time12h.trim().split(' ');
    let [hours, minutes] = time.split(':');

    let hrs = parseInt(hours, 10);

    if (modifier.toUpperCase() === 'PM' && hrs !== 12) {
      hrs += 12;
    } else if (modifier.toUpperCase() === 'AM' && hrs === 12) {
      hrs = 0;
    }

    const formattedHours = String(hrs).padStart(2, '0');
    const formattedMinutes = String(minutes).padStart(2, '0');

    return `${formattedHours}:${formattedMinutes}:00`;
  }

  convertTo12HourFormat(time: string): string {
    const [hourStr, minuteStr] = time.split(':');
    let hour = parseInt(hourStr, 10);
    const minute = minuteStr;
    const ampm = hour >= 12 ? 'PM' : 'AM';

    hour = hour % 12;
    hour = hour === 0 ? 12 : hour;

    const formattedHour = hour.toString().padStart(2, '0');

    return `${formattedHour}:${minute} ${ampm}`;
  }

  convertDateIntoDays = (date: string) => {
    const formattedDate = moment(new Date(date)).format('ddd');
    return formattedDate;
  };

  format(date: any, format: string): string {
    return moment(new Date(date)).format(format);
  }

  generateTimeSlots = () => {
    const slots = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const time = moment({ hour, minute }).format('HH:mm');
        slots.push({ id: time, name: this.convertTo12HourFormat(time) });
      }
    }
    // console.log('--All Time slots : ', slots);
    return slots;
  };

  isValidUUID = (uuid: string): boolean => {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  };
}
