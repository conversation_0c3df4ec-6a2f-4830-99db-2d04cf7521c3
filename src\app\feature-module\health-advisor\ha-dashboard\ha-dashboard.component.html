<div class="row aos" data-aos="fade-up">
  <!-- Widgets, Appointments List -->
  <div class="row">
    <!-- Widgets -->
    <div class="col-12">
      <div class="dashboard-box-col d-flex gap-3">
        <div class="dashboard-widget-box">
          <div class="dashboard-content-info">
            <h6>Total Patient</h6>
            <h4>{{ allPatientsCount }}</h4>
            <span class="text-success"
              ><i class="fa-solid fa-arrow-up"></i>15% From Last Week</span
            >
          </div>
          <div class="dashboard-widget-icon">
            <span class="dash-icon-box"
              ><i class="fa-solid fa-user-injured"></i
            ></span>
          </div>
        </div>
        <div class="dashboard-widget-box">
          <div class="dashboard-content-info">
            <h6>Total Doctors</h6>
            <h4>{{ allDoctorsCount }}</h4>
            <span class="text-success"
              ><i class="fa-solid fa-arrow-up"></i>20% From Yesterday</span
            >
          </div>
          <div class="dashboard-widget-icon">
            <span class="dash-icon-box"
              ><i class="fa-solid fa-user-doctor"></i
            ></span>
          </div>
        </div>
        <div class="dashboard-widget-box">
          <div class="dashboard-content-info">
            <h6>Patients Today</h6>
            <h4>{{ todayPatientsCount }}</h4>
            <span class="text-danger"
              ><i class="fa-solid fa-arrow-up"></i>15% From Yesterday</span
            >
          </div>
          <div class="dashboard-widget-icon">
            <span class="dash-icon-box"
              ><i class="fa-solid fa-user-clock"></i
            ></span>
          </div>
        </div>
      </div>
    </div>
    <!-- Activity List -->
    <div class="col-md-5">
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head">
          <div
            class="header-title d-flex justify-content-between align-items-center w-100"
          >
            <h5>Patient Slot Requests</h5>
            <div>
              <a
                class="btn btn-primary float-end mx-2"
                [routerLink]="['/health-advisor/patient-requests']"
                >Expand</a
              >
            </div>
          </div>
        </div>
        <div class="dashboard-card-body">
          <!-- Tab Navigation -->
          <ul class="nav nav-tabs mb-3" id="activityTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button
                class="nav-link active"
                id="today-tab"
                data-bs-toggle="tab"
                data-bs-target="#today"
                type="button"
                role="tab"
                aria-controls="today"
                aria-selected="true"
              >
                Today ({{ todayPatientActivities.data()?.length }})
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                id="upcoming-tab"
                data-bs-toggle="tab"
                data-bs-target="#upcoming"
                type="button"
                role="tab"
                aria-controls="upcoming"
                aria-selected="false"
              >
                Upcoming ({{ upcomingPatientActivities.data()?.length }})
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                id="past-tab"
                data-bs-toggle="tab"
                data-bs-target="#past"
                type="button"
                role="tab"
                aria-controls="past"
                aria-selected="false"
              >
                Past ({{ pastPatientActivities.data()?.length }})
              </button>
            </li>
          </ul>

          <!-- Tab Content -->
          <div class="tab-content" id="activityTabContent">
            <!-- Today Tab -->
            <div
              class="tab-pane fade show active"
              id="today"
              role="tabpanel"
              aria-labelledby="today-tab"
            >
              <div class="activity-list">
                @if (todayPatientActivities.isLoading()) {
                <div class="text-center py-3">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                </div>
                } @else if (todayPatientActivities.data()?.length === 0 ||
                !todayPatientActivities.data()) {
                <div class="text-center py-4">
                  <i
                    class="bi bi-calendar-x text-muted"
                    style="font-size: 2rem"
                  ></i>
                  <p class="text-muted mt-2">
                    No activities scheduled for today
                  </p>
                </div>
                } @else { @for (activity of todayPatientActivities.data(); track
                activity.slotId) {
                <div
                  class="activity-item border rounded p-3 mb-2"
                  (click)="onActivityClick(activity)"
                >
                  <div class="row align-items-center">
                    <div class="col-md-8">
                      <h6 class="mb-1 text-primary">
                        {{ activity.patientName }}
                      </h6>
                      <p class="mb-1 small text-muted">
                        <i class="bi bi-building"></i>
                        {{ activity.departmentName }}
                      </p>
                      <p class="mb-1 small">
                        <i class="bi bi-clipboard-pulse"></i>
                        {{ activity.purpose }}
                      </p>
                      <p class="mb-0 small text-muted">
                        <i class="bi bi-clock"></i>
                        {{
                          util.convertTo12HourFormat(activity.preferredDateTime)
                        }}
                      </p>
                    </div>
                    <div class="col-md-4 text-end">
                      <span
                        class="badge"
                        [class]="
                          activity.status === 'Pending'
                            ? 'bg-warning'
                            : activity.status === 'Confirmed'
                            ? 'bg-success'
                            : 'bg-secondary'
                        "
                      >
                        {{ activity.status }}
                      </span>
                    </div>
                  </div>
                </div>
                } }
              </div>
            </div>

            <!-- Upcoming Tab -->
            <div
              class="tab-pane fade"
              id="upcoming"
              role="tabpanel"
              aria-labelledby="upcoming-tab"
            >
              <div class="activity-list">
                @if (upcomingPatientActivities.isLoading()) {
                <div class="text-center py-3">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                </div>
                } @else if (upcomingPatientActivities.data()?.length === 0 ||
                !upcomingPatientActivities.data()) {
                <div class="text-center py-4">
                  <i
                    class="bi bi-calendar-plus text-muted"
                    style="font-size: 2rem"
                  ></i>
                  <p class="text-muted mt-2">No upcoming activities</p>
                </div>
                } @else { @for (activity of upcomingPatientActivities.data();
                track activity.slotId) {
                <div
                  class="activity-item border rounded p-3 mb-2"
                  (click)="onActivityClick(activity)"
                >
                  <div class="row align-items-center">
                    <div class="col-md-8">
                      <h6 class="mb-1 text-primary">
                        {{ activity.patientName }}
                      </h6>
                      <p class="mb-1 small text-muted">
                        <i class="bi bi-building"></i>
                        {{ activity.departmentName }}
                      </p>
                      <p class="mb-1 small">
                        <i class="bi bi-clipboard-pulse"></i>
                        {{ activity.purpose }}
                      </p>
                      <p class="mb-0 small text-muted">
                        <i class="bi bi-calendar-event"></i>
                        {{ activity.requestedDate | date : "dd MMM yyyy" }} at
                        {{
                          util.convertTo12HourFormat(activity.preferredDateTime)
                        }}
                      </p>
                    </div>
                    <div class="col-md-4 text-end">
                      <span
                        class="badge"
                        [class]="
                          activity.status === 'Pending'
                            ? 'bg-warning'
                            : activity.status === 'Confirmed'
                            ? 'bg-success'
                            : 'bg-secondary'
                        "
                      >
                        {{ activity.status }}
                      </span>
                    </div>
                  </div>
                </div>
                } }
              </div>
            </div>

            <!-- Past Tab -->
            <div
              class="tab-pane fade"
              id="past"
              role="tabpanel"
              aria-labelledby="past-tab"
            >
              <div class="activity-list">
                @if (pastPatientActivities.isLoading()) {
                <div class="text-center py-3">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                </div>
                } @else if (pastPatientActivities.data()?.length === 0 ||
                !pastPatientActivities.data()) {
                <div class="text-center py-4">
                  <i
                    class="bi bi-calendar-check text-muted"
                    style="font-size: 2rem"
                  ></i>
                  <p class="text-muted mt-2">No past activities</p>
                </div>
                } @else { @for (activity of pastPatientActivities.data(); track
                activity.slotId) {
                <div
                  class="activity-item border rounded p-3 mb-2"
                  (click)="onActivityClick(activity)"
                >
                  <div class="row align-items-center">
                    <div class="col-md-8">
                      <h6 class="mb-1 text-primary">
                        {{ activity.patientName }}
                      </h6>
                      <p class="mb-1 small text-muted">
                        <i class="bi bi-building"></i>
                        {{ activity.departmentName }}
                      </p>
                      <p class="mb-1 small">
                        <i class="bi bi-clipboard-pulse"></i>
                        {{ activity.purpose }}
                      </p>
                      <p class="mb-0 small text-muted">
                        <i class="bi bi-calendar-event"></i>
                        {{ activity.requestedDate | date : "dd MMM yyyy" }} at
                        {{
                          activity.preferredDateTime
                            ? util.convertTo12HourFormat(
                                activity.preferredDateTime
                              )
                            : "Time not Specified"
                        }}
                      </p>
                    </div>
                    <div class="col-md-4 text-end">
                      <span
                        class="badge"
                        [class]="
                          activity.status === 'Completed'
                            ? 'bg-success'
                            : activity.status === 'Cancelled'
                            ? 'bg-danger'
                            : 'bg-secondary'
                        "
                      >
                        {{ activity.status }}
                      </span>
                    </div>
                  </div>
                </div>
                } }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Appointments List  -->
    <div class="col-md-7">
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head">
          <div
            class="header-title d-flex justify-content-between align-items-center w-100"
          >
            <h5>Appointments List</h5>
            <div>
              <a
                class="btn btn-primary float-end mx-2"
                (click)="viewAllAppointments()"
              >
                Expand
              </a>
            </div>
          </div>
        </div>

        <div class="dashboard-card-body">
          <!-- Tabs Header -->
          <ul class="nav nav-tabs" role="tablist">
            <li class="nav-item" role="presentation">
              <a
                class="nav-link active"
                data-bs-toggle="tab"
                href="#upcoming1"
                role="tab"
                >Upcoming</a
              >
            </li>
            <li class="nav-item" role="presentation">
              <a class="nav-link" data-bs-toggle="tab" href="#past1" role="tab"
                >Past</a
              >
            </li>
          </ul>

          <!-- Tabs Content -->
          <div class="tab-content">
            <!-- Upcoming Appointments -->
            <div
              class="tab-pane fade show active"
              id="upcoming1"
              role="tabpanel"
            >
              <LoadingSpinner
                [isLoading]="getUpcomingLimitAppointments.isLoading()"
              ></LoadingSpinner>

              <PageWithSearchPagination
                *ngIf="
                  getUpcomingLimitAppointments.isSuccess() &&
                  getUpcomingLimitAppointments.data()?.length
                "
                [data]="getUpcomingLimitAppointments.data()"
                (filteredDataChange)="filteredUpcomingAppointments = $event"
                [isPagination]="true"
                [itemsPerPage]="3"
                [isSearch]="true"
                searchPlaceHolder="Search by Patient Name"
                [isItemsPerPage]="false"
              >
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead class="table-light d-none">
                      <tr>
                        <th class="border-0 ps-4">
                          <i class="fas fa-user me-2 text-muted"></i>Patient
                          Information
                        </th>
                        <th class="border-0">
                          <i class="fas fa-calendar-alt me-2 text-muted"></i
                          >Appointment Details
                        </th>
                        <th class="border-0">
                          <i class="fas fa-info-circle me-2 text-muted"></i
                          >Status
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let app of filteredUpcomingAppointments;
                          let i = index
                        "
                        (click)="onAppointmentClick(app)"
                        class="cursor-pointer appointment-row"
                        [style.animation-delay]="i * 0.1 + 's'"
                      >
                        <td class="ps-4 py-3">
                          <div class="d-flex align-items-center">
                            <div class="position-relative me-3">
                              <img
                                [src]="app.photoLink || util.getNormalAvatar()"
                                [alt]="app.patientName + ' Avatar'"
                                class="rounded-circle border border-2 border-light shadow-sm"
                                style="
                                  width: 50px;
                                  height: 50px;
                                  object-fit: cover;
                                "
                              />
                              <span
                                class="position-absolute bottom-0 end-0 translate-middle p-1 bg-success border border-white rounded-circle"
                              >
                                <span class="visually-hidden">Online</span>
                              </span>
                            </div>
                            <div class="patient-details">
                              <h6 class="mb-1 fw-semibold text-dark">
                                {{ app.patientName || "N/A" }}
                              </h6>
                              <div class="d-flex align-items-center">
                                <i
                                  class="fas fa-hospital text-muted me-1"
                                  style="font-size: 12px"
                                ></i>
                                <span class="text-muted small">
                                  {{
                                    app.facilityName
                                      ? (app.facilityName | titlecase)
                                      : "No Facility"
                                  }}
                                </span>
                              </div>
                            </div>
                          </div>
                        </td>
                        <td class="py-3">
                          <div class="appointment-info">
                            <div class="d-flex align-items-center mb-1">
                              <i class="fas fa-calendar text-primary me-2"></i>
                              <span class="fw-medium text-dark">
                                {{
                                  app.appointmentDate
                                    ? (app.appointmentDate
                                      | date : "MMM dd, yyyy")
                                    : "N/A"
                                }}
                              </span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                              <i class="fas fa-clock text-success me-2"></i>
                              <span class="text-muted">
                                {{
                                  app.appointmentTime
                                    ? convertTo12HourFormat(app.appointmentTime)
                                    : "N/A"
                                }}
                              </span>
                            </div>
                            <span
                              class="badge rounded-pill px-3 py-1 fw-normal"
                              [ngClass]="{
                                'bg-primary':
                                  app.appointmentType?.toLowerCase() ===
                                  'clinic',
                                'bg-success':
                                  app.appointmentType?.toLowerCase() ===
                                  'online',
                                'bg-warning':
                                  app.appointmentType?.toLowerCase() ===
                                  'nurse',
                                'bg-info':
                                  app.appointmentType?.toLowerCase() ===
                                  'hospital',
                                'bg-danger':
                                  app.appointmentType?.toLowerCase() ===
                                  'sample',
                                'bg-secondary': app.appointmentType
                              }"
                            >
                              <i class="fas fa-stethoscope me-1"></i>
                              {{
                                app.appointmentType
                                  ? (app.appointmentType | titlecase)
                                  : "N/A"
                              }}
                            </span>
                          </div>
                        </td>

                        <td class="py-3">
                          <div class="d-flex align-items-center">
                            <div class="status-indicator me-2">
                              <span
                                class="badge bg-warning text-dark rounded-pill px-3 py-2"
                              >
                                <i class="fas fa-clock me-1"></i>
                                {{
                                  app.appointmentStatus
                                    ? (app.appointmentStatus | titlecase)
                                    : "N/A"
                                }}
                              </span>
                            </div>
                          </div>
                          <small class="text-muted d-block mt-1">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ app.facilityName ? "On-site" : "Virtual" }}
                          </small>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </PageWithSearchPagination>

              <ErrorAlert
                error="No Upcoming Appointments Found"
                *ngIf="getUpcomingLimitAppointments.isError()"
                color="blue"
              ></ErrorAlert>
            </div>

            <!-- Past Appointments -->
            <div class="tab-pane fade" id="past1" role="tabpanel">
              <LoadingSpinner
                [isLoading]="getPastLimitAppointments.isLoading()"
              ></LoadingSpinner>

              <PageWithSearchPagination
                *ngIf="
                  getPastLimitAppointments.isSuccess() &&
                  getPastLimitAppointments.data()?.length
                "
                [data]="getPastLimitAppointments.data()"
                (filteredDataChange)="filteredPastAppointments = $event"
                [isPagination]="true"
                [itemsPerPage]="3"
                [isSearch]="true"
                searchPlaceHolder="Search by Patient Name"
                [isItemsPerPage]="false"
              >
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead class="table-light d-none">
                      <tr>
                        <th class="border-0 ps-4">
                          <i class="fas fa-user me-2 text-muted"></i>Patient
                          Information
                        </th>
                        <th class="border-0">
                          <i class="fas fa-calendar-alt me-2 text-muted"></i
                          >Appointment Details
                        </th>
                        <th class="border-0">
                          <i class="fas fa-info-circle me-2 text-muted"></i
                          >Status
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let app of filteredPastAppointments;
                          let i = index
                        "
                        class="cursor-pointer appointment-row"
                        [style.animation-delay]="i * 0.1 + 's'"
                        (click)="onAppointmentClick(app)"
                      >
                        <td class="ps-4 py-3">
                          <div class="d-flex align-items-center">
                            <div class="position-relative me-3">
                              <img
                                [src]="app.photoLink || util.getNormalAvatar()"
                                [alt]="app.patientName + ' Avatar'"
                                class="rounded-circle border border-2 border-light shadow-sm"
                                style="
                                  width: 50px;
                                  height: 50px;
                                  object-fit: cover;
                                "
                              />
                              <span
                                class="position-absolute bottom-0 end-0 translate-middle p-1 bg-success border border-white rounded-circle"
                              >
                                <span class="visually-hidden">Online</span>
                              </span>
                            </div>
                            <div class="patient-details">
                              <h6 class="mb-1 fw-semibold text-dark">
                                {{ app.patientName || "N/A" }}
                              </h6>
                              <div class="d-flex align-items-center">
                                <i
                                  class="fas fa-hospital text-muted me-1"
                                  style="font-size: 12px"
                                ></i>
                                <span class="text-muted small">
                                  {{
                                    app.facilityName
                                      ? (app.facilityName | titlecase)
                                      : "No Facility"
                                  }}
                                </span>
                              </div>
                            </div>
                          </div>
                        </td>
                        <td class="py-3">
                          <div class="appointment-info">
                            <div class="d-flex align-items-center mb-1">
                              <i class="fas fa-calendar text-primary me-2"></i>
                              <span class="fw-medium text-dark">
                                {{
                                  app.appointmentDate
                                    ? (app.appointmentDate
                                      | date : "MMM dd, yyyy")
                                    : "N/A"
                                }}
                              </span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                              <i class="fas fa-clock text-success me-2"></i>
                              <span class="text-muted">
                                {{
                                  app.appointmentTime
                                    ? convertTo12HourFormat(app.appointmentTime)
                                    : "N/A"
                                }}
                              </span>
                            </div>
                            <span
                              class="badge rounded-pill px-3 py-1 fw-normal"
                              [ngClass]="{
                                'bg-primary':
                                  app.appointmentType?.toLowerCase() ===
                                  'clinic',
                                'bg-success':
                                  app.appointmentType?.toLowerCase() ===
                                  'online',
                                'bg-warning':
                                  app.appointmentType?.toLowerCase() ===
                                  'nurse',
                                'bg-info':
                                  app.appointmentType?.toLowerCase() ===
                                  'hospital',
                                'bg-danger':
                                  app.appointmentType?.toLowerCase() ===
                                  'sample',
                                'bg-secondary': app.appointmentType
                              }"
                            >
                              <i class="fas fa-stethoscope me-1"></i>
                              {{
                                app.appointmentType
                                  ? (app.appointmentType | titlecase)
                                  : "N/A"
                              }}
                            </span>
                          </div>
                        </td>

                        <td class="py-3">
                          <div class="d-flex align-items-center">
                            <div class="status-indicator me-2">
                              <span
                                class="badge bg-warning text-dark rounded-pill px-3 py-2"
                              >
                                <i class="fas fa-clock me-1"></i>
                                {{
                                  app.appointmentStatus
                                    ? (app.appointmentStatus | titlecase)
                                    : "N/A"
                                }}
                              </span>
                            </div>
                          </div>
                          <small class="text-muted d-block mt-1">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ app.facilityName ? "On-site" : "Virtual" }}
                          </small>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!-- 🔴 Reuse your table HTML here with `filteredPast` -->
              </PageWithSearchPagination>

              <ErrorAlert
                error="No Past Appointments Found"
                *ngIf="getPastLimitAppointments.isError()"
                color="blue"
              ></ErrorAlert>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Roles Pie Chart, List -->
    <div class="row">
      <!-- Weekly Overview -->
      <div class="col-md-6">
        <div class="dashboard-card w-100">
          <div class="dashboard-card-head border-0">
            <div class="header-title">
              <h5>Weekly Overview</h5>
            </div>
            <!-- <div class="chart-create-date">
              <h6>Mar 14 - Mar 21</h6>
            </div> -->
          </div>
          <div class="dashboard-card-body">
            <div class="chart-tab">
              <ul
                class="nav nav-pills product-licence-tab"
                id="pills-tab2"
                role="tablist"
              >
                <li class="nav-item" role="presentation">
                  <button
                    class="nav-link active"
                    id="pills-revenue-tab"
                    data-bs-toggle="pill"
                    data-bs-target="#pills-revenue"
                    type="button"
                    role="tab"
                    aria-controls="pills-revenue"
                    aria-selected="false"
                  >
                    Revenue
                  </button>
                </li>
                <li class="nav-item" role="presentation">
                  <button
                    class="nav-link"
                    id="pills-appointment-tab"
                    data-bs-toggle="pill"
                    data-bs-target="#pills-appointment"
                    type="button"
                    role="tab"
                    aria-controls="pills-appointment"
                    aria-selected="true"
                  >
                    Appointments
                  </button>
                </li>
              </ul>
              <div class="tab-content w-100" id="v-pills-tabContent">
                <div
                  class="tab-pane fade show active"
                  id="pills-revenue"
                  role="tabpanel"
                  aria-labelledby="pills-revenue-tab"
                >
                  <div *ngIf="facilityWeeklyRevenue.isError()">
                    Error fetching data...
                  </div>

                  <div
                    *ngIf="
                      facilityWeeklyAppointments.isPending() &&
                      facilityWeeklyRevenue.isPending()
                    "
                  >
                    Loading...
                  </div>

                  <div
                    *ngIf="
                      facilityWeeklyRevenue.isSuccess() &&
                      facilityWeeklyRevenue.data()
                    "
                  >
                    <div
                      id="revenue-chart"
                      *ngIf="facilityWeeklyRevenue.data().length > 0"
                    >
                      <apx-chart
                        [series]="chartOptions1.series"
                        [chart]="chartOptions1.chart"
                        [dataLabels]="chartOptions1.dataLabels"
                        [plotOptions]="chartOptions1.plotOptions"
                        [xaxis]="chartOptions1.xaxis"
                      ></apx-chart>
                    </div>

                    <div *ngIf="facilityWeeklyRevenue.data().length == 0">
                      No Recent Revenue
                    </div>
                  </div>
                </div>

                <div
                  class="tab-pane fade"
                  id="pills-appointment"
                  role="tabpanel"
                  aria-labelledby="pills-appointment-tab"
                >
                  <div *ngIf="facilityWeeklyAppointments.isError()">
                    Error fetching data...
                  </div>

                  <div *ngIf="facilityWeeklyAppointments.isPending()">
                    Loading...
                  </div>

                  <div
                    *ngIf="
                      facilityWeeklyAppointments.isSuccess() &&
                      facilityWeeklyAppointments.data()
                    "
                  >
                    <div
                      id="appointment-chart"
                      *ngIf="facilityWeeklyAppointments.data().length > 0"
                    >
                      <apx-chart
                        [series]="chartOptions2.series"
                        [chart]="chartOptions2.chart"
                        [dataLabels]="chartOptions2.dataLabels"
                        [plotOptions]="chartOptions2.plotOptions"
                        [xaxis]="chartOptions2.xaxis"
                      ></apx-chart>
                    </div>
                    <div *ngIf="facilityWeeklyAppointments.data().length == 0">
                      No Recent Appointments
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- List of Roles -->
      <div class="col-md-6 dashboard-card">
        <div class="dashboard-chart-col w-100">
          <div class="w-100">
            <div class="dashboard-card-head border-0">
              <div class="header-title">
                <h5>List of Roles</h5>
              </div>
            </div>
            <div class="dashboard-card-body p-0" *ngIf="isChartReady">
              <div
                id="pieChart"
                *ngIf="pieChartOptions?.series && pieChartOptions?.chart"
              >
                <apx-chart
                  #pieChart
                  [series]="pieChartOptions.series"
                  [chart]="pieChartOptions.chart"
                  [labels]="pieChartOptions.labels"
                  [responsive]="pieChartOptions.responsive"
                ></apx-chart>
              </div>
            </div>
            <div
              class="d-flex justify-content-center align-items-center"
              *ngIf="!isChartReady"
            >
              Loading...
            </div>
          </div>
        </div>
      </div>
      <!-- Current Role Users List -->
      <div class="col-12">
        <div class="dashboard-card w-100">
          <div class="dashboard-card-head border-0">
            <div class="header-title">
              <h5>{{ currentDept }} ({{ deptUsers && deptUsers.length }})</h5>
            </div>
          </div>
          <div class="dashboard-card-body p-0">
            <div>
              <app-dynamic-table
                [dataList]="deptUsers"
                [columns]="deptUsersCols"
                [showSerialNumber]="true"
                [showActions]="false"
                [isDataLoading]="loader['userslist']"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--  Followups -->
    <div class="row">
      <!-- Follow ups -->
      <div class="col-8 d-none">
        <div class="dashboard-card w-100">
          <div class="dashboard-card-head border-0">
            <div class="header-title">
              <h5>Follow ups ({{ refillList && refillList.length }})</h5>
            </div>
          </div>
          <div class="dashboard-card-body p-0">
            <div
              class="search-header d-flex align-items-baseline gap-2 gap-lg-5"
            >
              <div class="search-field">
                <select
                  name="selectPageCount"
                  id="selectPageCount"
                  class="form-control"
                  [(ngModel)]="itemsPerPage"
                  (change)="onItemsPerPageChange()"
                >
                  <option value="5" selected>
                    5 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                  </option>
                  <option value="10">
                    10 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                  </option>
                  <option value="15">
                    15 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                  </option>
                  <option value="25">
                    25 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                  </option>
                  <option value="50">
                    50 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                  </option>
                  <option value="50">
                    100 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                  </option>
                </select>
              </div>
              <div class="search-field flex-grow-1">
                <input
                  type="text"
                  class="form-control"
                  placeholder="Search"
                  [(ngModel)]="searchQuery"
                  (input)="onSearchChange()"
                />
                <span class="search-icon"
                  ><i class="fa-solid fa-magnifying-glass"></i
                ></span>
              </div>
            </div>
            <div class="custom-table p-0">
              <div class="table-responsive">
                <table class="table table-center mb-0">
                  <thead>
                    <tr>
                      <th>S.No</th>
                      <th>Facility Name</th>
                      <th>Patient Name</th>
                      <th>Location</th>
                      <!-- <th>Prescribed by</th> -->
                      <!-- <th>Follow-Up Date</th> -->
                      <th>Prescription Date</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <ng-container
                      *ngIf="
                        filteredRefillList && filteredRefillList.length;
                        else noUsers
                      "
                    >
                      <tr
                        *ngFor="
                          let refill of filteredRefillList;
                          trackBy: trackByRefillId;
                          let i = index
                        "
                      >
                        <td>
                          <a
                            href="javascript:void(0);"
                            class="text-blue-600"
                            data-bs-toggle="modal"
                            data-bs-target="#invoice_view"
                            >{{ i + 1 }}</a
                          >
                        </td>
                        <td>
                          {{
                            refill.facilityName ? refill.facilityName : "N/A"
                          }}
                        </td>
                        <td>
                          {{ refill.patientName ? refill.patientName : "N/A" }}
                        </td>
                        <!-- <td>
                          Dr.{{ refill.doctorName ? refill.doctorName : "N/A" }}
                        </td> -->
                        <td>
                          {{ refill.cityName ? refill.cityName : "N/A" }}
                        </td>
                        <!-- <td>N/A</td> -->
                        <td>
                          {{
                            refill.prescriptionDate
                              ? (refill.prescriptionDate | date : "dd MMM YYYY")
                              : "N/A"
                          }}
                        </td>
                        <td>
                          <button
                            class="btn btn-primary"
                            title="Refill"
                            [routerLink]="[
                              '/admin/manager/follow-up/facility',
                              refill?.facilityId,
                              'doctors',
                              refill?.prescribedBy,
                              'patients',
                              refill?.patientId,
                              'prescriptions',
                              refill?.prescriptionId
                            ]"
                          >
                            Review
                          </button>
                        </td>
                      </tr>
                    </ng-container>
                    <ng-template #noUsers>
                      <tr>
                        <td colspan="7" class="text-center">
                          No Record Available
                        </td>
                      </tr>
                    </ng-template>
                  </tbody>
                </table>
              </div>
            </div>
            <!-- Pagination -->
            <div
              class="pagination-container d-flex justify-content-center align-items-center mt-3"
            >
              <ul class="pagination custom-pagination mb-0">
                <!-- Previous Button -->
                <li
                  class="page-item"
                  [class.disabled]="currentPage === 1"
                  (click)="goToPage(currentPage - 1)"
                >
                  <a class="page-link">
                    <i class="fa fa-angle-left me-1"></i> Prev
                  </a>
                </li>

                <!-- Page Numbers -->
                <li
                  *ngFor="let page of visiblePages"
                  class="page-item"
                  [class.active]="page === currentPage"
                  [class.disabled]="page === '...'"
                  (click)="page !== '...' && goToPage(page)"
                >
                  <a class="page-link">{{ page }}</a>
                </li>

                <!-- Next Button -->
                <li
                  class="page-item"
                  [class.disabled]="currentPage === totalPages()"
                  (click)="goToPage(currentPage + 1)"
                >
                  <a class="page-link">
                    Next <i class="fa fa-angle-right ms-1"></i>
                  </a>
                </li>
              </ul>
            </div>
            <!-- /Pagination -->
          </div>
        </div>
      </div>
    </div>
    <!-- Recent Patients, Upcoming Appointments -->
    <div class="row">
      <div class="col-6">
        <div class="dashboard-card w-100">
          <div class="dashboard-card-head">
            <div class="header-title">
              <h5>Recent Patients</h5>
            </div>
          </div>
          <div
            class="dashboard-card-body"
            *ngIf="recentPatients && recentPatients.length"
          >
            <div
              class="d-flex recent-patient-grid-boxes overflow-auto"
              *ngIf="!loader['recentPatients']"
            >
              <div
                class="recent-patient-grid"
                *ngFor="let patient of recentPatients"
                (click)="
                  viewVisitDetails(
                    patient?.patientId,
                    patient?.visitId,
                    patient?.doctorId
                  )
                "
              >
                <a class="patient-img">
                  <img
                    src="assets/images/avatar-image.webp"
                    alt="Avatar image"
                    class="img-thumbnail"
                    *ngIf="!patient.photoLink"
                  />
                  <img
                    [src]="patient.photoLink"
                    alt="Avatar image"
                    class="img-thumbnail"
                    *ngIf="patient.photoLink"
                  />
                </a>
                <h5>
                  <a>{{ patient.patientName || "N/A" }}</a>
                </h5>
                <span>Patient ID : {{ patient.patientId || "N/A" }}</span>
              </div>
            </div>
            <div
              class="d-flex justify-content-center align-items-center"
              *ngIf="loader['recentPatients']"
            >
              Loading...
            </div>
          </div>
          <div *ngIf="recentPatients && recentPatients.length <= 0">
            No Patients
          </div>
        </div>
      </div>
      <div class="col-6">
        <div class="dashboard-card w-100">
          <div
            class="upcoming-appointment-card"
            *ngIf="getUpcomingData() && getUpcomingData().length"
          >
            <div class="title-card">
              <h5>Upcoming Appointment</h5>
            </div>
            <div class="upcoming-patient-info">
              <div class="info-details">
                <span class="img-avatar">
                  <!-- <img
                src="assets/img/doctors-dashboard/profile-01.jpg"
                alt="Img" /> -->
                  <img
                    src="assets/images/avatar-image.webp"
                    alt="Avatar image"
                    class="img-thumbnail"
                    *ngIf="!getUpcomingData()[0].photoLink" />
                  <img
                    [src]="getUpcomingData()[0].photoLink"
                    alt="Avatar image"
                    class="img-thumbnail"
                    *ngIf="getUpcomingData()[0].photoLink"
                /></span>
                <div class="name-info">
                  <span>#{{ getUpcomingData()[0].location || "N/A" }}</span>
                  <h6>
                    {{
                      getUpcomingData()[0].userRoleName.includes("Doctor")
                        ? "Dr."
                        : ""
                    }}
                    {{ getUpcomingData()[0].doctorName || "N/A" }}
                  </h6>
                  <p class="m-0 p-0 text-white">
                    {{ getUpcomingData()[0].userRoleName || "N/A" }}
                  </p>
                </div>
              </div>
              <div class="date-details">
                <span>General visit</span>
                <h6>
                  {{
                    getUpcomingData()[0].appointmentDate
                      ? (getUpcomingData()[0].appointmentDate
                        | date : "dd MMM yyyy")
                      : "N/A"
                  }}
                  {{
                    getUpcomingData()[0].appointmentTime
                      ? convertTo12HourFormat(
                          getUpcomingData()[0].appointmentTime
                        )
                      : "N/A"
                  }}
                </h6>
              </div>
              <div class="circle-bg">
                <img src="assets/img/bg/dashboard-circle-bg.png" alt="" />
              </div>
            </div>
            <div class="appointment-card-footer">
              <h5 *ngIf="getUpcomingData()[0].appointmentType === 'online'">
                <i class="fa-solid fa-video"></i>Video Appointment
              </h5>
              <h5 *ngIf="getUpcomingData()[0].appointmentType === 'clinic'">
                <i class="fa-solid fa-clinic-medical"></i>Clinic Appointment
              </h5>
              <div class="btn-getUpcomingData()">
                <!-- <a  class="btn">Chat Now</a> -->
                <a
                  class="btn"
                  [routerLink]="[
                    '/admin/manager/view-appointment',
                    getUpcomingData()[0].id
                  ]"
                  >View Appointment</a
                >
              </div>
            </div>
          </div>
          <div
            class="upcoming-appointment-card"
            *ngIf="!getUpcomingData() || !getUpcomingData().length"
          >
            No Upcoming Appointments
          </div>
        </div>
      </div>
    </div>
    <!-- Doctors List, Patients List -->
    <div class="row">
      <!-- Doctors List, -->
      <div class="col-12">
        <div class="dashboard-card w-100">
          <div class="dashboard-card-head border-0">
            <div class="header-title">
              <h5>
                Doctors List ({{ allDoctorsList && allDoctorsList.length }})
              </h5>
            </div>
          </div>
          <div class="dashboard-card-body">
            <div class="custom-table border-0">
              <app-dynamic-table
                [dataList]="allDoctorsList"
                [columns]="doctorsCols"
                [showSerialNumber]="false"
                [showActions]="false"
                [isDataLoading]="loader['doctors']"
              />
            </div>
          </div>
        </div>
      </div>
      <!--  Patients List -->
      <div class="col-12">
        <div class="dashboard-card w-100">
          <div class="dashboard-card-head border-0">
            <div class="header-title">
              <h5>
                Patients List ({{ allPatientsList && allPatientsList.length }})
              </h5>
            </div>
          </div>
          <div class="dashboard-card-body border-0">
            <div class="custom-table border-0">
              <app-dynamic-table
                [dataList]="allPatientsList"
                [columns]="patientsCols"
                [showSerialNumber]="false"
                [showActions]="false"
                [isDataLoading]="loader['patients']"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
