import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  I_APPOINTMENT_CONFIRMATION_DETAIL,
  I_APPOINTMENT_FOR_CONFIRMATION,
} from '../model';
import {
  injectMutation,
  injectQuery,
} from '@tanstack/angular-query-experimental';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';
import { ActivatedRoute } from '@angular/router';
import { AdminFriendlyPageWrapperComponent } from 'src/app/shared/components/re-use/admin-friendly-page-wrapper/admin-friendly-page-wrapper.component';
import { FormsModule } from '@angular/forms';
import { isAfter, isBefore, isToday } from 'date-fns';

@Component({
  selector: 'app-one-doctor-appointments',
  imports: [CommonModule, AdminFriendlyPageWrapperComponent, FormsModule],
  templateUrl: './one-doctor-appointments.component.html',
  styleUrl: './one-doctor-appointments.component.scss',
})
export class OneDoctorAppointmentsComponent {
  util = inject(UtilFunctions);
  user = this.util.user;
  activeRoute = inject(ActivatedRoute);
  doctorId = this.util.decrypt(this.activeRoute.snapshot.params['doctorId']);
  activeTab = 'today';

  todayAppointmentsSearch: string = '';
  upcomingAppointmentsSearch: string = '';
  pastAppointmentsSearch: string = '';

  constructor() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Queries
  allAppointment = injectQuery(() => ({
    queryKey: ['doctor-appointments', this.doctorId],
    queryFn: async () => {
      const res: I_APPOINTMENT_FOR_CONFIRMATION[] =
        await this.util.patientService.getSingleDoctorAppointments(
          this.doctorId
        );

      return res;

      // return res.map((a: I_APPOINTMENT_FOR_CONFIRMATION) => {
      //   return {
      //     ...a,
      //     appointmentTime: this.util.convertTo12HourFormat(a.appointmentTime24),
      //     appointmentDateString: this.formatDate(a.appointmentDate),
      //   };
      // });
    },
    refetchOnWindowFocus: false,
  }));

  // Mutations
  createAppointmentConfirmationMutation = injectMutation(() => ({
    mutationFn: (payload: any) =>
      this.util.patientService.createAppointmentConfirmation(payload),
    onSuccess: (res: any) => {
      if (res && res.error) {
        this.util.swal.fire(
          'Error creating appointment confirmation',
          '',
          'error'
        );

        return;
      }

      this.util.swal.fire(
        'Appointment confirmation created successfully',
        '',
        'success'
      );

      this.allAppointment.refetch();
    },
    onError: (error: any) => {
      this.util.swal.fire(
        'Error creating appointment confirmation',
        '',
        'error'
      );
    },
  }));

  updateAppointmentConfirmationMutation = injectMutation(() => ({
    mutationFn: (payload: any) =>
      this.util.patientService.updateAppointmentConfirmation(payload),
    onSuccess: (res: any) => {
      if (res && res.error) {
        this.util.swal.fire(
          'Error updating appointment confirmation',
          '',
          'error'
        );

        return;
      }

      this.util.swal.fire(
        'Appointment confirmation updated successfully',
        '',
        'success'
      );

      this.allAppointment.refetch();
    },
    onError: (error: any) => {
      this.util.swal.fire(
        'Error updating appointment confirmation',
        '',
        'error'
      );
    },
  }));

  // Getters for filtered appointments
  todayAppointments() {
    const now = new Date();

    return (
      this.allAppointment.data()?.filter((apt) => {
        // combine date + time into one ISO string
        const aptDateTime = new Date(
          `${apt.appointmentDate}T${apt.appointmentTime}`
        );

        // only include today's appointments that are still upcoming
        return isToday(aptDateTime) && isAfter(aptDateTime, now);
      }) || []
    );
  }

  upcomingAppointments() {
    const today = new Date().toISOString().split('T')[0];
    return (
      this.allAppointment
        .data()
        ?.filter((apt) => apt.appointmentDate > today) || []
    );
  }

  pastAppointments() {
    const now = new Date();

    return (
      this.allAppointment.data()?.filter((apt) => {
        // Combine date + time into a single Date object
        const aptDateTime = new Date(
          `${apt.appointmentDate}T${apt.appointmentTime}`
        );

        // Past appointment = appointment datetime is before current datetime
        return isBefore(aptDateTime, now);
      }) || []
    );
  }

  filterTodayAppointments() {
    const searchTerm = this.todayAppointmentsSearch.toLowerCase();
    // console.log('Search term: ', searchTerm);
    // console.log('Today appointments: ', this.todayAppointments());

    return this.todayAppointments().filter((apt: any) => {
      // Only check top-level values (not nested objects/arrays)
      return Object.values(apt).some((val) => {
        if (val && typeof val === 'string') {
          return val.toLowerCase().includes(searchTerm);
        }
        return false;
      });
    });
  }

  filterUpcomingAppointments() {
    return this.upcomingAppointments().filter((apt: any) => {
      // Only check top-level values (not nested objects/arrays)
      return Object.values(apt).some((val) => {
        if (val && typeof val === 'string') {
          return val.toLowerCase().includes(this.upcomingAppointmentsSearch);
        }
        return false;
      });
    });
  }

  filterPastAppointments() {
    return this.pastAppointments().filter((apt: any) => {
      // Only check top-level values (not nested objects/arrays)
      return Object.values(apt).some((val) => {
        if (val && typeof val === 'string') {
          return val.toLowerCase().includes(this.pastAppointmentsSearch);
        }
        return false;
      });
    });
  }

  // Summary counts
  get totalAppointments() {
    return this.allAppointment.data()?.length || 0;
  }

  get todayCount() {
    return this.todayAppointments().length;
  }

  get upcomingCount() {
    return this.upcomingAppointments().length;
  }

  get pastCount() {
    return this.pastAppointments().length;
  }

  // Functions
  setActiveTab(tab: string) {
    this.activeTab = tab;
  }

  back() {
    this.util.location.back();
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    };
    return date.toLocaleDateString('en-US', options);
  }

  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'badge-success';
      case 'pending':
        return 'badge-warning';
      case 'completed':
        return 'badge-info';
      case 'cancelled':
        return 'badge-danger';
      case 'scheduled':
        return 'badge-primary';
      default:
        return 'badge-secondary';
    }
  }

  getAppointmentTypeIcon(type: string): string {
    switch (type.toLowerCase()) {
      case 'online':
        return 'fas fa-video';
      case 'clinic':
        return 'fas fa-clinic-medical';
      case 'hospital':
        return 'fas fa-hospital';
      case 'nurse':
        return 'fas fa-user-nurse';
      case 'sample':
        return 'fas fa-tint';
      default:
        return 'fas fa-calendar';
    }
  }

  getAppointmentTypeBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'clinic':
        return 'badge-success';
      case 'online':
        return 'badge-warning';
      case 'hospital':
        return 'badge-info';
      case 'sample':
        return 'badge-danger';
      case 'nurse':
        return 'badge-primary';
      default:
        return 'badge-secondary';
    }
  }

  confirmAppointment(specialist: I_APPOINTMENT_CONFIRMATION_DETAIL) {
    this.util.swal
      .fire({
        title: 'Are you sure?',
        text: 'You are about to confirm this appointment',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, confirm it!',
      })
      .then(async (result) => {
        if (result.isConfirmed) {
          // Cancel appointment
          console.log('Confirm appointment:', specialist);

          const confirmationPayload = {
            phoneConfirmation: false,
            confirmationId: specialist.confirmationId,
            status: 'Confirmed',
            confirmedBy: this.user.userId + ' - ' + this.user.roleDescription,
            confirmedDate: new Date().toISOString(),
          };

          console.log('Payload : ', confirmationPayload);

          this.updateAppointmentConfirmationMutation.mutate(
            confirmationPayload
          );
        } else {
          return;
        }
      });
  }

  cancelAppointment(specialist: I_APPOINTMENT_CONFIRMATION_DETAIL) {
    this.util.swal
      .fire({
        title: 'Are you sure?',
        text: 'You are about to cancel this appointment',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, cancel it!',
      })
      .then(async (result) => {
        if (result.isConfirmed) {
          // Cancel appointment
          console.log('Cancel appointment:', specialist);

          const confirmationPayload = {
            phoneConfirmation: false,
            confirmationId: specialist.confirmationId,
            status: 'Cancelled',
            confirmedBy: this.user.userId + ' - ' + this.user.roleDescription,
            confirmedDate: new Date().toISOString(),
          };

          console.log('Payload : ', confirmationPayload);

          this.updateAppointmentConfirmationMutation.mutate(
            confirmationPayload
          );
        } else {
          return;
        }
      });
  }

  getPatientConfirmationData(appointment: I_APPOINTMENT_FOR_CONFIRMATION): any {
    const patientConfirmation = appointment.specialist.find((s: any) =>
      s.specialistRole.includes('Doctor')
    );
    return patientConfirmation;
  }

  getPatientConfirmations(appointment: I_APPOINTMENT_FOR_CONFIRMATION): any {
    return appointment.specialist.filter((s: any) =>
      s.specialistRole.includes('Doctor')
    );
  }

  getWhoBookedThisAppointment(
    appointment: I_APPOINTMENT_FOR_CONFIRMATION
  ): boolean {
    if (
      appointment.bookedBy
        .split('-')[0]
        .includes(this.util.ROLES_DETAILS.DOCTOR.roleDescription) &&
      appointment.bookedBy.split('-')[1] == this.user.userId
    ) {
      return false;
    }

    return true;
  }

  getSpecialistConfirmationStatus(appointment: any) {
    const docConfirmation = appointment?.specialist?.find((s: any) =>
      s.specialistRole.includes('Doctor')
    );

    return docConfirmation?.confirmationStatus.includes('Confirmed');
  }

  getSpecialistRole(appointment: any) {
    if (appointment.specialist) {
      const docConfirmation = appointment?.specialist?.find(
        (s: any) =>
          !s.specialistRole.includes('Patient') &&
          !s.specialistRole.includes('Super Admin') &&
          !s.specialistRole.includes('Health Advisor')
      );

      return docConfirmation?.specialistRole;
    }
    return '';
  }

  viewVisit(appointment: any) {
    console.log('Specialist Role:', this.getSpecialistRole(appointment));
    if (this.getSpecialistRole(appointment).includes('Dietitian')) {
      // console.log('View visit Dietician:', appointment);
      this.getDietFormRoutes(appointment);
    }
    if (this.getSpecialistRole(appointment).includes('Physiotherapy')) {
      // console.log('View visit Physiotherapist:', appointment);
      this.getPhysioFormRoutes(appointment);
    }
    if (this.getSpecialistRole(appointment).includes('Doctor')) {
      // console.log('View visit Doctor:', appointment);
      this.getVisitFormRoutes(appointment);
    }
  }

  getDietFormRoutes(appointment: any) {
    // this.util.swal.fire('Coming Soon', 'Diet Form', 'info');
    if (this.user.roleName.includes(this.util.ROLES.PATIENT)) {
      this.util.router.navigate([
        '/patient/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
      this.util.router.navigate([
        '/health-advisor/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.ADMIN)) {
      this.util.router.navigate([
        '/admin/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DIETITIAN)) {
      this.util.router.navigate([
        '/dietitian/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DOCTOR)) {
      this.util.router.navigate([
        '/doctor/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.NURSE)) {
      this.util.router.navigate([
        '/nurse/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.PHYSIOTHERAPIST)) {
      this.util.router.navigate([
        '/physio-rehab-coordinator/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }
  }

  getPhysioFormRoutes(appointment: any) {
    // this.util.swal.fire('Coming Soon', 'Physio Form', 'info');
    if (this.user.roleName.includes(this.util.ROLES.PATIENT)) {
      this.util.router.navigate([
        '/patient/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
      this.util.router.navigate([
        '/health-advisor/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.ADMIN)) {
      this.util.router.navigate([
        '/admin/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DOCTOR)) {
      this.util.router.navigate([
        '/doctor/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.NURSE)) {
      this.util.router.navigate([
        '/nurse/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DIETITIAN)) {
      this.util.router.navigate([
        '/dietitian/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.PHYSIOTHERAPIST)) {
      this.util.router.navigate([
        '/physio-rehab-coordinator/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }
  }

  getVisitFormRoutes(appointment: any) {
    if (this.user.roleName.includes(this.util.ROLES.PATIENT)) {
      this.util.router.navigate([
        '/patient/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
      this.util.router.navigate([
        '/health-advisor/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.ADMIN)) {
      this.util.router.navigate([
        '/admin/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DOCTOR)) {
      // console.log('View visit Doctor:', appointment);

      if (
        appointment.appointmentStatus.includes('Completed') ||
        appointment.appointmentStatus.includes('Cancelled')
      ) {
        this.util.router.navigate([
          '/doctor/view/doc-visit-details/',
          this.util.adminService.encrypt(appointment.patientId),
          this.util.adminService.encrypt(appointment.visitId),
        ]);
      } else {
        this.util.router.navigate([
          '/doctor/create/doc-visit-details/',
          this.util.adminService.encrypt(appointment.patientId),
          this.util.adminService.encrypt(appointment.visitId),
        ]);
      }
    }

    if (this.user.roleName.includes(this.util.ROLES.NURSE)) {
      this.util.router.navigate([
        '/nurse/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DIETITIAN)) {
      this.util.router.navigate([
        '/dietitian/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.PHYSIOTHERAPIST)) {
      this.util.router.navigate([
        '/physio-rehab-coordinator/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }
  }

  getStatusBtnClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'btn-success';
      case 'pending':
        return 'btn-warning';
      case 'completed':
        return 'btn-info';
      case 'cancelled':
        return 'btn-danger';
      case 'scheduled':
        return 'btn-primary';
      default:
        return 'btn-secondary';
    }
  }
}
