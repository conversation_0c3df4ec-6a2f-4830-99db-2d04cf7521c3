import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import {
  BsDatepickerConfig,
  BsDatepickerDirective,
  BsDatepickerModule,
} from 'ngx-bootstrap/datepicker';
import { ToastrService } from 'ngx-toastr';
import { AdminService } from 'src/app/admin/service/admin.service';
import { SelectSearchComponent } from 'src/app/shared/select-search/select-search.component';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';

@Component({
  selector: 'app-cp-diet-consent-remark',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    BsDatepickerModule,
    SelectSearchComponent,
  ],
  templateUrl: './cp-diet-consent-remark.component.html',
  styleUrl: './cp-diet-consent-remark.component.scss',
})
export class CpDietConsentRemarkComponent implements OnInit {
  @Input() patientId: any;
  @Input() consultationId: any | null = null;
  @Input() appointmentId: any;
  @Input() isCompleted: any;
  @Input() isUpdate: boolean = false;

  patientConsenForm!: FormGroup;
  patientConsentData: any = [];

  @ViewChild('entryDatePicker') entryDatePicker!: BsDatepickerDirective;

  datepickerConfig: Partial<BsDatepickerConfig> = {
    dateInputFormat: 'DD MMM YYYY', // Change this to your preferred format
    showWeekNumbers: false, // Optional: theme
  };
  user = this.util.user;

  constructor(
    private fb: FormBuilder,
    private util: UtilFunctions,
    private toastr: ToastrService,
    private adminService: AdminService
  ) {
    this.patientConsenForm = this.fb.group({
      entryDate: [''],
      consentVerbal: [''],
      consentWritten: [''],
      dieticianRemarks: [''],
      patientQuestions: [''],
      consentType: [''],
    });
  }

  ngOnInit(): void {
    if (this.consultationId) {
      this.getPatientConsent(this.consultationId);
    }
  }

  asFormControl(control: AbstractControl | null): FormControl {
    return control as FormControl;
  }

  openCalendar(name: string): void {
    if (name == 'entryDatePicker') {
      this.entryDatePicker.show();
    }
  }

  async getPatientConsent(consultationId: any) {
    try {
      const data: any = await this.adminService.getPatientConsentById(
        Number(consultationId)
      );
      if (data?.length > 0) {
        this.patientConsentData = data[0];
        this.patientConsenForm.patchValue({
          entryDate: this.patientConsentData.entryDate
            ? new Date(this.patientConsentData.entryDate)
            : null,
          consentType: this.patientConsentData.consentType || '',
          dieticianRemarks: this.patientConsentData.dieticianRemarks || '',
          patientQuestions: this.patientConsentData.patientQuestions || '',
        });
      } else {
        this.patientConsentData = [];
      }
    } catch (error) {
      this.toastr.error('Failed to fetch Follow Up data.');
      console.error(error);
    }
  }

  // Consent Form Save Function
  savePatientConsent() {
    if (this.patientConsenForm.invalid) {
      this.toastr.error('Please fill all required fields.');
      return;
    }

    if (this.patientConsentData.length > 0) {
      const payload = {
        id: this.patientConsentData.id,
        patientId: Number(this.patientId),
        dietConsultationId: this.consultationId
          ? Number(this.consultationId)
          : 0,
        entryDate: this.entryDate.value,
        consentVerbal:
          this.consentType.value === 'Verbal'
            ? 1
            : this.consentType.value === 'Both'
            ? 1
            : 0,
        consentWritten:
          this.consentType.value === 'Written'
            ? 1
            : this.consentType.value === 'Both'
            ? 1
            : 0,
        dieticianRemarks: this.dieticianRemarks.value,
        patientQuestions: this.patientQuestions.value,
        consentType: this.consentType.value,
        createdDate: new Date(),
      };

      this.adminService.updateConsentRemark(payload).subscribe({
        next: () => {
          this.toastr.success('Patient Consent Updated successfully.');
          this.getPatientConsent(this.consultationId);
        },
        error: (err) => {
          this.toastr.error('Failed to save Patient Consent.');
          console.error(err);
        },
      });
    } else {
      const payload = {
        patientId: Number(this.patientId),
        dietConsultationId: this.consultationId
          ? Number(this.consultationId)
          : 0,
        entryDate: this.entryDate.value,
        consentVerbal:
          this.consentType.value === 'Verbal'
            ? 1
            : this.consentType.value === 'Both'
            ? 1
            : 0,
        consentWritten:
          this.consentType.value === 'Written'
            ? 1
            : this.consentType.value === 'Both'
            ? 1
            : 0,
        dieticianRemarks: this.dieticianRemarks.value,
        patientQuestions: this.patientQuestions.value,
        consentType: this.consentType.value,
        createdDate: new Date(),
      };

      this.adminService.createPatientConsent(payload).subscribe({
        next: () => {
          this.toastr.success('Patient Consent saved successfully.');
          this.getPatientConsent(this.consultationId);
          this.updateAppointmentStatus();
        },
        error: (err) => {
          this.toastr.error('Failed to save Patient Consent.');
          console.error(err);
        },
      });
    }
  }

  updateAppointmentStatus() {
    this.adminService
      .updateAppointmentOnGoingStatus(Number(this.appointmentId))
      .subscribe({
        next: () => {
          this.toastr.success('Appointment Status Updated successfully.');
        },
        error: (err) => {
          this.toastr.error('Failed to Update Appointment Status.');
          console.error(err);
        },
      });
  }

  get entryDate() {
    return this.patientConsenForm.get('entryDate') as FormControl;
  }
  get consentVerbal() {
    return this.patientConsenForm.get('consentVerbal') as FormControl;
  }
  get consentWritten() {
    return this.patientConsenForm.get('consentWritten') as FormControl;
  }
  get dieticianRemarks() {
    return this.patientConsenForm.get('dieticianRemarks') as FormControl;
  }
  get patientQuestions() {
    return this.patientConsenForm.get('patientQuestions') as FormControl;
  }
  get consentType() {
    return this.patientConsenForm.get('consentType') as FormControl;
  }
}
