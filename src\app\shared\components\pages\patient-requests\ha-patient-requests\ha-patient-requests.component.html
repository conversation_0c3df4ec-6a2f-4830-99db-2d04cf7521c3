<AdminFriendlyPageWrapper>
  <main>
    <header class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h3 class="mb-0">
          <i class="fas fa-calendar-check me-2 text-primary"></i>
          Patient Requests
        </h3>
        <small class="text-muted">
          Manage appointment requests for
          <span
            class="fw-bold"
            >{{ patientRequests.data()?.[0]?.patientName || 'Patient' }}</span
          >
        </small>
      </div>
      <div class="d-flex gap-2 justify-content-end">
        <button class="btn btn-outline-secondary rounded" (click)="back()">
          <i class="fas fa-arrow-left me-1"></i>
          Back
        </button>
      </div>
    </header>

    <!-- Loading State -->
    <LoadingSpinner
      *ngIf="patientRequests.isLoading() || patientRequests.isPending()"
    ></LoadingSpinner>

    <!-- Error State -->
    <ErrorAlert *ngIf="patientRequests.isError()"> </ErrorAlert>

    <!-- Content -->
    <div *ngIf="patientRequests.isSuccess()">
      <!-- Summary Cards -->
      <div class="row mb-4">
        <div class="col-md-4">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="card-title mb-0">Total Requests</h6>
                  <h3 class="mb-0 pt-2">
                    {{ patientRequests.data()?.length || 0 }}
                  </h3>
                </div>
                <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-4">
          <div class="card bg-warning text-dark">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="card-title mb-0">With Time Slots</h6>
                  <h3 class="mb-0 pt-2">{{ getRequestsWithTime().length }}</h3>
                </div>
                <i class="fas fa-clock fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-4">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="card-title mb-0">Pending Slots</h6>
                  <h3 class="mb-0 pt-2">
                    {{ getRequestsWithoutTime().length }}
                  </h3>
                </div>
                <i class="fas fa-hourglass-half fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Requests State -->
      <div
        *ngIf="!patientRequests.data() || patientRequests.data()?.length === 0"
        class="text-center py-5"
      >
        <div class="card bg-light">
          <div class="card-body py-5">
            <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">No Requests Found</h4>
            <p class="text-muted">
              This patient hasn't made any appointment requests yet.
            </p>
          </div>
        </div>
      </div>

      <!-- Requests with Time Slots -->
      <div *ngIf="getRequestsWithTime().length > 0" class="mb-5">
        <div class="d-flex align-items-center mb-3">
          <h4 class="mb-0">
            <i class="fas fa-clock text-success me-2"></i>
            Scheduled Time Slots
          </h4>
          <span class="badge bg-success ms-2">{{
            getRequestsWithTime().length
          }}</span>
        </div>

        <div class="row">
          <div
            *ngFor="let request of getRequestsWithTime()"
            class="col-lg-6 col-xl-4 mb-4"
          >
            <div class="card h-100 border-success shadow-sm">
              <div class="card-header bg-success bg-opacity-10 border-success">
                <div class="d-flex justify-content-between align-items-center">
                  <span class="fw-bold text-success">
                    <i class="fas fa-user-clock me-1"></i>
                    Slot {{ request.slotOrder }}
                  </span>
                  <span class="badge {{ getStatusBadgeClass(request.status) }}">
                    {{ request.status | titlecase }}
                  </span>
                </div>
              </div>

              <div class="card-body">
                <div class="mb-3">
                  <h6 class="card-title mb-1">
                    <i class="fas fa-user me-2 text-primary"></i>
                    {{ request.patientName }}
                  </h6>
                  <small class="text-muted">ID: {{ request.patientId }}</small>
                </div>

                <div class="row g-3">
                  <div class="col-12">
                    <div class="d-flex align-items-center">
                      <i class="fas fa-calendar-day text-info me-2"></i>
                      <div>
                        <small class="text-muted d-block">Date</small>
                        <span class="fw-medium">{{
                          formatDate(request.requestedDate)
                        }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="col-12">
                    <div class="d-flex align-items-center">
                      <i class="fas fa-clock text-warning me-2"></i>
                      <div>
                        <small class="text-muted d-block">Time</small>
                        <span class="fw-medium text-primary">{{
                          formatTime(request.preferredDatetime)
                        }}</span>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="request.purpose" class="col-12">
                    <div class="d-flex align-items-start">
                      <i
                        class="fas fa-notes-medical text-secondary me-2 mt-1"
                      ></i>
                      <div>
                        <small class="text-muted d-block">Purpose</small>
                        <span class="fw-medium">{{ request.purpose }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="card-footer bg-transparent">
                <div class="d-flex gap-2">
                  <button
                    class="btn btn-sm btn-outline-success flex-fill"
                    [disabled]="request.status === 'confirmed'"
                  >
                    <i class="fas fa-check me-1"></i>
                    {{
                      request.status === "confirmed" ? "Confirmed" : "Confirm"
                    }}
                  </button>
                  <button
                    class="btn btn-sm btn-outline-danger"
                    [disabled]="request.status === 'cancelled'"
                  >
                    <i class="fas fa-times me-1"></i>
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Requests without Time Slots -->
      <div *ngIf="getRequestsWithoutTime().length > 0" class="mb-4">
        <div class="d-flex align-items-center mb-3">
          <h4 class="mb-0">
            <i class="fas fa-hourglass-half text-warning me-2"></i>
            Pending Time Assignment
          </h4>
          <span class="badge bg-warning text-dark ms-2">{{
            getRequestsWithoutTime().length
          }}</span>
        </div>

        <div class="row">
          <div
            *ngFor="let request of getRequestsWithoutTime()"
            class="col-lg-6 col-xl-4 mb-4"
          >
            <div class="card h-100 border-warning shadow-sm">
              <div class="card-header bg-warning bg-opacity-10 border-warning">
                <div class="d-flex justify-content-between align-items-center">
                  <span class="fw-bold text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Slot {{ request.slotOrder }}
                  </span>
                  <span class="badge {{ getStatusBadgeClass(request.status) }}">
                    {{ request.status | titlecase }}
                  </span>
                </div>
              </div>

              <div class="card-body">
                <div class="mb-3">
                  <h6 class="card-title mb-1">
                    <i class="fas fa-user me-2 text-primary"></i>
                    {{ request.patientName }}
                  </h6>
                  <small class="text-muted">ID: {{ request.patientId }}</small>
                </div>

                <div class="row g-3">
                  <div class="col-12">
                    <div class="d-flex align-items-center">
                      <i class="fas fa-calendar-day text-info me-2"></i>
                      <div>
                        <small class="text-muted d-block">Requested Date</small>
                        <span class="fw-medium">{{
                          formatDate(request.requestedDate)
                        }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="col-12">
                    <div class="alert alert-warning py-2 mb-0">
                      <i class="fas fa-info-circle me-2"></i>
                      <small>No specific time requested</small>
                    </div>
                  </div>

                  <div *ngIf="request.purpose" class="col-12">
                    <div class="d-flex align-items-start">
                      <i
                        class="fas fa-notes-medical text-secondary me-2 mt-1"
                      ></i>
                      <div>
                        <small class="text-muted d-block">Purpose</small>
                        <span class="fw-medium">{{ request.purpose }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="card-footer bg-transparent">
                <div class="d-flex gap-2">
                  <button class="btn btn-sm btn-primary flex-fill">
                    <i class="fas fa-clock me-1"></i>
                    Assign Time
                  </button>
                  <button class="btn btn-sm btn-outline-danger">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</AdminFriendlyPageWrapper>
