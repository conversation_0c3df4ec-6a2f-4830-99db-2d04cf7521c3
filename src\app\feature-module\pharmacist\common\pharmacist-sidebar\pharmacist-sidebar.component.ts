import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { CommonService } from 'src/app/shared/common/common.service';
import { routes } from 'src/app/shared/routes/routes';
import { UserAccountsService } from 'src/app/shared/services/user-accounts.service';
import { UserService } from 'src/app/shared/services/user.service';
import { DoctorService } from '../../../doctor/services/doctor.service';

@Component({
  selector: 'pharma-sidebar',
  standalone: false,
  templateUrl: './pharmacist-sidebar.component.html',
  styleUrl: './pharmacist-sidebar.component.scss',
})
export class PharmacistSidebarComponent {
  public routes = routes;
  public base = '';
  public page = '';
  public last = '';

  userData: any | null = null;
  userId: string = '';
  user: any;
  today = new Date();

  constructor(
    private common: CommonService,
    private router: Router,
    private toastr: ToastrService,
    private authService: AuthService,
    private userService: UserService,
    private userAccountService: UserAccountsService,
    private doctorService: DoctorService
  ) {
    this.common.base.subscribe((res: string) => {
      this.base = res;
    });
    this.common.page.subscribe((res: string) => {
      this.page = res;
    });
    this.common.last.subscribe((res: string) => {
      this.last = res;
    });
    console.log('base', this.base);
    console.log('page', this.page);
    console.log('last', this.last);

    this.userData = this.authService.getDataFromSession('user');
    this.userId = this.userData['userId'];
    this.getDoctorDetailsById(this.userId);
  }

  async getUserDetails1(userId: string) {
    try {
      const res: any = await this.userService.getUserByUserId(userId);
      console.log('--User response _sidebar : ', res);
      this.user = res[0];
    } catch (error) {
      console.log('--Error while fetching user details');
      this.toastr.error('Error while getting user details');
    }
  }

  async getDoctorDetailsById(docId: string) {
    if (!docId) {
      this.toastr.error('Doctor Id Invalid', '', { timeOut: 3000 });
      return;
    }

    try {
      const patientData: any = await this.doctorService.getDoctorById(
        docId,
        this.userData['facilityId']
      );

      const docExperience: any =
        await this.doctorService.getDoctorTotalExperience(docId);
      if (patientData.length > 0) {
        this.user = patientData[0];

        if (!patientData[0].photoLink) {
          console.warn('No photoLink available for the doctor.');
          return;
        }
        const pathname = this.getNormalizedPathname(patientData[0].photoLink);
        console.log('Normalized Path:', pathname);
        const securedUrl = await this.fetchSecuredUrl(pathname);
        this.user.photoLinkUrl = securedUrl;

        if (docExperience) {
          this.user.doctorExperience = docExperience[0].total;
        }

        console.log('--User : ', this.user);

        // this.doctorService.setCurrentDoctorDetails(patientData[0]);
        // const globalUser = this.doctorService.getCurrentDoctorDetails();
        // console.log('--Current user detail sidebar : ', globalUser());
        // this.user = globalUser();
      } else {
        this.toastr.error('No doctor data found', '', { timeOut: 3000 });
      }
    } catch (error) {
      console.error('Error fetching doctor details:', error);
      this.toastr.error('Error fetching doctor details', '', {
        timeOut: 3000,
      });
    }
  }

  convertMonthsToYearsMonths(totalMonths: number) {
    const years = Math.floor(totalMonths / 12);
    const months = totalMonths % 12;

    if (years === 0 && months === 0) {
      return '0 months';
    } else if (years === 0) {
      return `${months} month${months !== 1 ? 's' : ''}`;
    } else if (months === 0) {
      return `${years} year${years !== 1 ? 's' : ''}`;
    } else {
      return `${years} year${years !== 1 ? 's' : ''}, ${months} month${
        months !== 1 ? 's' : ''
      }`;
    }
  }

  convertDaysToYearsMonthsDays(totalDays: number): string {
    const years = Math.floor(totalDays / 365);
    const remainingDaysAfterYears = totalDays % 365;

    const months = Math.floor(remainingDaysAfterYears / 30);
    const days = remainingDaysAfterYears % 30;

    const parts = [];

    if (years > 0) parts.push(`${years} year${years !== 1 ? 's' : ''}`);
    if (months > 0) parts.push(`${months} month${months !== 1 ? 's' : ''}`);
    if (days > 0) parts.push(`${days} day${days !== 1 ? 's' : ''}`);

    return parts.length ? parts.join(', ') : '0 days';
  }

  logout() {
    this.userData = null;
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/login']);
    this.toastr.success('Logged out successfully!');
  }

  async fetchSecuredUrl(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.userAccountService.getSecureImgUrl({ fileUrl: url }).subscribe({
        next: (response: any) => {
          resolve(response['url']);
        },
        error: (error: any) => {
          this.toastr.error('', error.error.message, { timeOut: 3000 });
          reject(error);
        },
      });
    });
  }

  private getFileType(filePath: string): string {
    const PDF_EXTENSION = '.pdf';
    return filePath.endsWith(PDF_EXTENSION) ? 'pdf' : 'image';
  }

  private getNormalizedPathname(filePath: string): string {
    if (!filePath) {
      console.error('Invalid file path:', filePath);
      return '';
    }

    try {
      const url = new URL(filePath, window.location.origin); // Handle relative paths
      return url.pathname.replace(/^\/+/, '');
    } catch (error) {
      console.error('Error parsing URL:', error, 'File Path:', filePath);
      return '';
    }
  }

  calculateAge1(dob: string): string {
    const birthDate = new Date(dob);
    const today = new Date();

    let years = today.getFullYear() - birthDate.getFullYear();
    let months = today.getMonth() - birthDate.getMonth();

    if (months < 0) {
      years--;
      months += 12;
    }

    return `${years} Years ${months.toString().padStart(2, '0')} Months`;
  }

  calculateAge(dob: string): string {
    const birthDate = new Date(dob);
    const today = new Date();

    let years = today.getFullYear() - birthDate.getFullYear();
    let months = today.getMonth() - birthDate.getMonth();
    let days = today.getDate() - birthDate.getDate();

    if (days < 0) {
      months--;
      const lastDayOfMonth = new Date(
        today.getFullYear(),
        today.getMonth(),
        0
      ).getDate();
      days += lastDayOfMonth;
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    return `${years} Years ${months.toString().padStart(2, '0')} Months ${days
      .toString()
      .padStart(2, '0')} Days`;
  }
}
