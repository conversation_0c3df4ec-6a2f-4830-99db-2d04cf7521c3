import { CommonModule, Location } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  injectMutation,
  injectQuery,
} from '@tanstack/angular-query-experimental';
import moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { AdminService } from 'src/app/admin/service/admin.service';
import { RecordingState } from 'src/app/feature-module/doctor/doc-clinical-visit/doc-clinical-complaints/doc-clinical-complaints.component';
import { DoctorService } from 'src/app/feature-module/doctor/services/doctor.service';
import { HealthAdvisorService } from 'src/app/feature-module/health-advisor/service/health-advisor.service';
import { NurseService } from 'src/app/feature-module/nurse/services/nurse.service';
import { PatientService } from 'src/app/feature-module/patient/patient.service';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { SubmitBtnComponent } from 'src/app/shared/components/re-use/submit-btn/submit-btn.component';
import { ErrorAlertComponent } from 'src/app/shared/error-alert/error-alert.component';
import { FormFieldComponent } from 'src/app/shared/form-field/form-field.component';
import { LoadingSpinnerComponent } from 'src/app/shared/loading-spinner/loading-spinner.component';
import { SearchableSelectComponent } from 'src/app/shared/searchable-select/searchable-select.component';
import { UserAccountsService } from 'src/app/shared/services/user-accounts.service';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-select-phlebotomist-slot-and-book',
  imports: [
    CommonModule,
    LoadingSpinnerComponent,
    ErrorAlertComponent,
    SubmitBtnComponent,
    SearchableSelectComponent,
    FormFieldComponent,
  ],
  templateUrl: './select-phlebotomist-slot-and-book.component.html',
  styleUrl: './select-phlebotomist-slot-and-book.component.scss',
})
export class SelectPhlebotomistSlotAndBookComponent {
  authService = inject(AuthService);
  adminService = inject(AdminService);
  haService = inject(HealthAdvisorService);
  util = inject(UtilFunctions);
  router = inject(Router);
  activeRoute = inject(ActivatedRoute);
  location = inject(Location);
  userAccService = inject(UserAccountsService);
  doctorService = inject(DoctorService);
  patientService = inject(PatientService);
  toastr = inject(ToastrService);
  nurseService = inject(NurseService);

  user = this.authService.getDataFromSession('user');
  patientId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['patientId']
  );
  activityId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['activityId']
  );
  packageId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['packageId']
  );

  doctorId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['doctorId']
  );
  facilityId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['facilityId']
  );
  deptId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['deptId']
  );
  ppId = this.adminService.decrypt(this.activeRoute.snapshot.params['ppId']);

  selectedDate = signal(new Date());
  selectedAppointmentType = signal('sample');
  selectedSlot = signal<any>(null);
  selectedVisitType = signal<any>(null);
  visitTypeOptions = this.util.VISIT_TYPES;
  isAppointmentBookingLoading = signal(false);

  appointmentForm!: FormGroup;

  appointmentTypes: any[] = [
    {
      name: 'Blood Sample Collection',
      value: 'sample',
    },
  ];

  appointmentServiceTypes: any[] = [
    {
      name: 'Instant',
      id: 'Instant',
    },
    {
      name: 'Scheduled',
      id: 'Scheduled',
    },
    {
      name: 'Face-to-Face',
      id: 'Face-to-Face',
    },
    {
      name: 'Lab Tests',
      id: 'Lab Tests',
    },
  ];

  constructor() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    this.initAppointmentForm();
  }

  // Queries
  doctorDetails = injectQuery(() => ({
    queryKey: [
      'doctor-details',
      this.doctorId,
      this.selectedDate(),
      this.facilityId,
      this.deptId,
    ],
    queryFn: async () => {
      const res: any = await this.haService.getPhlebototmistDetails({
        doctorId: this.doctorId,
        facilityId: this.facilityId,
        deptId: this.deptId,
        availableDate: moment(this.selectedDate()).format('YYYY-MM-DD'),
        todayDateTime: moment(this.selectedDate()).format('YYYY-MM-DD HH:mm'),
      });

      await Promise.all(
        res.map(async (doc: any) => {
          if (doc.image) {
            const pathname = this.util.getNormalizedPathname(doc.image);
            const securedUrl = await this.util.fetchSecuredUrl(pathname);
            doc.image = securedUrl;
          } else {
            doc.image = this.util.getNormalAvatar();
          }
        })
      );

      console.log('Doctor Details : ', res);

      return res[0] ?? {};
    },
    refetchOnWindowFocus: false,
  }));

  doctorSlots = injectQuery(() => ({
    queryKey: [
      'doctor-slots',
      this.doctorId,
      this.selectedDate(),
      this.selectedAppointmentType() ?? 'clinic',
      this.facilityId,
      this.deptId,
    ],
    queryFn: async () => {
      const res: any = await this.haService.getDoctorSlots({
        doctorId: Number(this.doctorId),
        facilityId: Number(this.facilityId),
        departmentId: Number(this.deptId),
        appointmentType: this.selectedAppointmentType(),
        availableDate: moment(this.selectedDate()).format('YYYY-MM-DD'),
        todayDateTime: moment(this.selectedDate()).format('YYYY-MM-DD HH:mm'),
      });
      return res;
    },
    refetchOnWindowFocus: false,
  }));

  patientDetails = injectQuery(() => ({
    queryKey: ['patient-details', this.patientId],
    queryFn: async () => {
      const res: any = await this.userAccService.getPatientById(this.patientId);
      await Promise.all(
        res.map(async (user: any) => {
          if (user.photoLink) {
            const pathname = this.util.getNormalizedPathname(user.photoLink);
            const securedUrl = await this.util.fetchSecuredUrl(pathname);
            user.photoLink = securedUrl;
          } else {
            user.photoLink = this.util.getNormalAvatar();
          }
        })
      );
      return res[0];
    },
    refetchOnWindowFocus: false,
  }));

  patientSlots = injectQuery(() => ({
    queryKey: ['patient-slots', this.patientId, this.selectedDate()],
    queryFn: async () => {
      const res: any = await this.haService.getPatientPendingSlots({
        patientId: this.patientId,
        requestedDate: moment(this.selectedDate()).format('YYYY-MM-DD'),
      });
      return res;
    },
    refetchOnWindowFocus: false,
  }));

  // Mutations

  createVisitMutation = injectMutation(() => ({
    mutationFn: (payload: any) =>
      this.nurseService.createVisitAfterAppointment(payload),
    onSuccess: (res: any) => {
      if (res && res.error) {
        Swal.fire('Error creating visit', '', 'error');
        this.isAppointmentBookingLoading.set(false);
        return;
      }

      // this.toastr.success('Visit created successfully');
      // this.router.navigateByUrl('/patient/appointments');
      Swal.fire('Appointment booked successfully', '', 'success');
      this.isAppointmentBookingLoading.set(false);
      this.afterSubmissionNavigation();
    },
    onError: (error: any) => {
      this.toastr.error('Error creating visit');
    },
  }));

  createAppointmentConfirmationMutation = injectMutation(() => ({
    mutationFn: (payload: any) =>
      this.patientService.createAppointmentConfirmation(payload),
    onSuccess: (res: any) => {
      if (res && res.error) {
        Swal.fire('Error creating appointment confirmation', '', 'error');
        this.isAppointmentBookingLoading.set(false);
        return;
      }

      // this.toastr.success('Appointment confirmation created successfully');
      // this.router.navigateByUrl('/patient/appointments');
      Swal.fire('Appointment booked successfully', '', 'success');
      this.isAppointmentBookingLoading.set(false);
      this.afterSubmissionNavigation();
    },
    onError: (error: any) => {
      this.toastr.error('Error creating appointment confirmation');
    },
  }));

  // Functions

  initAppointmentForm() {
    this.appointmentForm = new FormGroup({
      visitType: new FormControl('Lab Tests', Validators.required),
      complaintTitle: new FormControl(''),
      complaintDescription: new FormControl(''),
      appointmentServiceType: new FormControl('Lab Tests', Validators.required),
    });
  }

  back = () => {
    if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
      this.location.back();
      return;
    }

    this.location.back();
  };

  formatNextAvailable(dateTime: string): string {
    return moment(dateTime).format('MMM DD, HH:mm');
  }

  changeAppointmentType(type: string) {
    this.selectedAppointmentType.set(type);
  }

  selectNextDate() {
    this.selectedDate.set(moment(this.selectedDate()).add(1, 'days').toDate());
  }

  selectPreviousDate() {
    this.selectedDate.set(
      moment(this.selectedDate()).subtract(1, 'days').toDate()
    );
  }

  selectToday() {
    this.selectedDate.set(new Date());
  }

  isToday(date: Date): boolean {
    const today = new Date();
    // Set time to 00:00:00 for accurate comparison
    today.setHours(0, 0, 0, 0);
    const checkDate = new Date(date);
    checkDate.setHours(0, 0, 0, 0);
    return checkDate <= today;
  }

  onSlotClick = (slot: any) => {
    console.log('Slot : ', slot);
    this.selectedSlot.set(slot);
  };

  onSelectVisitType(visitType: any) {
    this.selectedVisitType.set(visitType);
  }

  async onBookAppointment() {
    this.isAppointmentBookingLoading.set(true);
    console.log('Book Appointment : ', this.appointmentForm.value);
    await this.confirmAppointmentFn();
    this.isAppointmentBookingLoading.set(false);
  }

  confirmAppointmentFn = async () => {
    let payload = {};

    this.isAppointmentBookingLoading.set(true);

    if (
      this.selectedAppointmentType() === 'clinic' ||
      this.selectedAppointmentType() === 'hospital' ||
      this.selectedAppointmentType() === 'nurse' ||
      this.selectedAppointmentType() === 'sample'
    ) {
      payload = {
        timeSlotId: this.selectedSlot().timeSlotId,
        isBooked: true,
        optService: '',
        modifiedDate: new Date().toISOString(),
        cost: +this.doctorDetails.data().cost,
        locationName: this.doctorDetails.data().location,
        consultationFee: this.doctorDetails.data().cost,
      };
    }

    if (this.selectedAppointmentType() === 'online') {
      payload = {
        timeSlotId: this.selectedSlot().timeSlotId,
        isBooked: true,
        optService: '',
        modifiedDate: new Date().toISOString(),
        cost: 149,
      };
    }

    console.log('Book Appointment Payload : ', payload);

    const createAppointmentPayload = {
      patientId: this.patientDetails.data().userId,
      doctorId: this.doctorDetails.data().id,
      facilityId: this.doctorDetails.data().facilityId,
      appointmentDate: moment(this.selectedDate()).format('YYYY-MM-DD'),
      appointmentTime: this.selectedSlot().startTime,
      timeSlotId: this.selectedSlot().timeSlotId,
      appointmentTypeId: this.selectedAppointmentType(),
      consultationFee: +this.doctorDetails.data().cost,
      purpose: '',
      symptoms: '',
      status: 'Booked', //('Scheduled': string,'Confirmed': string,'Completed': string,'Cancelled': string,'No-show': string,'Rescheduled')
      notes: '',
      location:
        this.selectedAppointmentType() === 'clinic' ||
        this.selectedAppointmentType() === 'hospital' ||
        this.selectedAppointmentType() === 'nurse' ||
        this.selectedAppointmentType() === 'sample'
          ? this.doctorDetails.data().location
          : 'online',
      isFirstVisit: 0, // 0 or 1
      durationMinutes: '15',
      bookedBy: `${this.user.roleDescription} - ${this.user.userId}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),

      visit: {
        visitType: this.visitType.value,
        visitStatus: 'Scheduled',
        visitDate: moment(this.selectedDate()).format('YYYY-MM-DD'),
      },
      complaint: {
        complaintTitle: this.complaintTitle.value,
        complaintDescription: this.complaintDescription.value,
        onsetDate: moment(new Date()).format('YYYY-MM-DD'),
      },
      meetLink: '',

      careActivity: null,
      carePlanId: 0,
      monthNumber: 0,
      activityId: this.activityId,
      packageId: this.packageId,
      purchasePackageId: this.ppId,

      userRoleName: this.doctorDetails.data().roleName,
      departmentId: this.deptId,
      appointmentService: this.appointmentServiceType.value,
    };

    console.log('Create Appointment Payload : ', createAppointmentPayload);

    try {
      const res: any = await this.patientService.bookAppointment(payload);
      console.log('Book Appointment Response : ', res);

      if (res && res.error) {
        Swal.fire('Appointment not created, Try Again', '', 'error');
        return;
      }

      if (this.selectedAppointmentType() === 'online') {
        try {
          const getMeetLinkRes: any = await this.patientService.getMeetLink(
            `U${this.patientDetails.data().userId}U${
              this.doctorDetails.data().id
            }`,
            'Online-Consultation'
          );

          createAppointmentPayload.meetLink = getMeetLinkRes.meetLink;
        } catch (error) {
          console.log('Error while getting meet link : ', error);
        }
      }

      try {
        const res: any = await this.patientService.createPatientAppointment(
          createAppointmentPayload
        );

        console.log('Create Appointmet res : ', res);

        if (res && res.error) {
          Swal.fire('Appointment not created, Try Again', '', 'error');
          return;
        }

        const newVisitPayload = {
          patientId: +this.patientId,
          activityId: 0,
          visitType: this.visitType.value,
          visitStatus: 'Scheduled',
          facility: this.doctorDetails.data().facilityId,
          consultingDoctorId: this.doctorDetails.data().id,
          referringDoctorId: null,
          externalReferrerName: null,
          externalReferrerHospital: null,
          visitDate: moment(this.selectedDate()).format('YYYY-MM-DD'),
          appointmentId: res.id,
          carePlanId: 0,
          monthNumber: 0,
          careActivity: null,
          reportedBy: this.user.roleDescription,
          reportedId: this.user.userId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        const patientAppointmentConfirmationPayload = {
          appointmentId: res.id,
          patientId: +this.patientId,
          specialistId: +this.patientId,
          specialistRole: 'Patient',
          confirmedDate: new Date().toISOString(),
          confirmedBy: this.user.userId + ' - ' + this.user.roleDescription,
          status: this.patientId == this.user.userId ? 'Confirmed' : 'Pending',
          notes: '',
        };

        const specialistAppointmentConfirmationPayload = {
          appointmentId: res.id,
          patientId: +this.patientId,
          specialistId: +this.doctorDetails.data().id,
          specialistRole: 'Phlebotomist',
          confirmedDate: new Date().toISOString(),
          confirmedBy: this.user.userId + ' - ' + this.user.roleDescription,
          status:
            this.doctorDetails.data().id == this.user.userId
              ? 'Confirmed'
              : 'Pending',
          notes: '',
        };

        const healthAdvisorAppointmentConfirmationPayload = {
          appointmentId: res.id,
          patientId: +this.patientId,
          specialistId: +this.user.userId,
          specialistRole: 'Health Advisor',
          confirmedDate: new Date().toISOString(),
          confirmedBy: this.user.userId + ' - ' + this.user.roleDescription,
          status: 'Confirmed',
          notes: '',
        };

        this.createAppointmentConfirmationMutation.mutate(
          patientAppointmentConfirmationPayload
        );

        this.createAppointmentConfirmationMutation.mutate(
          specialistAppointmentConfirmationPayload
        );

        if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
          this.createAppointmentConfirmationMutation.mutate(
            healthAdvisorAppointmentConfirmationPayload
          );
        }

        Swal.fire('Appointment booked successfully', '', 'success');

        // Create Phelbotomist Task
        try {
          const task: any = await this.patientService.createPhlebotomistTask({
            patientId: this.patientDetails.data().userId,
            phlebotomistId: this.user.userId,
            appointmentId: res.id,
            labId: 1,
            collectionStatus: 'Pending',
            collectionTime: new Date(),
            deliveryStatus: 'Pending',
            deliveryTime: new Date(),
            notes: '',
            createdDate: new Date(),
          });
          if (task && task.error) {
            Swal.fire('Task not created, Try Again', '', 'error');
            return;
          }
        } catch (error) {
          console.error('Error creating a Task:', error);
          this.toastr.error('Error creating a Task', '', {
            timeOut: 3000,
          });
        }
        this.afterSubmissionNavigation();
      } catch (error) {
        console.error('Error creating a appointment:', error);
        this.toastr.error('Error creating a appointment', '', {
          timeOut: 3000,
        });
      } finally {
        this.isAppointmentBookingLoading.set(false);
      }
    } catch (error: any) {
      this.toastr.error('Error while booking slot', '', { timeOut: 3000 });
      console.log('Error while booking slot : ', error);
    } finally {
      // Swal.fire('Slot booked successfully', '', 'success');
      this.isAppointmentBookingLoading.set(false);
    }

    this.isAppointmentBookingLoading.set(false);
  };

  afterSubmissionNavigation() {
    if (this.user.roleName.includes(this.util.ROLES.PATIENT)) {
      this.router.navigate(['/patient/dashboard']);
      return;
    }

    if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
      this.router.navigate([
        '/health-advisor/lab-tests/',
        this.adminService.encrypt(this.activityId),
        this.adminService.encrypt(this.patientId),
      ]);
      return;
    }
  }

  // Audio to text

  loading: boolean = false;
  localAudioFileUrl = signal<string>('');
  audioUrl: string = '';
  transcription: string = '';
  isRecording: boolean = false;
  mediaRecorder!: MediaRecorder;
  audioChunks: BlobPart[] = [];
  selectedMicField: string = 'complaintTitle';

  recordingState: RecordingState = {
    complaintTitle: { isRecording: false, loading: false },
    complaintDescription: { isRecording: false, loading: false },
  };

  startRecording(field: string) {
    this.selectedMicField = field;
    navigator.mediaDevices
      .getUserMedia({ audio: true })
      .then((stream) => {
        this.mediaRecorder = new MediaRecorder(stream);
        this.audioChunks = [];

        this.mediaRecorder.ondataavailable = (event) => {
          this.audioChunks.push(event.data);
        };

        this.mediaRecorder.start();
        this.recordingState[field].isRecording = true;
      })
      .catch((error) => {
        console.error('Error accessing microphone:', error);
      });
  }

  stopRecording(field: string) {
    if (this.mediaRecorder && this.recordingState[field].isRecording) {
      this.mediaRecorder.stop();
      this.recordingState[field].isRecording = false;

      this.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
        if (audioBlob.size > 0) {
          this.localAudioFileUrl.set(URL.createObjectURL(audioBlob));
          this.uploadAudio(field, audioBlob);
        } else {
          console.error('Recorded audio blob is empty');
        }
      };
    }
  }

  uploadAudio(field: string, blob: Blob) {
    this.recordingState[field].loading = true;
    const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
    this.sendAudioForTranscription(audioBlob, field);
  }

  sendAudioForTranscription(audioBlob: Blob, field: string) {
    const file = new File([audioBlob], 'recording.wav', { type: 'audio/wav' });

    this.doctorService.transcribeAudioWithResource(file).subscribe(
      ({ transcription, audio }) => {
        const text = this.util.capitalizeFirstLetter(transcription.text);

        if (field === 'complaintTitle') {
          this.complaintTitle.setValue(text);
        } else if (field === 'complaintDescription') {
          this.complaintDescription.setValue(text);
        }

        this.audioUrl = URL.createObjectURL(audio);
        this.recordingState[field].loading = false;
      },
      (error) => {
        console.error('Error during transcription:', error);
        this.recordingState[field].loading = false;
      }
    );
  }

  startLoading() {
    this.loading = true;
  }
  stopLoading() {
    this.loading = false;
  }

  setTranscription(text: string) {
    text = this.util.capitalizeFirstLetter(text);
    switch (this.selectedMicField) {
      case 'complaintTitle':
        this.complaintTitle.setValue(text);
        this.selectedMicField = '';
        break;
      case 'complaintDescription':
        this.complaintDescription.setValue(text);
        this.selectedMicField = '';
        break;
    }
  }

  // Getters
  get visitType() {
    return this.appointmentForm.get('visitType') as FormControl;
  }

  get complaintTitle() {
    return this.appointmentForm.get('complaintTitle') as FormControl;
  }

  get complaintDescription() {
    return this.appointmentForm.get('complaintDescription') as FormControl;
  }

  get appointmentServiceType() {
    return this.appointmentForm.get('appointmentServiceType') as FormControl;
  }

  getAppointmentNameByValue(value: string) {
    return this.appointmentTypes.find((type) => type.value === value)?.name;
  }
}
