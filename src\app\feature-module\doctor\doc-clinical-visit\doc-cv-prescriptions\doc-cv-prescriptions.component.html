<div class="d-flex justify-content-between align-items-center">
  <h5 class="mb-0">Prescriptions</h5>
  <button
    class="btn btn-primary rounded"
    type="button"
    (click)="isAddFormActive = true"
    *ngIf="!isAddFormActive"
  >
    Add Prescription
  </button>
  <div class="d-flex gap-2" *ngIf="isAddFormActive">
    <button
      class="btn btn-danger rounded"
      type="button"
      (click)="isAddFormActive = false"
      *ngIf="isAddFormActive"
    >
      Cancel
    </button>

    <!-- <button
      class="btn btn-primary rounded"
      type="button"
      *ngIf="isAddFormActive"
    >
      Add Medicine
    </button> -->

    <button
      class="btn btn-success rounded"
      type="button"
      *ngIf="isAddFormActive && addedMedicines.length > 0"
      (click)="
        updatePresStatus ? updatePrescriptionSubmitFn() : addPrescription()
      "
    >
      {{ updatePresStatus ? "Update Prescription" : "Save Prescription" }}
    </button>
  </div>
</div>

<!-- Record Card -->

<div *ngIf="!isAddFormActive">
  <LoadingSpinner [isLoading]="prescriptionList.isPending()"></LoadingSpinner>

  <ErrorAlert
    [error]="prescriptionList.error().message"
    *ngIf="prescriptionList.isError()"
  >
  </ErrorAlert>

  <div
    class="container-fluid mt-4"
    *ngIf="
      !isAddFormActive &&
      prescriptionList.isSuccess() &&
      prescriptionList.data()
    "
  >
    <div class="row g-3">
      <div class="col-md-6 col-lg-4" *ngFor="let record of uniquePrescriptions">
        <div class="card shadow-sm h-100">
          <div class="card-body">
            <ul class="list-group list-group-flush mb-3">
              <li class="list-group-item" *ngIf="record.prescribedByName">
                <strong> Prescribed By: </strong>
                {{ record.prescribedByName ? record.prescribedByName : "N/A" }}
              </li>
              <li class="list-group-item" *ngIf="record.prescriptionDate">
                <strong> Prescription Date: </strong>
                {{
                  record.prescriptionDate
                    ? (record.prescriptionDate | date : "dd MMM yyyy")
                    : "N/A"
                }}
              </li>
            </ul>

            <div class="d-flex justify-content-end gap-2">
              <button
                class="btn btn-outline-primary btn-sm"
                (click)="toggleView(record)"
              >
                <i class="bi bi-eye"></i> View
              </button>
              <!-- <button
                class="btn btn-outline-primary btn-sm"
                (click)="updatePrescriptionFn(record)"
              >
                <i class="bi bi-pencil-square"></i> Edit
              </button>
              <button
                class="btn btn-outline-danger btn-sm"
                (click)="toggleDeleteModal(record)"
              >
                <i class="bi bi-trash"></i> Delete
              </button> -->
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 text-center" *ngIf="uniquePrescriptions.length === 0">
        <div>No Records Found</div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="isAddFormActive" class="pt-2 card p-4 mt-3">
  <div class="row g-3">
    <!-- Prescribed By -->
    <div class="col-6">
      <div class="form-group">
        <label for="doctorName" class="form-label"> Prescribed By </label>

        <p class="m-0">
          <b>Dr. {{ findCurrentUserFromUsers().name }}</b>
        </p>
      </div>
    </div>

    <!-- Prescription Date -->
    <div class="col-md-6">
      <label class="form-label"> Prescription Date </label>

      <p class="m-0">
        <b>{{ today | date : "dd MMM yyyy" }}</b>
      </p>
    </div>
  </div>

  <!-- Added Medicines -->
  <div class="pt-2">
    <label class="form-label" *ngIf="addedMedicines.length > 0">
      Prescriped Medicines
    </label>
    <ol *ngIf="addedMedicines.length > 0">
      <li
        *ngFor="let med of addedMedicines; let i = index"
        class="p-2 border-bottom d-flex justify-content-between"
      >
        <span>
          {{ i + 1 }}. <b>{{ med.medicineName }} </b>- {{ med.strength }} -
          {{ med.frequency }} - {{ med.duration }} days
        </span>
        <div class="d-flex gap-2">
          <button
            class="btn btn-outline-success btn-sm"
            (click)="editMedicine(med)"
          >
            <i class="bi bi-pencil-square"></i> Edit
          </button>
          <button
            class="btn btn-outline-danger btn-sm"
            (click)="addedMedicines.splice(i, 1)"
          >
            <i class="bi bi-trash"></i> Remove
          </button>
        </div>
      </li>
    </ol>
  </div>

  <form [formGroup]="prescriptionForm" autocomplete="off" class="row g-3 pt-2">
    <!-- medicineName -->
    <div class="col-lg-3 col-md-6">
      <div class="form-wrap">
        <label class="col-form-label" for="medicineName">
          Medicine Name <span class="text-danger">*</span>
        </label>
        <FormField
          [control]="medicineName"
          placeholder="e.g., Paracetamol"
          name="medicineName"
          [customErrors]="{ required: 'Medicine name is required' }"
        ></FormField>
      </div>
    </div>

    <!-- strength -->
    <div class="col-lg-3 col-md-6">
      <div class="form-wrap">
        <label class="col-form-label" for="strength">
          Strength <span class="text-danger">*</span>
        </label>
        <FormField
          [control]="strength"
          placeholder="e.g., 500mg"
          name="strength"
          [customErrors]="{ required: 'Strength is required' }"
        ></FormField>
      </div>
    </div>

    <!-- duration -->
    <div class="col-lg-3 col-md-6">
      <div class="form-wrap">
        <label class="col-form-label" for="duration">
          Duration <span class="text-danger">*</span>
          <small>(In days)</small>
        </label>
        <FormField
          [control]="duration"
          type="phone"
          placeholder="e.g., 3 days"
          name="duration"
          [customErrors]="{ required: 'Duration is required' }"
        ></FormField>
      </div>
    </div>

    <!-- frequency -->
    <div class="col-lg-3 col-md-6">
      <div class="form-wrap">
        <label class="col-form-label" for="frequency">
          Frequency <span class="text-danger">*</span>
        </label>
        <FormField
          [control]="frequency"
          placeholder="e.g., Twice a day"
          name="frequency"
          [customErrors]="{ required: 'Frequency is required' }"
        ></FormField>
      </div>

      <div class="col-12 d-flex justify-content-end pt-3">
        <button type="reset" class="btn btn-secondary me-2">Clear</button>
        <button
          type="button"
          class="btn btn-primary"
          (click)="submitForm()"
          [disabled]="prescriptionForm.invalid"
        >
          {{ editMedStatus ? "Update Medicine" : "Add Medicine" }}
        </button>
      </div>
    </div>
  </form>
</div>
