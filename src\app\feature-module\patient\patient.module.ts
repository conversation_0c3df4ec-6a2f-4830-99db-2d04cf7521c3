import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PatientRoutingModule } from './patient-routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { SharedModule } from 'primeng/api';
import { PatientSidebarModule } from '../common/patient-sidebar/patient-sidebar.module';
import { PatientComponent } from './patient.component';
import { PatientDashboardComponent } from './patient-dashboard/patient-dashboard.component';
import { NgCircleProgressModule } from 'ng-circle-progress';
import { NgApexchartsModule } from 'ng-apexcharts';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { PatientProfileComponent } from './patient-profile/patient-profile.component';
import { PatientBasicInfoComponent } from './patient-basic-info/patient-basic-info.component';
import { SearchableMultiSelectComponent } from 'src/app/shared/searchable-multiselect/searchable-multiselect.component';
import { SearchableSelectComponent } from 'src/app/shared/searchable-select/searchable-select.component';
import { PatientAddressComponent } from './patient-address/patient-address.component';
import { PatientContactComponent } from './patient-contact/patient-contact.component';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { PatientClinicalNotesComponent } from './clinical-notes/patient-clinical-notes/patient-clinical-notes.component';
import { MatSelectModule } from '@angular/material/select';
import { PatientInsuranceComponent } from './patient-insurance/patient-insurance.component';
import { PatientAppointmentsComponent } from './patient-appointments/patient-appointments.component';
import { PatientLifestyleComponent } from './patient-lifestyle/patient-lifestyle.component';
import { PatientNotificationsComponent } from './patient-notifications/patient-notifications.component';
import { SelectSearchComponent } from 'src/app/shared/select-search/select-search.component';
import { ClinicalNotesComponent } from './clinical-notes/clinical-notes.component';
import { PatientClinicalVisitsComponent } from './clinical-notes/patient-clinical-visits/patient-clinical-visits.component';
import { PatientVitalsComponent } from './clinical-notes/patient-vitals/patient-vitals.component';
import { PatientChiefComplaintsComponent } from './clinical-notes/patient-chief-complaints/patient-chief-complaints.component';
import { PatientClinicalAttachmentsComponent } from './clinical-notes/patient-clinical-attachments/patient-clinical-attachments.component';
import { PatientFamilyHistoryComponent } from './clinical-notes/patient-family-history/patient-family-history.component';
import { PatientFollowupsComponent } from './clinical-notes/patient-followups/patient-followups.component';
import { PatientGynecHistoryComponent } from './clinical-notes/patient-gynec-history/patient-gynec-history.component';
import { PatientHospitalHistoryComponent } from './clinical-notes/patient-hospital-history/patient-hospital-history.component';
import { PatientImmunizationComponent } from './clinical-notes/patient-immunization/patient-immunization.component';
import { PatientInvestigationsComponent } from './clinical-notes/patient-investigations/patient-investigations.component';
import { PatientPrescriptionsComponent } from './clinical-notes/patient-prescriptions/patient-prescriptions.component';
import { PatientPresentIllnessComponent } from './clinical-notes/patient-present-illness/patient-present-illness.component';
import { PatientProceduresComponent } from './clinical-notes/patient-procedures/patient-procedures.component';
import { PatientAllergyComponent } from './clinical-notes/patient-allergy/patient-allergy.component';
import { PatientSurgicalHistoryComponent } from './clinical-notes/patient-surgical-history/patient-surgical-history.component';
import { DynamicTableComponent } from 'src/app/shared/dynamic-table/dynamic-table.component';
import { FormFieldComponent } from 'src/app/shared/form-field/form-field.component';
import { LoaderComponent } from 'src/app/shared/loader/loader.component';
import { PatientDocPreviewComponent } from './patient-doc-preview/patient-doc-preview.component';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { PatientPrescriptionComponent } from './patient-prescription/patient-prescription.component';
import { PatientDependantsComponent } from './patient-dependants/patient-dependants.component';
import { PtDocProfileComponent } from './patient-appointments/pt-doc-profile/pt-doc-profile.component';
import { PtBookSlotComponent } from './patient-appointments/pt-book-slot/pt-book-slot.component';
import { PtBookSlotPaymentComponent } from './patient-appointments/pt-book-slot-payment/pt-book-slot-payment.component';
import { PtAppointmentListComponent } from './patient-appointments/pt-appointment-list/pt-appointment-list.component';
import { PtPastAppointmentListComponent } from './patient-appointments/pt-past-appointment-list/pt-past-appointment-list.component';
import { PtFutureAppointmentListComponent } from './patient-appointments/pt-future-appointment-list/pt-future-appointment-list.component';
import { PrescriptionRefillAddComponent } from './prescription-refill-add/prescription-refill-add.component';
import { PrescriptionsRefillListComponent } from './prescriptions-refill-list/prescriptions-refill-list.component';
import { PatientChatComponent } from './patient-chat/patient-chat.component';
import { PtRescheduleAppointmentComponent } from './patient-appointments/pt-reschedule-appointment/pt-reschedule-appointment.component';
import { ApproveRefillComponent } from './approve-refill/approve-refill.component';
import { RejectRefillComponent } from './reject-refill/reject-refill.component';
import { ChangePasswordComponent } from 'src/app/shared/change-password/change-password.component';
import { TwoFactorVerificationComponent } from 'src/app/shared/two-factor-verification/two-factor-verification.component';
import { PtAskDoctorComponent } from './patient-chat/pt-ask-doctor/pt-ask-doctor.component';
import { PtQueryListComponent } from './patient-chat/pt-query-list/pt-query-list.component';
import { PtQueryViewComponent } from './patient-chat/pt-query-view/pt-query-view.component';
import { TrustedDevicesComponent } from 'src/app/shared/trusted-devices/trusted-devices.component';
import { PatientReviewsComponent } from './patient-reviews/patient-reviews.component';
import { PatientVitalSignComponent } from './patient-vitals/patient-vitals.component';
import { PatientLifestyleAddComponent } from './patient-lifestyle-add/patient-lifestyle-add.component';
import { PatientReviewsListComponent } from './patient-reviews/patient-reviews-list/patient-reviews-list.component';
import { PatientDocumentsComponent } from './patient-documents/patient-documents.component';
import { PatientCreateDocComponent } from './patient-create-doc/patient-create-doc.component';
import { NgxPayPalModule } from 'ngx-paypal';
import { PatientPrescriptionItemsComponent } from './patient-prescription-items/patient-prescription-items.component';
import { PatientClinicalVisitListComponent } from './patient-clinical-visit-list/patient-clinical-visit-list.component';
import { PatientClinicalVisitViewComponent } from './patient-clinical-visit-view/patient-clinical-visit-view.component';
import { PageWithSearchPaginationComponent } from 'src/app/shared/page-with-search-pagination/page-with-search-pagination.component';
import { ChatbotComponent } from 'src/app/shared/chatbot/chatbot.component';
import { BackButtonComponent } from '../../shared/back-button/back-button.component';
import { PatientVisitHistoryComponent } from './patient-clinical-visit-list/patient-visit-history/patient-visit-history.component';
import { LoadingSpinnerComponent } from '../../shared/loading-spinner/loading-spinner.component';
import { ErrorAlertComponent } from '../../shared/error-alert/error-alert.component';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { ViewAiLifestyleSummaryComponent } from '../../shared/view-ai-lifestyle-summary/view-ai-lifestyle-summary.component';
import { PtBookAppointmentComponent } from './patient-appointments/pt-book-appointment/pt-book-appointment.component';
import { FollowUpLabtestListComponent } from 'src/app/shared/components/pages/follow-up-labtest-list/follow-up-labtest-list.component';
import { PtLabTestListComponent } from './pt-lab-tests/pt-lab-test-list/pt-lab-test-list.component';
import { PtLabTestSelectPackageComponent } from './pt-lab-tests/pt-lab-test-select-package/pt-lab-test-select-package.component';
import { LabTestPackageCardComponent } from 'src/app/shared/components/cards/lab-test-package-card/lab-test-package-card.component';
import { PtLabTestPackageDetailsComponent } from './pt-lab-tests/pt-lab-test-package-details/pt-lab-test-package-details.component';
import { PtLabTestCartComponent } from './pt-lab-tests/pt-lab-test-cart/pt-lab-test-cart.component';
import { PatientHeaderComponent } from './common/patient-header/patient-header.component';
import { PurchasedCarePlansListComponent } from './patient-care-plans/purchased-care-plans-list/purchased-care-plans-list.component';
import { PurchasedCarePlanDetailsComponent } from './patient-care-plans/purchased-care-plan-details/purchased-care-plan-details.component';
import { SubmitBtnComponent } from 'src/app/shared/components/re-use/submit-btn/submit-btn.component';

@NgModule({
  declarations: [
    PatientComponent,
    ClinicalNotesComponent,
    PatientVitalsComponent,
    PatientAllergyComponent,
    PatientSurgicalHistoryComponent,
    PatientClinicalVisitsComponent,
    PatientChiefComplaintsComponent,
    PatientFamilyHistoryComponent,
    PatientFollowupsComponent,
    PatientGynecHistoryComponent,
    PatientHospitalHistoryComponent,
    PatientImmunizationComponent,
    PatientInvestigationsComponent,
    PatientPrescriptionsComponent,
    PatientClinicalAttachmentsComponent,
    PatientPresentIllnessComponent,
    PatientProceduresComponent,
    PatientDashboardComponent,
    PatientProfileComponent,
    PatientBasicInfoComponent,
    PatientAddressComponent,
    PatientContactComponent,
    PatientClinicalNotesComponent,
    PatientInsuranceComponent,
    PatientAppointmentsComponent,
    PatientLifestyleComponent,
    PatientNotificationsComponent,
    PatientDocPreviewComponent,
    PatientPrescriptionComponent,
    PatientDependantsComponent,
    PtDocProfileComponent,
    PtBookSlotComponent,
    PtBookSlotPaymentComponent,
    PtAppointmentListComponent,
    PtPastAppointmentListComponent,
    PtFutureAppointmentListComponent,
    PrescriptionRefillAddComponent,
    PrescriptionsRefillListComponent,
    PatientChatComponent,
    PtRescheduleAppointmentComponent,
    PatientVitalSignComponent,
    PtAskDoctorComponent,
    PtQueryListComponent,
    PtQueryViewComponent,
    PatientReviewsComponent,
    PatientLifestyleAddComponent,
    PatientReviewsListComponent,
    PatientDocumentsComponent,
    PatientCreateDocComponent,
    PatientPrescriptionItemsComponent,
    PatientClinicalVisitListComponent,
    PatientClinicalVisitViewComponent,
    PatientVisitHistoryComponent,
    PtBookAppointmentComponent,
    PtLabTestListComponent,
    PtLabTestSelectPackageComponent,
    PtLabTestPackageDetailsComponent,
    PtLabTestCartComponent,
    PatientHeaderComponent,
    PurchasedCarePlansListComponent,
    PurchasedCarePlanDetailsComponent,
  ],
  imports: [
    CommonModule,
    PatientRoutingModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgApexchartsModule,
    CarouselModule,
    NgCircleProgressModule.forRoot(),
    BsDatepickerModule.forRoot(),
    PatientSidebarModule,
    SearchableMultiSelectComponent,
    SearchableSelectComponent,
    SelectSearchComponent,
    DynamicTableComponent,
    MatSelectModule,
    SharedModule,
    FormFieldComponent,
    LoaderComponent,
    PdfViewerModule,
    ChangePasswordComponent,
    TwoFactorVerificationComponent,
    ApproveRefillComponent,
    RejectRefillComponent,
    TrustedDevicesComponent,
    NgxPayPalModule,
    DynamicTableComponent,
    PageWithSearchPaginationComponent,
    ChatbotComponent,
    BackButtonComponent,
    LoadingSpinnerComponent,
    ErrorAlertComponent,
    NgbTooltipModule,
    FollowUpLabtestListComponent,
    ViewAiLifestyleSummaryComponent,
    LabTestPackageCardComponent,
    SubmitBtnComponent,
  ],
})
export class PatientModule {}
