import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, firstValueFrom } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class HealthAdvisorService {
  private apiUrl: string = environment.apiUrl;

  private cartItems = new BehaviorSubject<any[]>([]);
  cartItems$ = this.cartItems.asObservable();

  constructor(private http: HttpClient) {}

  addToCart(item: any) {
    const current = this.cartItems.value.slice(); // clone the array
    const existing = current.find((ci) => ci.package_id === item.package_id);

    if (existing) {
      // existing.quantity += item.quantity;
      alert('Package already added to cart!');
    } else {
      current.push({ ...item });
    }

    this.cartItems.next([...current]); // emit updated cart
  }

  getCartItems(): any[] {
    return this.cartItems.value;
  }

  clearCart() {
    this.cartItems.next([]);
  }

  getTotalCount(): number {
    return this.cartItems.value.reduce((sum, item) => sum + item.quantity, 0);
  }

  getAllPatientsForLabTests() {
    const endPoint = this.apiUrl + `activity/getallinternalfacilitypatients`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  getAllLabPackages() {
    const endPoint = this.apiUrl + `lab/getallpackages`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  getPackageByPackageId(packageId: any) {
    const endPoint = this.apiUrl + `lab/getpackagebypackageid`;
    return firstValueFrom(this.http.post<any>(endPoint, { packageId }));
  }

  getLabTestsByPackageId(packageId: any) {
    const endPoint = this.apiUrl + `lab/getlistoftestfrompackageid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id: packageId }));
  }

  getCartDetailsByUserId(userId: any) {
    const endPoint = this.apiUrl + `activity/getusercurrentcartbyuserid`;
    return firstValueFrom(this.http.post<any>(endPoint, { userId }));
  }

  createNewCartForUser(payload: any) {
    const endPoint = this.apiUrl + `activity/createnewcart`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  createNewCartItem(payload: any) {
    const endPoint = this.apiUrl + `activity/createnewcartitems`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getCartItemsByCartId(cartId: any, packageId: any) {
    const endPoint = this.apiUrl + `activity/getusercurrentcartitemsbyuserid`;
    return firstValueFrom(this.http.post<any>(endPoint, { cartId, packageId }));
  }

  updatecartItems(payload: any) {
    const endPoint = this.apiUrl + `activity/updatecartitems`;
    return firstValueFrom(this.http.put<any>(endPoint, payload));
  }

  deleteCartItems(cartId: any, packageId: any) {
    const endPoint = this.apiUrl + `activity/removeitemfromcart`;
    return firstValueFrom(this.http.post<any>(endPoint, { cartId, packageId }));
  }

  getCartSummaryByCartId(cartId: any) {
    const endPoint = this.apiUrl + `activity/getcheckoutsummarybycartid`;
    return firstValueFrom(this.http.post<any>(endPoint, { cartId }));
  }

  getCartTotalPrice(cartId: any) {
    const endPoint = this.apiUrl + `activity/calculatetotalpricebycartid`;
    return firstValueFrom(this.http.post<any>(endPoint, { cartId }));
  }

  getUserPurchasedPackages(userId: any) {
    const endPoint = this.apiUrl + `activity/getuserlabordersbyuserid`;
    return firstValueFrom(this.http.post<any>(endPoint, { userId }));
  }

  getUserPurchasedCarePlans(userId: any) {
    const endPoint = this.apiUrl + `activity/getusercarplanpurchases`;
    return firstValueFrom(this.http.post<any>(endPoint, { userId }));
  }

  getCartCountByCartId(cartId: any) {
    const endPoint = this.apiUrl + `activity/getcountofcartitemsbycardid`;
    return firstValueFrom(this.http.post<any>(endPoint, { cartId }));
  }

  finalizePackagePurchaseByUserId(payload: any) {
    const endPoint = this.apiUrl + `activity/finalizepurchaseorderbyuserid`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getPatientsByActivityId(activityId: any) {
    const endPoint = this.apiUrl + `activity/getallpatientbyactivityid`;
    return firstValueFrom(this.http.post<any>(endPoint, { activityId }));
  }

  // getallactivitypatientbyactivityid(activityId: any, facilityId: any) {
  //   const endPoint = this.apiUrl + `activity/getallactivitypatientbyactivityid`;
  //   return firstValueFrom(
  //     this.http.post<any>(endPoint, { activityId, facilityId: facilityId })
  //   );
  // }

  getallactivitypatientbyactivityid(activityId: any, facilityId: any) {
    const endPoint = this.apiUrl + `user-accounts/getlistallpatientenrollments`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { activityId, facilityId: facilityId })
    );
  }

  getActivitiesByFacilityIds(payload: any) {
    const endPoint = this.apiUrl + `activity/getallactivitybyoritempfacilityid`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  uploadLabTestReport(payload: any) {
    const endPoint = this.apiUrl + `activity/createlabtestreport`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  updateLabTestReportStatus(payload: any) {
    const endPoint = this.apiUrl + `activity/updatelabreportstatus`;
    return firstValueFrom(this.http.put<any>(endPoint, payload));
  }

  getUploadedReports(payload: any) {
    const endPoint =
      this.apiUrl + `activity/getalllabpurchasedorderbypurchaseid`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getAllInvoices(payload: any) {
    const endPoint = this.apiUrl + `activity/getuserinvoicepdfdetails`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getCarePlanInvoices(payload: any) {
    const endPoint = this.apiUrl + `activity/getusercareplaninvoicepdfdetails`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getInvoiceNumber() {
    const endPoint = this.apiUrl + `activity/getinvoicenumber`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  uploadInvoice(payload: any) {
    const endPoint = this.apiUrl + `activity/updatelabuserpurchase`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getAllLabs() {
    const endPoint = this.apiUrl + `activity/getalllaboratorieslist`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  getAllLabTestsByLabAndPackageId(labId: any, packageId: any) {
    const endPoint = this.apiUrl + `activity/getlistoftestunitrangesbylabid`;
    return firstValueFrom(this.http.post<any>(endPoint, { labId, packageId }));
  }

  getAddedLabReports(payload: any) {
    const endPoint = this.apiUrl + `activity/getLabUserReportResults`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  createLabReports(payload: any) {
    const endPoint = this.apiUrl + `activity/createlabuserreports`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  updateLabReports(payload: any) {
    const endPoint = this.apiUrl + `activity/updatelabuserreports`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  updateLabRepostStatus(payload: any) {
    const endPoint = this.apiUrl + `activity/updatelabreportstatusbylm`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  updatePurchasedPackageStatus(payload: any) {
    const endPoint = this.apiUrl + `activity/updateuserpurchasestatus`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getResultSubmittedLabTests(payload: any) {
    const endPoint =
      this.apiUrl + `activity/getlabuserpackagedetailbypatientid`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getFinalLabResultAndReportDetailsByReportId(reportId: any) {
    const endPoint = this.apiUrl + `activity/getfinalreportdetailbyreportId`;
    return firstValueFrom(this.http.post<any>(endPoint, { reportId }));
  }

  publishLabReport(payload: any) {
    const endPoint = this.apiUrl + `activity/updatelabreportpdf`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getReportCommentsByReportId(reportId: any) {
    const endPoint = this.apiUrl + `activity/getreportcommentsbyreportId`;
    return firstValueFrom(this.http.post<any>(endPoint, { reportId }));
  }

  uploadManagerSignature(payload: any) {
    const endPoint = this.apiUrl + `activity/updatelabreportinfo`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getReportStatusByReportId(reportId: any) {
    const endPoint = this.apiUrl + `activity/getreportstatusbyreportid`;
    return firstValueFrom(this.http.post<any>(endPoint, { reportId }));
  }

  uploadInvoicePdf(payload: any) {
    const endPoint = this.apiUrl + `activity/invoice`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  generateLabReportPdf(payload: any) {
    const endPoint = this.apiUrl + `activity/labreport`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  // Careplans

  getAllInternalPatients() {
    const endPoint = this.apiUrl + `activity/getallinternalfacilitypatients`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  getAllCarePlans() {
    const endPoint = this.apiUrl + `activity/getallcareplandetails`;
    return firstValueFrom(this.http.post<any>(endPoint, {}));
  }

  getCarePlanByPlanId(planId: any) {
    const endPoint = this.apiUrl + `activity/getcareactivitydetailbycareid`;
    return firstValueFrom(this.http.post<any>(endPoint, { careId: planId }));
  }

  createNewCartForCarePlan(payload: any) {
    const endPoint = this.apiUrl + `activity/createnewcartforcare`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  createNewCartItemForCarePlan(payload: any) {
    const endPoint = this.apiUrl + `activity/createnewcartitemsforcare`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  calculateTotalPriceForCarePlanByCartId(cartId: any) {
    const endPoint =
      this.apiUrl + `activity/calculatetotalpriceforcarebycartid`;
    return firstValueFrom(this.http.post<any>(endPoint, { cartId }));
  }

  updateCarePlanCartItems(payload: any) {
    const endPoint = this.apiUrl + `activity/updatecartitemsforcare`;
    return firstValueFrom(this.http.put<any>(endPoint, payload));
  }

  deleteCarePlanCartItems(cartId: any, careId: any) {
    const endPoint = this.apiUrl + `activity/removeitemfromcartforcare`;
    return firstValueFrom(this.http.post<any>(endPoint, { cartId, careId }));
  }

  getCurrentCarePlanCartByUserId(userId: any) {
    const endPoint = this.apiUrl + `activity/getusercurrentcartforcarebyuserid`;
    return firstValueFrom(this.http.post<any>(endPoint, { userId }));
  }

  getCarePlanCartCheckoutSummaryByCartId(cartId: any) {
    const endPoint = this.apiUrl + `activity/getcheckoutsummaryforcarebycartid`;
    return firstValueFrom(this.http.post<any>(endPoint, { cartId }));
  }

  finalizeCarplanPurchaseByUserId(payload: any) {
    const endPoint =
      this.apiUrl + `activity/finalizepurchaseorderforcarebyuserid`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  generateCarePlanInvoicePdf(payload: any) {
    const endPoint = this.apiUrl + `activity/careplaninvoice`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  // Payments Testing

  // Care plan activities
  getAllDoctorsList(payload: any) {
    const endPoint = this.apiUrl + `doctors/getalldoctorinfoforba`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getDoctorDetails(payload: any) {
    const endPoint = this.apiUrl + `doctors/getdocinfoforbookappointment`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getDoctorSlots(payload: any) {
    const endPoint = this.apiUrl + `doctors/getdocslotsforbookappointments`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getSpecialistActivityAppointments(payload: any) {
    const endPoint = this.apiUrl + `patients/getpatcareactbookappointments`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getSpecialistActivityVisits(payload: any) {
    const endPoint = this.apiUrl + `patients/getpatcareactvisits`;
    // return firstValueFrom(this.http.post<any>(endPoint, payload));
    return [];
  }

  getAllPatientRequests(payload: any) {
    const endPoint = this.apiUrl + `patients/getpatientallappointmentrequests`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }
}
