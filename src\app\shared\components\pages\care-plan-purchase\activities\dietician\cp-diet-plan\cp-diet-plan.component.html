<form [formGroup]="dietPlanForm" autocomplete="off">
  <div
    class="card mb-4 shadow-sm border-0 rounded-3"
    style="background-color: whitesmoke"
  >
    <div
      class="card-header border-0 pb-3 d-flex justify-content-between align-items-center"
    >
      <h5 class="card-title mb-0 fw-bold text-primary">
        <i class="bi bi-egg-fried me-2"></i> Diet Plan
      </h5>
      <!-- Action Buttons -->
      <div class="d-flex align-items-center">
        <button
          class="btn btn-outline-secondary rounded-pill px-4 me-2 shadow-sm"
          (click)="dietPlanForm.reset()"
        >
          <i class="bi bi-x-circle me-1"></i> Cancel
        </button>
        <button
          class="btn btn-primary rounded-pill px-4 shadow-sm"
          (click)="saveDietPlan()"
          [disabled]="isCompleted"
        >
          <i class="bi bi-check-circle me-1"></i>
          {{ dietData.length > 0 ? "Update" : "Save" }}
        </button>
      </div>
    </div>

    <div class="card-body">
      <div class="row g-4">
        <!-- Time -->
        <div class="col-md-6">
          <label class="form-label fw-semibold">
            <i class="bi bi-clock me-1"></i> Time
          </label>
          <input
            type="time"
            class="form-control form-control-sm shadow-sm"
            [formControl]="time"
            placeholder="Time"
          />
        </div>

        <!-- Meal -->
        <div class="col-md-6">
          <kt-select-search
            [items]="[
              { name: 'Early Morning', value: 'Early Morning' },
              { name: 'Breakfast', value: 'Breakfast' },
              { name: 'Mid-morning', value: 'Mid-morning' },
              { name: 'Lunch', value: 'Lunch' },
              { name: 'Evening', value: 'Evening' },
              { name: 'Dinner', value: 'Dinner' },
              { name: 'Late-night', value: 'Late-night' }
            ]"
            label="Meal"
            placeholder="Select Meal"
            [formControl]="meal"
          >
          </kt-select-search>
        </div>

        <!-- Food Items -->
        <div class="col-md-6">
          <label class="form-label fw-semibold">
            <i class="bi bi-basket me-1"></i> Food Items
          </label>
          <input
            type="text"
            class="form-control form-control-sm shadow-sm"
            [formControl]="foodItems"
            placeholder="E.g. Oats, Apple, Boiled Egg"
          />
        </div>

        <!-- Quantity -->
        <div class="col-md-6">
          <label class="form-label fw-semibold">
            <i class="bi bi-cup-straw me-1"></i> Quantity
          </label>
          <input
            type="text"
            class="form-control form-control-sm shadow-sm"
            [formControl]="quantity"
            placeholder="E.g. 2 cups, 1 slice, 100 ml"
          />
        </div>

        <!-- Notes -->
        <div class="col-md-12">
          <div class="mb-3">
            <label class="form-label fw-semibold">
              <i class="bi bi-chat-dots me-1"></i> Notes
            </label>
            <textarea
              rows="3"
              class="form-control shadow-sm"
              [formControl]="notes"
              placeholder="Any additional notes"
            ></textarea>
          </div>
        </div>
      </div>
      <!-- Table -->
      <div class="row">
        <PageWithSearchPagination
          [data]="dietData"
          (filteredDataChange)="filterDietData = $event"
          [isPagination]="dietData"
          [itemsPerPage]="5"
          [isSearch]="true"
          searchPlaceHolder="Search Diet Plan"
          [isItemsPerPage]="true"
        >
          <div class="table-responsive rounded-4 shadow-lg mt-3">
            <table
              class="table table-striped table-hover table-bordered align-middle mb-0"
            >
              <thead class="table-primary">
                <tr>
                  <th scope="col">Time</th>
                  <th scope="col">Meal</th>
                  <th scope="col">Food Items</th>
                  <th scope="col">Quantity</th>
                  <th scope="col">Notes</th>
                  <th scope="col" class="text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let m of dietData; let i = index">
                  <td class="fw-semibold">{{ m.mealTime }}</td>
                  <td class="fw-semibold">{{ m.mealLabel }}</td>
                  <td class="fw-semibold">{{ m.foodItem }}</td>
                  <td class="fw-semibold">{{ m.quantity }}</td>
                  <td class="fw-semibold">{{ m.notes }}</td>
                  <td class="text-center">
                    <button
                      class="btn btn-sm btn-warning me-2 px-3 rounded-pill shadow-sm"
                      (click)="editDiet(m.id)"
                    >
                      <i class="bi bi-pencil-square"></i> Edit
                    </button>
                    <!-- <button
                      class="btn btn-sm btn-danger px-3 rounded-pill shadow-sm"
                      (click)="deleteManualTherapy(i)"
                    >
                      <i class="bi bi-trash"></i> Delete
                    </button> -->
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </PageWithSearchPagination>
      </div>
    </div>
  </div>
</form>
