import {
  AfterViewInit,
  Component,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { firstValueFrom } from 'rxjs';
import { UserAccountsService } from 'src/app/shared/services/user-accounts.service';
import { AdminManagerService } from '../../admin-manager/services/admin-manager.service';
import { PatientService } from '../../patient/patient.service';
import { UserService } from 'src/app/shared/services/user.service';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { PieChartOptions } from '../../admin-manager/am-dashboard/am-dashboard.component';
import { ChartComponent } from 'ng-apexcharts';
import { doctorDashboard } from 'src/app/shared/models/models';
import { routes } from 'src/app/shared/routes/routes';
import { ChartOptions } from 'src/app/modal/modal.component';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';

@Component({
  selector: 'app-ha-dashboard',
  standalone: false,
  templateUrl: './ha-dashboard.component.html',
  styleUrl: './ha-dashboard.component.scss',
})
export class HaDashboardComponent implements OnInit {
  @ViewChild('chart') chart!: ChartComponent;
  public chartOptions1: Partial<ChartOptions>;
  public chartOptions2: Partial<ChartOptions>;
  public routes = routes;
  public tableData: Array<doctorDashboard> = [];
  public tableData2: Array<doctorDashboard> = [];

  @ViewChild('pieChart') pieChart!: ChartComponent;
  public pieChartOptions!: PieChartOptions;
  util = inject(UtilFunctions);

  // Admin Manager Dashboard
  facilityId!: string;
  user: any;
  userDetails: any;
  allDoctorsCount: number = 0;
  allPatientsCount: number = 0;
  allDoctorsList: any[] = [];
  allPatientsList: any[] = [];
  allUsersLastLoginList: any[] = [];
  refillList: any[] = [];
  filteredRefillList: any[] = [];
  usersDeptWiseCount: any;
  deptUsers: any[] = [];
  filteredDeptUsers: any[] = [];
  currentDept: string = 'Admin Manager';
  isDeptLoading: boolean = false;
  isChartReady: boolean = false;

  deptUsersCols: any = [];

  filteredUpcomingAppointments: any[] = [];
  filteredPastAppointments: any[] = [];

  appointments: any[] = [];
  filteredAppointments: any[] = [];
  todayPatientsCount: number = 0;
  recentPatients: any[] = [];

  isPageLoading: boolean = false;

  // Pagination Variables
  currentPage: number = 1;
  itemsPerPage: number = 5; // Change as needed
  totalFilteredCount: number = 0;
  searchQuery: string = ''; // Search query

  loader: Record<string, boolean> = {
    doctors: false,
    patients: false,
    userChart: false,
    userslist: false,
    appointments: false,
    followups: false,
    recentPatients: false,
  };

  facilityWeeklyAppointments = injectQuery(() => ({
    queryKey: ['facility-weekly-appointments', this.facilityId],
    queryFn: async () => {
      const res: any = await this.amService.getWeeklyAppoinitmentByFacilityId(
        this.facilityId
      );

      if (res && res.length > 0) {
        this.chartOptions2 = {
          series: [
            {
              name: 'Appointments',
              data: res.map((item: any) => item.appointmentCount),
            },
          ],
          chart: {
            type: 'bar',
            height: 220,
            stacked: true,
            endingShape: 'rounded',
            toolbar: {
              show: false,
            },
          },
          plotOptions: {
            bar: {
              horizontal: false,
              columnWidth: '50%',
              endingShape: 'rounded',
              borderRadius: 7,
            },
          },
          dataLabels: {
            enabled: false,
          },
          xaxis: {
            categories: res.map((item: any) => item.weekDays),
          },
        };
      }

      return res;
    },
    refetchOnWindowFocus: false,
  }));

  facilityWeeklyRevenue = injectQuery(() => ({
    queryKey: ['facility-weekly-revenue', this.facilityId],
    queryFn: async () => {
      const res: any = await this.amService.getWeeklyRevnueByFacilityId(
        this.facilityId
      );

      if (res && res.length > 0) {
        this.chartOptions1 = {
          series: [
            {
              name: 'Revenue',
              data: res.map((item: any) => item.total_revenue),
            },
          ],
          chart: {
            type: 'bar',
            height: 220,
            stacked: true,
            toolbar: {
              show: false,
            },
          },
          plotOptions: {
            bar: {
              horizontal: false,
              columnWidth: '50%',
              endingShape: 'rounded',
              borderRadius: 7,
            },
          },
          dataLabels: {
            enabled: false,
          },
          xaxis: {
            categories: res.map((item: any) => item.WEEKDAY),
          },
        };
      }

      return res;
    },
    refetchOnWindowFocus: false,
  }));

  todayPatientActivities = injectQuery(() => ({
    queryKey: ['today-patient-activities'],
    queryFn: async () => {
      const res: any = await this.util.haService.getTodayPatientActivities();
      return res;
    },
    refetchOnWindowFocus: false,
  }));

  upcomingPatientActivities = injectQuery(() => ({
    queryKey: ['upcoming-patient-activities'],
    queryFn: async () => {
      const res: any = await this.util.haService.getUpcomingPatientActivities();
      return res;
    },
    refetchOnWindowFocus: false,
  }));

  pastPatientActivities = injectQuery(() => ({
    queryKey: ['past-patient-activities'],
    queryFn: async () => {
      const res: any = await this.util.haService.getPastPatientActivities();
      return res;
    },
    refetchOnWindowFocus: false,
  }));

  doctorsCols: any = [
    {
      key: 'fullName',
      label: 'Doctor Name',
      type: 'link',
      linkFn: (item: any) => this.getDoctorLink(item.id),
    },
    { key: 'facilityName', label: 'Facility Name' },
    { key: 'cityName', label: 'City', type: 'address' },
    { key: 'dob', label: 'DOB', type: 'date', format: 'dd MMM yyyy' },
  ];

  patientsCols: any = [
    {
      key: 'fullName',
      label: 'Patient Name',
      type: 'link',
      linkFn: (item: any) => this.getPatientLink(item.id),
    },
    { key: 'facilityName', label: 'Facility Name' },
    { key: 'cityName', label: 'City' },
    { key: 'dob', label: 'DOB', type: 'date', format: 'dd MMM yyyy' },
  ];

  private doctorLinkCache = new Map<number, string>();
  private patientLinkCache = new Map<number, string>();

  private getDoctorLink(id: number) {
    if (!this.doctorLinkCache.has(id)) {
      this.doctorLinkCache.set(
        id,
        '/health-advisor/doctor-profile/' + this.util.encrypt(id)
      );
    }
    return this.doctorLinkCache.get(id)!;
  }

  private getPatientLink(id: number) {
    if (!this.patientLinkCache.has(id)) {
      this.patientLinkCache.set(
        id,
        '/health-advisor/patient-profile/' + this.util.encrypt(id)
      );
    }
    return this.patientLinkCache.get(id)!;
  }

  constructor(
    private router: Router,
    private toastr: ToastrService,
    private authService: AuthService,
    private userService: UserService,
    private patientService: PatientService,
    private amService: AdminManagerService,
    private userAccountService: UserAccountsService
  ) {
    this.chartOptions1 = {
      series: [
        {
          name: 'High',
          data: [50, 40, 15, 45, 35, 48, 65],
        },
      ],
      chart: {
        type: 'bar',
        height: 220,
        stacked: true,
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '50%',
          endingShape: 'rounded',
          borderRadius: 7,
        },
      },
      dataLabels: {
        enabled: false,
      },
      xaxis: {
        categories: ['M', 'T', 'W', 'T', 'F', 'S', 'S'],
      },
    };
    this.chartOptions2 = {
      series: [
        {
          name: 'High',
          data: [40, 20, 30, 60, 90, 40, 110],
        },
      ],
      chart: {
        type: 'bar',
        height: 220,
        stacked: true,
        endingShape: 'rounded',
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '50%',
          endingShape: 'rounded',
          borderRadius: 7,
        },
      },
      dataLabels: {
        enabled: false,
      },
      xaxis: {
        categories: ['M', 'T', 'W', 'T', 'F', 'S', 'S'],
      },
    };
    this.pieChartOptions = {
      series: [this.allDoctorsCount, this.allPatientsCount, 30],
      chart: {
        width: 500,
        type: 'pie',
      },
      labels: ['Doctors', 'Patients', 'Others'],
      responsive: [
        {
          breakpoint: 500,
          options: {
            chart: {
              width: 200,
            },
            legend: {
              position: 'bottom',
            },
          },
        },
      ],
    };

    this.user = this.authService.getDataFromSession('user');
    this.facilityId = this.user.facilityId;
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  ngOnInit() {
    this.getUserDetails(this.user.userId);
    this.getDoctorsListByFacilityId(this.facilityId);
    this.getPatientsListByFacilityId(this.facilityId);
    this.getAllUsersLastLoginData();
    this.getRecentPatients();
    // this.getAllRefillList();
    this.getAllAppointments(this.facilityId);
    this.getUsersDeptWiseCount();
    // this.setPieChartData();
    this.getUsersByDept('ROLE_ADMIN_MANAGER');
  }

  getUpcomingLimitAppointments = injectQuery(() => ({
    queryKey: ['upcoming-appointments', this.facilityId],
    queryFn: async () => {
      const res: any = await this.amService.getUpcoming5AptforHALogin();
      return res.reverse();
    },
    refetchOnWindowFocus: false,
  }));

  getPastLimitAppointments = injectQuery(() => ({
    queryKey: ['past-appointments', this.facilityId],
    queryFn: async () => {
      const res: any = await this.amService.getPast5AptforHALogin();
      return res;
    },
    refetchOnWindowFocus: false,
  }));

  trackByRefillId(index: number, refill: any): any {
    return refill.id;
  }

  viewAppointment = (appointment: any) => {
    if (appointment.visitId) {
      this.viewVisitDetails(
        appointment.patientId,
        appointment.visitId,
        appointment.doctorId
      );
    } else {
      let appId = appointment['appointmentId'];
      this.router.navigate([
        '/health-advisor/instant-appointment/view-appointment/',
        this.util.adminService.encrypt(appId),
      ]);
    }
  };

  viewPatientAppointments = (patientId: any) => {
    this.router.navigateByUrl(
      `/admin/manager/view-patient-appointments/${patientId}`
    );
  };

  viewVisitDetails = (patientId: any, visitId: any, activityId: any) => {
    this.router.navigate([
      '/health-advisor/view/doc-visit-details/',
      this.util.encrypt(patientId),
      this.util.encrypt(visitId),
    ]);
  };

  async loadPrescription(prescriptionId: any) {
    try {
      const prescription = await firstValueFrom(
        this.patientService.getPrescriptionByPrescriptionId(prescriptionId)
      );
      return prescription;
    } catch (err) {
      console.error('Failed to fetch prescription:', err);
    }
  }

  async getUserDetails(userId: string) {
    try {
      const res: any = await this.userService.getUserByUserId(userId);
      this.userDetails = res[0];
      return res;
    } catch (error) {
      console.log('--Error while getting user details : ', error);
      this.toastr.error('While getting user details', 'Error');
    }
  }

  async getAllAppointments(fId: string) {
    try {
      this.loader['appointments'] = true;
      const res: any = await this.amService.getAppointmentsListByFacilityId(
        fId
      );

      if (res && res.length > 0) {
        const docWithImages = await Promise.all(
          res.map(async (doc: any) => {
            let image = '';

            if (doc.photoLink) {
              const pathname = this.getNormalizedPathname(doc.photoLink);
              const securedUrl = await this.fetchSecuredUrl(pathname);
              image = securedUrl;
            }

            return { ...doc, photoLink: image };
          })
        );

        // Sort by date and time
        docWithImages.sort((a, b) => {
          const dateA = new Date(`${a.appointmentDate}T${a.appointmentTime}`);
          const dateB = new Date(`${b.appointmentDate}T${b.appointmentTime}`);
          return dateB.getTime() - dateA.getTime(); // Descending
        });

        const now = new Date();
        const futureAppointments = docWithImages.filter((doc) => {
          const appointmentDateTime = new Date(
            `${doc.appointmentDate}T${doc.appointmentTime}`
          );
          return appointmentDateTime >= now;
        });

        // Get the latest appointment
        const latestAppointment = futureAppointments[0];

        this.appointments = [latestAppointment];
        this.appointments = docWithImages;
        this.loader['appointments'] = false;
      } else {
        this.appointments = [];
        this.loader['appointments'] = false;
      }
    } catch (error) {
      console.log('--Error while getting user details : ', error);
      this.toastr.error('While getting user details', 'Error');
    }
  }

  applyFilters() {
    let filtered = [...this.refillList];

    // Search filter
    if (this.searchQuery.trim()) {
      const lowerSearch = this.searchQuery.toLowerCase();
      filtered = filtered.filter((facility: any) =>
        Object.values(facility).some(
          (value: any) =>
            value && value.toString().toLowerCase().includes(lowerSearch)
        )
      );
    }

    // Update the total count for pagination
    this.totalFilteredCount = filtered.length;

    // Now apply pagination
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + Number(this.itemsPerPage);

    // console.log('--start: ', start);
    // console.log('--end: ', end);

    this.filteredRefillList = filtered.slice(start, end);
  }

  async getDoctorsListByFacilityId(facilityId: string) {
    try {
      this.loader['doctors'] = true;
      const res: any = await this.amService.getAllDoctorsCountByFacilityId(
        facilityId
      );
      const doctors: any = await this.amService.getAllDoctorsByFacilityId(
        facilityId
      );
      doctors.map((item: any) => {
        item.fullName = 'Dr. ' + ' ' + item.fullName;
      });
      this.allDoctorsList = doctors;
      this.loader['doctors'] = false;
      this.allDoctorsCount = res[0].totalDoctors;
    } catch (error) {
      console.log('--Error while getting doctor details : ', error);
      this.toastr.error('While getting doctor details', 'Error');
    }
  }

  async getPatientsListByFacilityId(facilityId: string) {
    try {
      this.loader['patients'] = true;
      const res: any = await this.amService.getAllPatientsCountsByFacilityId(
        facilityId
      );
      const patients: any = await this.amService.getAllPatientsByFacilityId(
        facilityId
      );
      this.allPatientsCount = res[0].totalPatients;
      this.allPatientsList = patients;
      this.loader['patients'] = false;
    } catch (error) {
      console.log('--Error while getting patients details : ', error);
      this.toastr.error('While getting patients details', 'Error');
    }
  }

  async getRecentPatients() {
    this.loader['recentPatients'] = true;
    try {
      const recentPatients: any = await this.amService.getRecentPatientsList();
      const todayPatiets: any = await this.amService.getTodayPatientsCount(
        this.facilityId
      );
      this.todayPatientsCount = todayPatiets[0].todaysPatientCount;

      const patients = await Promise.all(
        recentPatients.map(async (doc: any) => {
          let image = '';

          if (doc.photo_link) {
            const pathname = this.getNormalizedPathname(doc.photo_link);
            const securedUrl = await this.fetchSecuredUrl(pathname);
            image = securedUrl;
          }

          return { ...doc, photoLink: image };
        })
      );

      patients.sort(
        (a, b) =>
          new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime()
      );

      // Step 2: Filter to get only the latest record for each patientId
      const uniquePatientsMap = new Map();
      for (const patient of patients) {
        if (!uniquePatientsMap.has(patient.patientId)) {
          uniquePatientsMap.set(patient.patientId, patient);
        }
      }

      // Step 3: Convert map to array and take first 10
      this.recentPatients = Array.from(uniquePatientsMap.values()).slice(0, 10);
      console.log('Recent Patients', this.recentPatients);
      this.loader['recentPatients'] = false;
    } catch (error) {
      this.loader['recentPatients'] = false;
      console.log('--Error while getting patients details : ', error);
      this.toastr.error('While getting patients details', 'Error');
    }
  }

  async getAllUsersLastLoginData() {
    try {
      const res: any = await this.amService.getLastLoginOfUsers();
      // console.log('--Last Login Details Res : ', res);
      this.allUsersLastLoginList = res;
    } catch (error) {
      console.log('--Error while getting users last login details : ', error);
      this.toastr.error('While getting users last login details', 'Error');
    }
  }

  async getUsersByDept(roleName: string) {
    this.deptUsers = [];
    this.loader['userslist'] = true;
    if (roleName === 'ROLE_PATIENT') {
      this.deptUsersCols = [
        {
          keys: ['firstName', 'lastName'],
          label: 'Patient Name',
          type: 'link',
          linkFn: (item: any) =>
            '/admin/manager/patient-profile/' + item.userId,
        },
        {
          key: 'facilityName',
          label: 'Facility Name',
        },
        {
          key: 'cityName',
          label: 'City',
        },
        { key: 'dob', label: 'DOB', type: 'date', format: 'dd MMM yyyy' },
      ];
    } else if (roleName === 'ROLE_DOCTOR') {
      this.deptUsers.map(
        (item: any) => (item.firstName = 'Dr. ' + '' + item.firstName)
      );
      this.deptUsersCols = [
        {
          keys: ['firstName', 'lastName'],
          label: 'Doctor Name',
          type: 'link',
          linkFn: (item: any) => '/admin/manager/doctor-profile/' + item.userId,
        },
        {
          key: 'facilityName',
          label: 'Facility Name',
        },
        {
          key: 'cityName',
          label: 'City',
        },
        { key: 'dob', label: 'DOB', type: 'date', format: 'dd MMM yyyy' },
      ];
    } else {
      this.deptUsersCols = [
        {
          keys: ['firstName', 'lastName'],
          label: 'Name',
        },
        { key: 'mobile', label: 'Phone' },
        { key: 'email', label: 'Email' },
      ];
    }
    try {
      const res: any = await this.amService.getUsersByDepartment({
        roleName: roleName,
        facilityId: this.user.facilityId,
      });
      if (res && res.length > 0) {
        this.deptUsers = res;
        this.loader['userslist'] = false;
      } else {
        this.deptUsers = [];
        this.loader['userslist'] = false;
      }
    } catch (error) {
      console.log('--Error while getting users by role details : ', error);
      this.toastr.error('While getting users by role details', 'Error');
      this.isDeptLoading = false;
    }
  }

  setPieChartData(data: any): void {
    this.isChartReady = false; // Hide the chart
    this.deptUsers = [];

    setTimeout(() => {
      this.pieChartOptions = {
        series: data.map((dept: any) => dept.CNT),
        chart: {
          width: 400,
          type: 'pie',
          events: {
            dataPointSelection: async (event, chartContext, config) => {
              const clickedIndex = config.dataPointIndex;
              const label = this.pieChartOptions.labels[clickedIndex];
              const roleMap: any = {
                'Admin Manager': 'ROLE_ADMIN_MANAGER',
                Doctor: 'ROLE_DOCTOR',
                Nurse: 'ROLE_NURSE',
                Finance: 'ROLE_FINANCE',
                HR: 'ROLE_HR',
                'Lab Technician': 'ROLE_LAB_TECHNICIAN',
                'Super Admin': 'ROLE_EMR_SUPER_ADMIN',
                Patient: 'ROLE_PATIENT',
                Secretary: 'ROLE_SECRETARY',
                Pharmacists: 'ROLE_PHARMACISTS',
                'Medical Records': 'ROLE_MEDICAL_RECORDS',
                'Health Advisor': 'ROLE_HEALTH_ADVISOR',
              };

              const roleName = roleMap[label];
              if (roleName) {
                await this.getUsersByDept(roleName);
                this.currentDept = label;
              }
            },
          },
        },
        labels: this.usersDeptWiseCount.map((dept: any) => dept.profileName),
        responsive: [
          {
            breakpoint: 500,
            options: {
              chart: {
                width: 200,
              },
              legend: {
                position: 'bottom',
              },
            },
          },
        ],
      };
      this.isChartReady = true; // Re-render chart
    });
  }

  applyFilters2() {
    let filtered = [...this.deptUsers];

    if (this.searchQuery.trim()) {
      const lowerSearch = this.searchQuery.toLowerCase();
      filtered = filtered.filter((facility: any) =>
        Object.values(facility).some(
          (value: any) =>
            value && value.toString().toLowerCase().includes(lowerSearch)
        )
      );
    }

    // Update the total count for pagination
    this.totalFilteredCount = filtered.length;

    // Now apply pagination
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + Number(this.itemsPerPage);

    this.filteredDeptUsers = filtered.slice(start, end);
  }

  async getUsersDeptWiseCount() {
    try {
      const res: any = await this.amService.getUsersCountByDeptWise(
        this.user.facilityId
      );
      this.usersDeptWiseCount = res;

      if (res && res.length) {
        this.setPieChartData(res); // Only call this now
      }
    } catch (error) {
      console.log('--Error while getting users by role details : ', error);
      this.toastr.error('While getting users by role details', 'Error');
    } finally {
    }
  }

  onSearchChange() {
    this.currentPage = 1;
    this.applyFilters();
  }

  goToPage(page: any) {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage = page;
      this.applyFilters();
    }
  }

  totalPages() {
    return Math.ceil(this.totalFilteredCount / this.itemsPerPage);
  }

  onItemsPerPageChange() {
    this.currentPage = 1; // Reset to the first page
    this.applyFilters();
  }

  get visiblePages(): (number | string)[] {
    const total = this.totalPages();
    const current = this.currentPage;
    const delta = 2; // how many pages to show before/after current
    const range: (number | string)[] = [];

    const addPage = (num: number | string) => {
      if (!range.includes(num)) {
        range.push(num);
      }
    };

    addPage(1);

    if (current - delta > 2) addPage('...');

    for (let i = current - delta; i <= current + delta; i++) {
      if (i > 1 && i < total) addPage(i);
    }

    if (current + delta < total - 1) addPage('...');

    if (total > 1) addPage(total);

    return range;
  }

  convertTo12HourFormat(time: string): string {
    const [hourStr, minuteStr] = time.split(':');
    let hour = parseInt(hourStr, 10);
    const minute = minuteStr;
    const ampm = hour >= 12 ? 'PM' : 'AM';

    hour = hour % 12;
    hour = hour === 0 ? 12 : hour;

    const formattedHour = hour.toString().padStart(2, '0');

    return `${formattedHour}:${minute} ${ampm}`;
  }

  async fetchSecuredUrl(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.userAccountService.getSecureImgUrl({ fileUrl: url }).subscribe({
        next: (response: any) => {
          resolve(response['url']);
        },
        error: (error: any) => {
          this.toastr.error('', error.error.message, { timeOut: 3000 });
          reject(error);
        },
      });
    });
  }

  private getNormalizedPathname(filePath: string): string {
    if (!filePath) {
      console.error('Invalid file path:', filePath);
      return '';
    }

    try {
      const url = new URL(filePath, window.location.origin); // Handle relative paths
      return url.pathname.replace(/^\/+/, '');
    } catch (error) {
      console.error('Error parsing URL:', error, 'File Path:', filePath);
      return '';
    }
  }

  onActivityClick = (activity: any) => {
    this.router.navigate([
      '/health-advisor/patient-requests/',
      this.util.adminService.encrypt(activity.patientId),
    ]);
  };

  getUpcomingData() {
    const data = this.getUpcomingLimitAppointments.data() || [];
    const now = new Date();
    return data.filter((doc: any) => {
      const appointmentDateTime = new Date(
        `${doc.appointmentDate}T${doc.appointmentTime}`
      );
      return appointmentDateTime >= now;
    });
  }

  viewAllAppointments() {
    this.router.navigate(['/health-advisor/manage-appointments/']);
    // this.router.navigate([
    //   '/health-advisor/instant-appointment/patient-history/',
    // ]);
  }

  onAppointmentClick(app: any) {
    console.log('Appointment : ', app);

    if (
      app.appointmentStatus.includes('Completed') ||
      app.appointmentStatus.includes('Cancelled')
    ) {
      this.completedAppointmentsNavigations(app);
    } else {
      this.onGoingAppointmentsNavigations(app);
    }
  }

  completedAppointmentsNavigations(app: any) {
    if (app.userRoleName.includes('Doctor')) {
      this.util.router.navigate([
        '/health-advisor/view/doc-visit-details/',
        this.util.encrypt(app.patientId),
        this.util.encrypt(app.visitId),
      ]);
      return;
    }
    if (app.userRoleName.includes('Dietitian')) {
      this.util.router.navigate([
        '/health-advisor/view/dietician-assesment-form/',
        this.util.encrypt(app.patientId),
        this.util.encrypt(app.dietConsultationId),
      ]);
      return;
    }
    if (app.userRoleName.includes('Physiotherapist')) {
      this.util.router.navigate([
        '/health-advisor/view/rehab-assesment-form/',
        this.util.encrypt(app.patientId),
        this.util.encrypt(app.physioConsultationId),
      ]);
      return;
    }
    if (app.userRoleName.includes('Phlebotomist')) {
      this.util.router.navigate([
        '/health-advisor/view/phlebotomist-app-details/',
        this.util.encrypt(app.patientId),
        this.util.encrypt(app.appointmentId),
      ]);
      return;
    }
  }

  onGoingAppointmentsNavigations(app: any) {
    if (app.userRoleName.includes('Doctor')) {
      this.util.router.navigate([
        '/health-advisor/create/doc-visit-details/',
        this.util.encrypt(app.patientId),
        this.util.encrypt(app.visitId),
      ]);
      return;
    }
    if (app.userRoleName.includes('Phlebotomist')) {
      this.util.swal.fire(
        'Coming Soon',
        'Phlebotomist form Navigation',
        'info'
      );
      return;
    }
    if (app.userRoleName.includes('Dietitian')) {
      this.util.swal.fire('Coming Soon', 'Dietitian form Navigation', 'info');
      return;
    }
    if (app.userRoleName.includes('Physiotherapist')) {
      this.util.swal.fire(
        'Coming Soon',
        'Physiotherapist form Navigation',
        'info'
      );
      return;
    }
    if (app.userRoleName.includes('Phlebotomist')) {
      this.util.router.navigate([
        '/health-advisor/view/phlebotomist-app-details/',
        this.util.encrypt(app.patientId),
        this.util.encrypt(app.appointmentId),
      ]);
      return;
    }
  }
}
