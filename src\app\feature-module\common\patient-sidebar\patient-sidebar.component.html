<!-- Profile Sidebar -->
<div class="stickybar">
  <!-- Profile Sidebar -->
  <div
    class="patient-sidebar profile-sidebar profile-sidebar-new aos"
    data-aos="fade-right"
  >
    <div class="pro-widget-content widget-profile">
      <div class="profile-info-widget">
        <!-- <a  class="booking-doc-img">
          <img src="https://i.pravatar.cc/50?img=49" alt="User Image" />
        </a> -->
        <a class="booking-doc-img">
          <ng-container *ngIf="this.userData && this.userData.photoLinkUrl">
            <img
              [src]="this.userData.photoLinkUrl"
              [alt]="this.userData['firstName'] + this.userData['lastName']"
            />
          </ng-container>
          <img
            *ngIf="!this.userData || !this.userData.photoLinkUrl"
            src="assets/images/avatar-image.webp"
            alt="User Image"
          />
        </a>
        <div class="profile-det-info">
          <h3>
            <a
              >{{
                userData && userData.firstName ? userData.firstName : "N/A"
              }}
              {{ userData && userData.lastName ? userData.lastName : "N/A" }}</a
            >
          </h3>
          <div class="patient-details">
            <h5 class="mb-0">
              Patient ID :
              {{ userData && userData.userId ? userData.userId : "N/A" }}
            </h5>
          </div>
          <span
            class="text-capitalize mb-2"
            *ngIf="userData && userData.dob && userData.gender"
            >{{ userData && userData.gender ? userData.gender : "N/A" }} &nbsp;
            {{
              userData && userData.dob ? calculateAge(userData.dob) : "N/A"
            }}</span
          >
        </div>
      </div>
    </div>
    <div class="dashboard-widget">
      <nav class="dashboard-menu">
        <ul>
          <li routerLinkActive="active">
            <a [routerLink]="routes.patientDashboard">
              <i class="isax isax-category-2"></i>
              <span>Dashboard</span>
            </a>
          </li>
          <li routerLinkActive="active">
            <a routerLink="/patient/clinical-visits">
              <i class="fa fa-tents"></i>
              <span>Medical Camp</span>
              <!-- <span>Clinical Visits</span> -->
            </a>
          </li>
          <li [ngClass]="{ active: page === 'lab-tests' }">
            <a routerLink="/patient/lab-tests">
              <i class="fa fa-flask"></i>
              <span>Lab Tests</span>
            </a>
          </li>
          <li [ngClass]="{ active: page === 'vitals' }">
            <a routerLink="/patient/vitals">
              <i class="fa fa-heartbeat"></i>
              <span>Vitals</span>
            </a>
          </li>
          <!-- <li [ngClass]="{ active: page === 'tele-medicine-list' }">
            <a routerLink="/patient/tele-medicine-list">
              <i class="fa fa-user-md"></i>
              <span>Tele Medicine</span>
            </a>
          </li> -->
          <li [ngClass]="{ active: page === 'purchased-care-plans' }">
            <a (click)="goToCarePlansPage()">
              <i class="fa fa-notes-medical"></i>
              <span>Care Plans</span>
            </a>
          </li>
          <li [ngClass]="{ active: page === 'life-style' }">
            <a routerLink="/patient/life-style">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                class="bi bi-activity"
                viewBox="0 0 16 16"
                style="margin-right: 5px"
              >
                <path
                  fill-rule="evenodd"
                  d="M6 2a.5.5 0 0 1 .47.33L10 12.036l1.53-4.208A.5.5 0 0 1 12 7.5h3.5a.5.5 0 0 1 0 1h-3.15l-1.88 5.17a.5.5 0 0 1-.94 0L6 3.964 4.47 8.171A.5.5 0 0 1 4 8.5H.5a.5.5 0 0 1 0-1h3.15l1.88-5.17A.5.5 0 0 1 6 2"
                />
              </svg>
              <span>LifeStyle</span>
            </a>
          </li>

          <li routerLinkActive="active">
            <a routerLink="/patient/insurance">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                class="bi bi-shield-check"
                style="margin-right: 5px"
                viewBox="0 0 16 16"
              >
                <path
                  d="M5.338 1.59a61 61 0 0 0-2.837.856.48.48 0 0 0-.328.39c-.554 4.157.726 7.19 2.253 9.188a10.7 10.7 0 0 0 2.287 2.233c.346.244.652.42.893.533q.18.085.293.118a1 1 0 0 0 .101.025 1 1 0 0 0 .1-.025q.114-.034.294-.118c.24-.113.547-.29.893-.533a10.7 10.7 0 0 0 2.287-2.233c1.527-1.997 2.807-5.031 2.253-9.188a.48.48 0 0 0-.328-.39c-.651-.213-1.75-.56-2.837-.855C9.552 1.29 8.531 1.067 8 1.067c-.53 0-1.552.223-2.662.524zM5.072.56C6.157.265 7.31 0 8 0s1.843.265 2.928.56c1.11.3 2.229.655 2.887.87a1.54 1.54 0 0 1 1.044 1.262c.596 4.477-.787 7.795-2.465 9.99a11.8 11.8 0 0 1-2.517 2.453 7 7 0 0 1-1.048.625c-.28.132-.581.24-.829.24s-.548-.108-.829-.24a7 7 0 0 1-1.048-.625 11.8 11.8 0 0 1-2.517-2.453C1.928 10.487.545 7.169 1.141 2.692A1.54 1.54 0 0 1 2.185 1.43 63 63 0 0 1 5.072.56"
                />
                <path
                  d="M10.854 5.146a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7.5 7.793l2.646-2.647a.5.5 0 0 1 .708 0"
                />
              </svg>
              <span>Insurance</span>
            </a>
          </li>
          <!-- <li [ngClass]="{ active: page === 'appointments' }">
            <a routerLink="/patient/appointments">
              <i class="isax isax-calendar-1"></i>
              <span>Appointments</span>
            </a>
          </li> -->

          <!-- <li [ngClass]="{ active: page === 'prescription' }">
            <a routerLink="/patient/prescription">
              <i class="fa fa-prescription-bottle"></i>
              <span>Prescriptions</span>
            </a>
          </li> -->
          <!-- <li [ngClass]="{ active: page === 'follow-up' }">
            <a (click)="redirectToFacility()">
              routerLink="/patient/follow-up/facility/{{
                loggedInUser.facilityId
              }}/doctors"
              <i class="fa fa-calendar-check"></i>
              <span>Follow Ups</span>
            </a>
          </li> -->
          <!-- <li [ngClass]="{ active: page === 'lab' }">
            <a routerLink="/patient/lab">
              <i class="fa fa-flask"></i>
              <span>Laboratory</span>
            </a>
          </li> -->
          <!-- <li [ngClass]="{ active: page === 'family' }">
            <a routerLink="/patient/family">
              <i class="fa fa-users"></i>
              <span class="mx-1">Family Members</span>
            </a>
          </li> -->
          <!-- <li [ngClass]="{ active: page === 'dependants' }">
            <a
              routerLink="/patient/dependants"
              class="d-flex align-items-baseline gap-1"
            >
             
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M3.69 9.12a.88.88 0 0 0-.65-.28c-.41 0-.72.19-.92.58s-.15.76.17 1.11q1.77 1.59 2.25 2.25c.41.56.61 1.38.61 2.44c0 1.31.5 2.28 1.5 2.95c.56.44 1.17.77 1.85.99v-3.89c0-.94-.33-1.72-.96-2.35m8.92.05c-.62.62-.96 1.39-.96 2.3v3.93c.96-.34 1.76-.87 2.42-1.57c.65-.7.98-1.47.98-2.41c0-1.13.19-1.94.57-2.44c.09-.16.26-.36.53-.61c.23-.25.47-.49.71-.71c.23-.21.46-.43.68-.65l.33-.28a.9.9 0 0 0 .28-.66c0-.28-.09-.53-.28-.73s-.42-.3-.72-.3s-.5.09-.69.28M12 20c.69 0 1.36-.09 2-.28v-3.57c0-.59-.18-1.05-.59-1.49Q12.795 14 12 14c-.53 0-1 .2-1.38.61c-.4.39-.62.85-.62 1.45v3.66c.64.19 1.31.28 2 .28M9 8.5c0 .83-.67 1.5-1.5 1.5S6 9.33 6 8.5S6.67 7 7.5 7S9 7.67 9 8.5m9 0c0 .83-.67 1.5-1.5 1.5S15 9.33 15 8.5S15.67 7 16.5 7s1.5.67 1.5 1.5m-4.5-3c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5S11.17 4 12 4s1.5.67 1.5 1.5m0 5.5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5s1.5.67 1.5 1.5"
                />
              </svg>
              <span>Dependants</span>
            </a>
          </li> -->
          <!-- <li [ngClass]="{ active: page === 'medical-history' }">
            <a routerLink="/patient/medical-history">
              <i class="fa fa-file-medical-alt"></i>
              <span>Medical History</span>
            </a>
          </li> -->
          <!-- <li [ngClass]="{ active: page === 'pay' }">
            <a routerLink="/patient/pay">
              <i class="fa fa-credit-card"></i>
              <span>Payments</span>
            </a>
          </li> -->
          <!-- <li [ngClass]="{ active: page === 'chat' }">
            <a routerLink="/patient/chat">
              <i class="fa fa-envelope"></i>
              <span>Messages</span>
            </a>
          </li> -->

          <!-- <li [ngClass]="{ active: page === 'notifications' }">
            <a routerLink="/patient/notifications">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                class="bi bi-journal-medical"
                viewBox="0 0 16 16"
                style="margin-right: 5px"
              >
                <path
                  fill-rule="evenodd"
                  d="M8 4a.5.5 0 0 1 .5.5v.634l.549-.317a.5.5 0 1 1 .5.866L9 6l.549.317a.5.5 0 1 1-.5.866L8.5 6.866V7.5a.5.5 0 0 1-1 0v-.634l-.549.317a.5.5 0 1 1-.5-.866L7 6l-.549-.317a.5.5 0 0 1 .5-.866l.549.317V4.5A.5.5 0 0 1 8 4M5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5m0 2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5"
                />
                <path
                  d="M3 0h10a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-1h1v1a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H3a1 1 0 0 0-1 1v1H1V2a2 2 0 0 1 2-2"
                />
                <path
                  d="M1 5v-.5a.5.5 0 0 1 1 0V5h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1zm0 3v-.5a.5.5 0 0 1 1 0V8h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1zm0 3v-.5a.5.5 0 0 1 1 0v.5h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1z"
                />
              </svg>
              <span>Notifications</span>
            </a>
          </li> -->
          <!-- <li routerLinkActive="active">
            <a [routerLink]="['/patient/documents']">
              <i class="isax isax-note-1"></i>
              <span>Documents</span>
            </a>
          </li>
          <li routerLinkActive="active">
            <a routerLink="/patient/reviews/list">
              <i class="isax isax-star"></i>
              <span>Reviews</span>
            </a>
          </li> -->
          <!--
          <li [ngClass]="{ active: page === 'appointments' }">
            <a [routerLink]="routes.patientAppointment">
              <i class="isax isax-calendar-1"></i>
              <span>Appointments</span>
            </a>
          </li>
          <li [ngClass]="{ active: page === 'pharmacy' }">
            <a [routerLink]="routes.pharmacy">
              <i class="fas fa-prescription-bottle-alt"></i>
              <span>Pharmacy</span>
            </a>
          </li>
          <li routerLinkActive="active">
            <a routerLink="/family">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                class="bi bi-people"
                style="margin-right: 0.5rem"
                viewBox="0 0 16 16"
              >
                <path
                  d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1zm-7.978-1L7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002-.014.002zM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4m3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0M6.936 9.28a6 6 0 0 0-1.23-.247A7 7 0 0 0 5 9c-4 0-5 3-5 4q0 1 1 1h4.216A2.24 2.24 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816M4.92 10A5.5 5.5 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0m3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4"
                />
              </svg>
              <span>Family Members</span>
            </a>
          </li>
          <li routerLinkActive="active">
            <a routerLink="/medical-records">
              <i class="isax isax-note-21"></i>
              <span>Medical History</span>
            </a>
          </li>

          <li routerLinkActive="active">
            <a [routerLink]="routes.favourites">
              <i class="isax isax-star-1"></i>
              <span>Favourites</span>
            </a>
          </li>-->
          <li routerLinkActive="active">
            <a (click)="logout()">
              <i class="isax isax-logout"></i>
              <span>Logout</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
  <!-- /Profile Sidebar -->
</div>
<!-- / Profile Sidebar -->
