import { CommonModule, Location } from '@angular/common';
import {
  Component,
  OnInit,
  ViewChild,
  AfterViewInit,
  ElementRef,
  inject,
  signal,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { timeRangeValidator } from 'src/app/shared/validators/timeRangeValidator';
import Swal from 'sweetalert2';
import {
  BsDatepickerDirective,
  BsDatepickerConfig,
  BsDatepickerModule,
} from 'ngx-bootstrap/datepicker';
import { TimepickerModule } from 'ngx-bootstrap/timepicker';
import { Router } from '@angular/router';
import moment from 'moment';
import { AdminService } from 'src/app/admin/service/admin.service';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';
import { PatientService } from '../../patient.service';
import { injectMutation } from '@tanstack/angular-query-experimental';
import { SubmitBtnComponent } from 'src/app/shared/components/re-use/submit-btn/submit-btn.component';

@Component({
  selector: 'app-create-tele-medicine-appointment',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    BsDatepickerModule,
    TimepickerModule,
    SubmitBtnComponent,
  ],
  templateUrl: './create-tele-medicine-appointment.component.html',
  styleUrls: ['./create-tele-medicine-appointment.component.scss'],
})
export class CreateTeleMedicineAppointmentComponent implements OnInit {
  authService = inject(AuthService);
  util = inject(UtilFunctions);
  user = this.authService.getDataFromSession('user');

  currentStep = 1;
  totalSteps = 3;

  @ViewChild('dobPicker') dobPicker!: BsDatepickerDirective;
  datepickerConfig: Partial<BsDatepickerConfig> = {
    dateInputFormat: 'DD MMM YYYY', // Change this to your preferred format
    showWeekNumbers: false, // Optional: theme
  };
  maxDate = new Date();

  bookAppointmentForm!: FormGroup;
  isFormSubmitting = signal(false);

  constructor(
    private fb: FormBuilder,
    private location: Location,
    private router: Router,
    private adminService: AdminService,
    private patientService: PatientService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.bookAppointmentForm = this.fb.group(
      {
        appointmentType: new FormControl('Telemedicine'),
        appointmentDate: new FormControl('', Validators.required),
        status: new FormControl('pending'), // Booked, Confirmed,Cancelled
        fees: new FormControl(''),
        primaryStartTime: new FormControl('', Validators.required),
        primaryEndTime: new FormControl('', Validators.required),
        secondaryStartTime: new FormControl(''),
        secondaryEndTime: new FormControl(''),
        teritaryStartTime: new FormControl(''),
        teritaryEndTime: new FormControl(''),
      },
      { validators: timeRangeValidator }
    );
  }

  // Mutations
  createAppointmentMutation = injectMutation(() => ({
    mutationFn: (payload: any) =>
      this.patientService.createPatientAvailableSlots(payload),
    onSuccess: (res: any) => {
      if (res && res.error) {
        this.toastr.error('Error creating appointment');
        this.isFormSubmitting.set(false);
        return;
      }

      this.toastr.success('Appointment Created Successfully');
      this.location.back();
      this.isFormSubmitting.set(false);
    },
    onError: (error: any) => {
      this.toastr.error('Error creating appointment');
      this.isFormSubmitting.set(false);
    },
  }));

  openCalendar(): void {
    if (this.dobPicker) {
      this.dobPicker.show();
    }
  }

  goBack() {
    this.location.back();
  }

  goToStep(step: number): void {
    if (step >= 1 && step <= this.totalSteps) {
      this.currentStep = step;
    }
  }

  nextStep(): void {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  async confirmAppointment() {
    this.isFormSubmitting.set(true);
    if (this.bookAppointmentForm.invalid) {
      this.bookAppointmentForm.markAllAsTouched();
      Swal.fire('Please fix the time slots and required fields.', '', 'error');
      this.isFormSubmitting.set(false);
      return;
    }
    let payload = {
      patientId: this.user.userId,
      appointmentType: this.bookAppointmentForm.controls['appointmentType']
        .value
        ? this.bookAppointmentForm.controls['appointmentType'].value
        : '',
      requestedDate: this.bookAppointmentForm.controls['appointmentDate'].value
        ? moment(
            this.bookAppointmentForm.controls['appointmentDate'].value
          ).format('YYYY-MM-DD')
        : '',
      status: 'pending',
      createdDate: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      totalAmount: 0,
      paymentStatus: null,
      slots: [
        {
          slotType: 'Primary',
          slotOrder: 1,
          preferredDatetime: this.bookAppointmentForm.controls[
            'primaryStartTime'
          ].value
            ? moment(
                this.bookAppointmentForm.controls['primaryStartTime'].value
              ).format('HH:mm')
            : '',
          endTime: this.bookAppointmentForm.controls['primaryEndTime'].value
            ? this.mergeDateWithTimeOnly(
                moment(
                  this.bookAppointmentForm.controls['appointmentDate'].value
                ).format('YYYY-MM-DD'),
                this.bookAppointmentForm.controls['primaryEndTime'].value
              )
            : '',
        },
        {
          slotType: 'Secondary',
          slotOrder: 2,
          preferredDatetime: this.bookAppointmentForm.controls[
            'secondaryStartTime'
          ].value
            ? moment(
                this.bookAppointmentForm.controls['secondaryStartTime'].value
              ).format('HH:mm')
            : '',
          endTime: this.bookAppointmentForm.controls['secondaryEndTime'].value
            ? this.mergeDateWithTimeOnly(
                moment(
                  this.bookAppointmentForm.controls['appointmentDate'].value
                ).format('YYYY-MM-DD'),
                this.bookAppointmentForm.controls['secondaryEndTime'].value
              )
            : '',
        },
        {
          slotType: 'Tertiary',
          slotOrder: 3,
          preferredDatetime: this.bookAppointmentForm.controls[
            'teritaryStartTime'
          ].value
            ? moment(
                this.bookAppointmentForm.controls['teritaryStartTime'].value
              ).format('HH:mm')
            : '',
          endTime: this.bookAppointmentForm.controls['teritaryEndTime'].value
            ? this.mergeDateWithTimeOnly(
                moment(
                  this.bookAppointmentForm.controls['appointmentDate'].value
                ).format('YYYY-MM-DD'),
                this.bookAppointmentForm.controls['teritaryEndTime'].value
              )
            : '',
        },
      ],
    };
    console.log('Payload : ', payload);
    this.createAppointmentMutation.mutate(payload);
  }

  mergeDateWithTimeOnly(appointmentDate: string, startTime: string): string {
    const time = new Date(startTime);
    const [year, month, day] = appointmentDate.split('-').map(Number);

    const combined = new Date(
      Date.UTC(
        year,
        month - 1, // JS months are 0-based
        day,
        time.getUTCHours(),
        time.getUTCMinutes(),
        time.getUTCSeconds()
      )
    );

    return combined.toISOString();
  }

  get summary() {
    const {
      appointmentType,
      appointmentDate,
      primaryStartTime,
      primaryEndTime,
    } = this.bookAppointmentForm.value;
    return {
      appointmentType: appointmentType || '-',
      appointmentDate: appointmentDate || '-',
      timeRange:
        primaryStartTime && primaryEndTime
          ? `${moment(primaryStartTime).format('DD MM YYYY, HH:MM')} - ${moment(
              primaryStartTime
            ).format('DD MM YYYY, HH:MM')}`
          : '-',
    };
    moment(primaryStartTime).format('DD MM YYYY');
  }
  // Getters for form fields (for cleaner access)
  get appointmentType() {
    return this.bookAppointmentForm.get('appointmentType') as FormControl;
  }
  get appointmentDate() {
    return this.bookAppointmentForm.get('appointmentDate') as FormControl;
  }
  get primaryStartTime() {
    return this.bookAppointmentForm.get('primaryStartTime') as FormControl;
  }
  get primaryEndTime() {
    return this.bookAppointmentForm.get('primaryEndTime') as FormControl;
  }
  get secondaryStartTime() {
    return this.bookAppointmentForm.get('secondaryStartTime') as FormControl;
  }
  get secondaryEndTime() {
    return this.bookAppointmentForm.get('secondaryEndTime') as FormControl;
  }
  get teritaryStartTime() {
    return this.bookAppointmentForm.get('teritaryStartTime') as FormControl;
  }
  get teritaryEndTime() {
    return this.bookAppointmentForm.get('teritaryEndTime') as FormControl;
  }
}
