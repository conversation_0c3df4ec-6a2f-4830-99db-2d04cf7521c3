import { Component, inject, signal } from '@angular/core';
import { AdminService } from 'src/app/admin/service/admin.service';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { PhilebotomistService } from '../../../service/philebotomist.service';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { injectQuery } from '@tanstack/angular-query-experimental';
import Swal from 'sweetalert2';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { C } from '@angular/cdk/scrolling-module.d-ud2XrbF8';
import { CommonService } from 'src/app/shared/common/common.service';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';

@Component({
  selector: 'app-pbm-view-assigned-appointment',
  standalone: false,
  templateUrl: './pbm-view-assigned-appointment.component.html',
  styleUrl: './pbm-view-assigned-appointment.component.scss',
})
export class PbmViewAssignedAppointmentComponent {
  authService = inject(AuthService);
  adminService = inject(AdminService);
  philebotomistService = inject(PhilebotomistService);
  util = inject(UtilFunctions);
  router = inject(Router);
  activeRoute = inject(ActivatedRoute);
  location = inject(Location);
  fb = inject(FormBuilder);
  commonService = inject(CommonService);
  toastr = inject(ToastrService);

  cities = [];
  states = [];
  countries = [];

  addressForm!: FormGroup;

  user = this.authService.getDataFromSession('user');
  appointmentId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['appointmentId']
  );
  patientId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['patientId']
  );
  addressId = signal<number | null>(null);
  taskId = signal<number | null>(null);
  showAddressForm: boolean = false;
  changeAddress: boolean = false;
  selectedAddress: any | null = null;

  disableDeliveredBtn: boolean = true;

  constructor() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Queries
  appointmentDetails = injectQuery(() => ({
    queryKey: ['appointment-details', this.appointmentId, this.patientId],
    queryFn: async () => {
      const res: any = await this.philebotomistService.getAppointmentDetails({
        patientId: this.patientId,
        appointmentId: this.appointmentId,
      });
      return res.map((a: any) => {
        let rTests = '';

        if (a.recommendedTests) {
          rTests = a.recommendedTests;
        } else {
          a.packageList.forEach((p: any, i: number) => {
            if (i == a.packageList.length - 1) {
              rTests += p.name;
            } else {
              rTests += p.name + ', ';
            }
          });
        }
        return {
          ...a,
          recommendTests: rTests,
        };
      })[0];
    },
    refetchOnWindowFocus: false,
  }));

  addressDetails = injectQuery(() => ({
    queryKey: ['address-details', this.patientId],
    queryFn: async () => {
      const id = this.patientId;
      if (!id) return null;
      const res =
        await this.philebotomistService.getAppointmentAddressByPatientId({
          patientId: id,
        });
      return res;
    },
    refetchOnWindowFocus: false,
  }));

  phlebotomistTask = injectQuery(() => ({
    queryKey: ['phlebotomist-task', this.patientId],
    queryFn: async () => {
      const id = this.patientId;
      if (!id) return null;
      const task: any = await this.philebotomistService.getPhlebotomistTaskById(
        { patientId: Number(id), appointmentId: Number(this.appointmentId) }
      );
      console.log('--Task:--', task);
      this.taskId.set(task[0].id);
      return task;
    },
  }));

  buildAddressForm() {
    this.addressForm = this.fb.group({
      address: new FormControl(''),
      country: new FormControl(''),
      state: new FormControl(''),
      city: new FormControl(''),
      zipCode: new FormControl(''),
    });
  }

  // Functions
  goBack() {
    if (!this.showAddressForm) {
      this.location.back();
    } else {
      this.showAddressForm = false;
    }
  }

  async onChangeAddressRadio(value: boolean) {
    if (this.changeAddress) {
      this.showAddressForm = true;
      this.buildAddressForm();
      this.getAllCountries();
      this.setupFormListeners();
    } else {
      this.showAddressForm = false;
      this.changeAddress = false;
    }
  }

  updateAddress(address: any) {
    console.log('Address', address);
    this.selectedAddress = address;
    this.showAddressForm = true;
    this.changeAddress = true;
    this.buildAddressForm();
    this.setupFormListeners();
    this.addressId.set(address['id']);
    this.addressForm.patchValue({
      address: address['address'],
      zipCode: address['zipCode'],
    });
    this.getAllCountries();
  }

  cancelAddress() {
    this.changeAddress = false;
    this.showAddressForm = false;
  }

  private setupFormListeners() {
    this.addressForm
      .get('country')
      ?.valueChanges.subscribe((val) => this.getStatesByCountryId(val));
    this.addressForm
      .get('state')
      ?.valueChanges.subscribe((val) => this.getCitiesByStateId(val));
  }

  async getAllCountries() {
    try {
      const res: any = await this.commonService.getAllCountries();
      this.countries = Array.from(
        new Set(
          res.map((country: { name: any; id: any }) => ({
            name: country.name,
            value: country.id,
          }))
        )
      );
      if (this.addressId()) {
        let conId: any = this.countries.find(
          (con: any) => con.name == this.selectedAddress['country']
        );
        this.addressForm.controls['country'].patchValue(conId['value']);
      }
    } catch (error) {
      console.log('--Error while fetching countries--', error);
      this.toastr.error('Error while fetching countries');
      return;
    }
  }

  async getStatesByCountryId(countryId: any) {
    try {
      const res: any = await this.commonService.getStateByCountryId(countryId);

      this.states = Array.from(
        new Set(
          res.map((state: any) => ({ name: state.name, value: state.id }))
        )
      );
      if (this.addressId()) {
        let conId: any = this.states.find(
          (con: any) => con.name == this.selectedAddress['state']
        );
        this.addressForm.controls['state'].patchValue(conId['value']);
      }
    } catch (error) {
      console.log('--Error while fetching states--', error);
      this.toastr.error('Error while fetching states');
      return;
    }
  }

  async getCitiesByStateId(stateId: any) {
    try {
      const res: any = await this.commonService.getCityByStateId(stateId);

      this.cities = Array.from(
        new Set(res.map((city: any) => ({ name: city.name, value: city.id })))
      );
      if (this.addressId()) {
        let conId: any = this.cities.find(
          (con: any) => con.name == this.selectedAddress['city']
        );
        this.addressForm.controls['city'].patchValue(conId['value']);
      }
    } catch (error) {
      console.log('--Error while fetching cities--', error);
      this.toastr.error('Error while fetching cities');
      return;
    }
  }

  payload() {
    return {
      appointmentId: this.appointmentId ? Number(this.appointmentId) : 0,
      address: this.addressForm.value.address
        ? this.addressForm.value.address
        : '',
      city: this.addressForm.value.city ? this.addressForm.value.city : '',
      state: this.addressForm.value.state ? this.addressForm.value.state : '',
      country: this.addressForm.value.country
        ? this.addressForm.value.country
        : '',
      zipCode: this.addressForm.value.zipCode
        ? this.addressForm.value.zipCode
        : '',
      patientId: this.patientId,
      confirmedBy: Number(this.user.userId),
      confirmedDate: moment(new Date()).format('YYYY-MM-DD'),
    };
  }

  updatePayload() {
    return {
      appointmentId: this.appointmentId ? Number(this.appointmentId) : 0,
      address: this.addressForm.value.address
        ? this.addressForm.value.address
        : '',
      city: this.addressForm.value.city ? this.addressForm.value.city : '',
      state: this.addressForm.value.state ? this.addressForm.value.state : '',
      country: this.addressForm.value.country
        ? this.addressForm.value.country
        : '',
      zipCode: this.addressForm.value.zipCode
        ? this.addressForm.value.zipCode
        : '',
      patientId: this.patientId,
      confirmedBy: Number(this.user.userId),
      confirmedDate: moment(new Date()).format('YYYY-MM-DD'),
      id: this.addressId() ? Number(this.addressId()) : 0,
    };
  }

  createUpdateAddress() {
    if (this.addressForm.invalid) {
      this.toastr.error('', 'Please fill all required fields', {
        timeOut: 3000,
      });
      return;
    }

    if (this.addressId()) {
      const payload = this.updatePayload();
      // UPDATE
      this.philebotomistService.updateAppointmentAddress(payload).subscribe({
        next: (res: any) => {
          this.addressDetails.refetch();
          this.addressForm.reset();
          this.showAddressForm = false;
          this.changeAddress = false;
        },
        error: () => {
          this.toastr.error('', 'Failed to update address', {
            timeOut: 3000,
          });
        },
      });
    } else {
      // CREATE
      const payload = this.payload();
      this.philebotomistService.createAppointmentAddress(payload).subscribe({
        next: (res: any) => {
          this.addressDetails.refetch();
          this.addressForm.reset();
          this.showAddressForm = false;
          this.changeAddress = false;
        },
        error: () => {
          this.toastr.error('', 'Failed to create address', { timeOut: 3000 });
        },
      });
    }
  }

  updateCollectionStatus() {
    let payload = {
      phlebotomistId: this.user.userId,
      labId: 1,
      collectionTime: new Date(),
      id: Number(this.taskId()),
    };
    this.philebotomistService
      .updatePhlebotomistCollectionStatus(payload)
      .subscribe({
        next: (res: any) => {
          this.phlebotomistTask.refetch();
          this.toastr.success('Collection status updated successfully', '', {
            timeOut: 3000,
          });
        },
        error: () => {
          this.toastr.error('Failed to update collection status', '', {
            timeOut: 3000,
          });
        },
      });
  }

  updateDeliveredStatus() {
    let payload = {
      phlebotomistId: this.user.userId,
      labId: 1,
      deliveryTime: new Date(),
      id: Number(this.taskId()),
    };
    this.philebotomistService
      .updatePhlebotomistDeliverStatus(payload)
      .subscribe({
        next: (res: any) => {
          this.phlebotomistTask.refetch();
          this.toastr.success('Delivered status updated successfully', '', {
            timeOut: 3000,
          });
        },
        error: () => {
          this.toastr.error('Failed to update delivered status', '', {
            timeOut: 3000,
          });
        },
      });
  }

  get address() {
    return this.addressForm.get('address') as FormControl;
  }
  get country() {
    return this.addressForm.get('country') as FormControl;
  }
  get state() {
    return this.addressForm.get('state') as FormControl;
  }
  get city() {
    return this.addressForm.get('city') as FormControl;
  }
  get zipCode() {
    return this.addressForm.get('zipCode') as FormControl;
  }
}
