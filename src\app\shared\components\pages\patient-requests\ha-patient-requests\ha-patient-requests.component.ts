import { CommonModule, Location } from '@angular/common';
import { Component, inject } from '@angular/core';
import { AdminFriendlyPageWrapperComponent } from '../../../re-use/admin-friendly-page-wrapper/admin-friendly-page-wrapper.component';

import { AuthService } from 'src/app/shared/auth/auth.service';
import { AdminService } from 'src/app/admin/service/admin.service';
import { ActivatedRoute, Router } from '@angular/router';
import { HealthAdvisorService } from 'src/app/feature-module/health-advisor/service/health-advisor.service';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { LoadingSpinnerComponent } from 'src/app/shared/loading-spinner/loading-spinner.component';
import { ErrorAlertComponent } from 'src/app/shared/error-alert/error-alert.component';

@Component({
  selector: 'app-ha-patient-requests',
  imports: [
    CommonModule,
    AdminFriendlyPageWrapperComponent,
    LoadingSpinnerComponent,
    ErrorAlertComponent,
  ],
  templateUrl: './ha-patient-requests.component.html',
  styleUrl: './ha-patient-requests.component.scss',
})
export class HaPatientRequestsComponent {
  authService = inject(AuthService);
  adminService = inject(AdminService);
  haService = inject(HealthAdvisorService);
  util = inject(UtilFunctions);
  router = inject(Router);
  location = inject(Location);
  activeRoute = inject(ActivatedRoute);

  user = this.authService.getDataFromSession('user');
  patientId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['patientId']
  );

  constructor() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Queries
  patientRequests = injectQuery(() => ({
    queryKey: ['patient-requests', this.patientId],
    queryFn: async () => {
      const res: any = await this.haService.getAllPatientRequests({
        patientId: this.patientId,
      });
      return res;
    },
    refetchOnWindowFocus: false,
  }));

  // Mutations

  // Functions
  back = () => {
    this.location.back();
  };

  // Helper function to get status badge class
  getStatusBadgeClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'bg-warning text-dark';
      case 'confirmed':
        return 'bg-success';
      case 'cancelled':
        return 'bg-danger';
      case 'completed':
        return 'bg-primary';
      default:
        return 'bg-secondary';
    }
  }

  // Helper function to format time
  formatTime(time: string): string {
    if (!time) return '';
    try {
      const [hours, minutes] = time.split(':');
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? 'PM' : 'AM';
      const displayHour = hour % 12 || 12;
      return `${displayHour}:${minutes} ${ampm}`;
    } catch {
      return time;
    }
  }

  // Helper function to format date
  formatDate(date: string): string {
    if (!date) return '';
    try {
      return new Date(date).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch {
      return date;
    }
  }

  // Filter requests with preferredDatetime
  getRequestsWithTime() {
    const data = this.patientRequests.data();
    if (!data) return [];
    return data.filter(
      (request: any) =>
        request.preferredDatetime && request.preferredDatetime.trim() !== ''
    );
  }

  // Get requests without preferredDatetime
  getRequestsWithoutTime() {
    const data = this.patientRequests.data();
    if (!data) return [];
    return data.filter(
      (request: any) =>
        !request.preferredDatetime || request.preferredDatetime.trim() === ''
    );
  }
}
