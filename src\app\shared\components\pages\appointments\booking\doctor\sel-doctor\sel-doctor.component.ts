import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import moment from 'moment';
import {
  AppliedFilter,
  Doctor,
  FilterOption,
} from '../../../../book-appointment/ba-sel-doc/ba-sel-doc.component';
import Swal from 'sweetalert2';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { AdminService } from 'src/app/admin/service/admin.service';
import { HealthAdvisorService } from 'src/app/feature-module/health-advisor/service/health-advisor.service';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';
import { ActivatedRoute, Router } from '@angular/router';
import { PageWithSearchPaginationComponent } from 'src/app/shared/page-with-search-pagination/page-with-search-pagination.component';
import { ErrorAlertComponent } from 'src/app/shared/error-alert/error-alert.component';

@Component({
  selector: 'app-sel-doctor',
  imports: [
    CommonModule,
    FormsModule,
    PageWithSearchPaginationComponent,
    ErrorAlertComponent,
  ],
  templateUrl: './sel-doctor.component.html',
  styleUrl: './sel-doctor.component.scss',
})
export class SelDoctorComponent {
  authService = inject(AuthService);
  adminService = inject(AdminService);
  haService = inject(HealthAdvisorService);
  util = inject(UtilFunctions);
  router = inject(Router);
  activeRoute = inject(ActivatedRoute);

  user = this.authService.getDataFromSession('user');
  patientId = this.adminService.decrypt(
    this.activeRoute.snapshot.params['patientId']
  );
  bookAppointmentType = this.activeRoute.snapshot.queryParams['bat'];

  // Loading and selection states
  selectedDoctorId = signal<number | null>(null);

  // Filter related properties
  filterOptions: FilterOption[] = [
    {
      key: 'cost',
      label: 'Filter by Cost',
      type: 'number',
      placeholder: 'Enter maximum cost (e.g., 500)',
      icon: 'fas fa-rupee-sign', // or 'fas fa-money-bill-wave'
    },
    {
      key: 'location',
      label: 'Filter by City',
      type: 'text',
      placeholder: 'Enter city name',
      icon: 'fas fa-map-marker-alt',
    },
    {
      key: 'departmentName',
      label: 'Filter by Specialist Department',
      type: 'text',
      placeholder: 'Enter department name',
      icon: 'fas fa-stethoscope',
    },
    {
      key: 'facility',
      label: 'Filter by Facility',
      type: 'text',
      placeholder: 'Enter facility name',
      icon: 'fas fa-hospital',
    },
    {
      key: 'name',
      label: 'Filter by Name',
      type: 'text',
      placeholder: 'Enter doctor name',
      icon: 'fas fa-user-md',
    },
    {
      key: 'languages',
      label: 'Filter by Language',
      type: 'text',
      placeholder: 'Enter language',
      icon: 'fas fa-language',
    },
  ];

  appliedFilters = signal<AppliedFilter[]>([]);
  selectedFilterOption = signal<FilterOption | null>(null);
  filterInputValue = signal<string>('');
  showFilterDropdown = signal<boolean>(false);

  filteredDoctorsList = signal<any[]>([]);

  constructor() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Computed property to get available filter options (excluding already applied filters)
  get availableFilterOptions(): FilterOption[] {
    const appliedFilterKeys = this.appliedFilters().map((f) => f.key);
    return this.filterOptions.filter(
      (option) => !appliedFilterKeys.includes(option.key)
    );
  }

  // Computed property to get filtered doctors
  get filteredDoctors(): Doctor[] {
    if (!this.doctors.data()) return [];

    let filtered = this.doctors.data();
    const filters = this.appliedFilters();

    filters.forEach((filter) => {
      filtered = filtered.filter((doctor: any) => {
        switch (filter.key) {
          case 'cost':
            const maxCost = parseFloat(filter.value);
            const doctorCost = parseFloat(doctor.cost);
            return doctorCost <= maxCost;

          case 'location':
            return doctor.location
              .toLowerCase()
              .includes(filter.value.toLowerCase());

          case 'departmentName':
            return doctor.departmentName
              .toLowerCase()
              .includes(filter.value.toLowerCase());

          case 'facility':
            return doctor.facility
              .toLowerCase()
              .includes(filter.value.toLowerCase());

          case 'name':
            return doctor.name
              .toLowerCase()
              .includes(filter.value.toLowerCase());

          case 'languages':
            return doctor.languages
              .toLowerCase()
              .includes(filter.value.toLowerCase());

          default:
            return true;
        }
      });
    });

    return filtered;
  }

  // Queries
  doctors = injectQuery(() => ({
    queryKey: ['all-doctors'],
    queryFn: async () => {
      // const res: any = await this.haService.getAllDoctorsList(
      //   this.buildParamPayload()
      // );

      let userCoords: { lat: number; lon: number } | null = null;
      try {
        userCoords = await this.util.getCurrentCoords();
        console.log('User Coords : ', userCoords);
      } catch (err) {
        console.error('User location not available:', err);
      }

      const res: any =
        await this.util.doctorService.getAllDoctorDetailsAcrossFacilities({
          availableDate: moment(new Date()).format('YYYY-MM-DD'),
          todayDateTime: moment(new Date()).format('YYYY-MM-DD HH:mm'),
          lat: userCoords?.lat,
          lon: userCoords?.lon,
        });

      await Promise.all(
        res.map(async (doc: any) => {
          if (doc.image) {
            const pathname = this.util.getNormalizedPathname(doc.image);
            const securedUrl = await this.util.fetchSecuredUrl(pathname);
            doc.image = securedUrl;
          } else {
            doc.image = this.util.getNormalAvatar();
          }
        })
      );
      return res;
    },
    refetchOnWindowFocus: false,
  }));

  // Filter Functions
  selectFilterOption(option: FilterOption) {
    this.selectedFilterOption.set(option);
    this.filterInputValue.set('');
    this.showFilterDropdown.set(false);
  }

  searchFilter() {
    const selectedOption = this.selectedFilterOption();
    const inputValue = this.filterInputValue().trim();

    if (!selectedOption || !inputValue) {
      return;
    }

    // Add the filter to applied filters
    const newFilter: AppliedFilter = {
      key: selectedOption.key,
      label: selectedOption.label,
      value: inputValue,
    };

    this.appliedFilters.update((filters) => [...filters, newFilter]);

    // Reset the input
    this.selectedFilterOption.set(null);
    this.filterInputValue.set('');
  }

  removeFilter(filterToRemove: AppliedFilter) {
    this.appliedFilters.update((filters) =>
      filters.filter((f) => f.key !== filterToRemove.key)
    );
  }

  clearAllFilters() {
    this.appliedFilters.set([]);
    this.selectedFilterOption.set(null);
    this.filterInputValue.set('');
  }

  toggleFilterDropdown() {
    this.showFilterDropdown.update((show) => !show);
  }

  // Existing Functions (unchanged)
  back = () => {
    this.util.location.back();
  };

  buildParamPayload() {
    return {
      availableDate: moment(new Date()).format('YYYY-MM-DD'),
      todayDateTime: moment(new Date()).format('YYYY-MM-DD HH:mm'),
    };
  }

  selectDoctor(doctor: Doctor) {
    if (
      doctor.availability === 'Not Available' &&
      doctor.nextAvailable === 'Not Available'
    ) {
      Swal.fire('Doctor is not available', '', 'error');
      return;
    }

    this.selectedDoctorId.set(doctor.id);
    console.log('Selected Doctor : ', doctor);

    if (this.user.roleName.includes(this.util.ROLES.PATIENT)) {
      this.getPatientRoutings(doctor);
      return;
    }

    if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
      this.getHealthAdvisorRoutings(doctor);
      return;
    }

    if (this.user.roleName.includes(this.util.ROLES.ADMIN)) {
      this.getAdminRoutings(doctor);
      return;
    }

    if (this.user.roleName.includes(this.util.ROLES.DOCTOR)) {
      this.getDoctorRoutings(doctor);
      return;
    }

    if (this.user.roleName.includes(this.util.ROLES.NURSE)) {
      this.getNurseRoutings(doctor);
      return;
    }

    if (this.user.roleName.includes(this.util.ROLES.DIETITIAN)) {
      this.getDietitianRoutings(doctor);
      return;
    }

    if (this.user.roleName.includes(this.util.ROLES.PHYSIOTHERAPIST)) {
      this.getPhysioRoutings(doctor);
      return;
    }

    if (this.user.roleName.includes(this.util.ROLES.PHLEBOTOMIST)) {
      this.getPhlebotomistRoutings(doctor);
      return;
    }
  }

  trackByDoctorId(index: number, doctor: Doctor): number {
    return doctor.id;
  }

  // Utility methods for template
  getDoctorInitials(name: string): string {
    return name
      .split(' ')
      .map((n) => n.charAt(0))
      .join('')
      .toUpperCase();
  }

  formatExperience(experience: string | null): string {
    if (!experience) return '';
    return `${experience} years`;
  }

  formatRating(rating: number): string {
    return rating.toFixed(1);
  }

  formatDate(date: string): string {
    return moment(new Date(date)).format('ddd, MMM D');
  }

  getPatientRoutings(doctor: any) {
    if (doctor.roleName.includes('Doctor')) {
      this.router.navigate([
        '/patient/instant-appointment/book-doctor-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Nurse')) {
      this.router.navigate([
        '/patient/instant-appointment/book-nurse-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Dietitian')) {
      this.router.navigate([
        '/patient/instant-appointment/book-dietitian-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Physiotherapist')) {
      this.router.navigate([
        '/patient/instant-appointment/book-physio-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Phlebotomist')) {
      this.router.navigate([
        '/patient/instant-appointment/book-phlebotomist-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }
  }

  getHealthAdvisorRoutings(doctor: any) {
    if (doctor.roleName.includes('Doctor')) {
      this.router.navigate([
        '/health-advisor/instant-appointment/book-doctor-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Nurse')) {
      this.router.navigate([
        '/health-advisor/instant-appointment/book-nurse-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Dietitian')) {
      this.router.navigate([
        '/health-advisor/instant-appointment/book-dietitian-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Physiotherapist')) {
      this.router.navigate([
        '/health-advisor/instant-appointment/book-physio-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Phlebotomist')) {
      this.router.navigate([
        '/health-advisor/instant-appointment/book-phlebotomist-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }
  }

  getAdminRoutings(doctor: any) {
    if (doctor.roleName.includes('Doctor')) {
      this.router.navigate([
        '/admin/instant-appointment/book-doctor-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Nurse')) {
      this.router.navigate([
        '/admin/instant-appointment/book-nurse-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Dietitian')) {
      this.router.navigate([
        '/admin/instant-appointment/book-dietitian-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Physiotherapist')) {
      this.router.navigate([
        '/admin/instant-appointment/book-physio-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Phlebotomist')) {
      this.router.navigate([
        '/admin/instant-appointment/book-phlebotomist-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }
  }

  getDoctorRoutings(doctor: any) {
    if (doctor.roleName.includes('Doctor')) {
      this.router.navigate([
        '/doctor/instant-appointment/book-doctor-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Nurse')) {
      this.router.navigate([
        '/doctor/instant-appointment/book-nurse-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Dietitian')) {
      this.router.navigate([
        '/doctor/instant-appointment/book-dietitian-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Physiotherapist')) {
      this.router.navigate([
        '/doctor/instant-appointment/book-physio-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Phlebotomist')) {
      this.router.navigate([
        '/doctor/instant-appointment/book-phlebotomist-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }
  }

  getNurseRoutings(doctor: any) {
    if (doctor.roleName.includes('Doctor')) {
      this.router.navigate([
        '/nurse/instant-appointment/book-doctor-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Nurse')) {
      this.router.navigate([
        '/nurse/instant-appointment/book-nurse-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Dietitian')) {
      this.router.navigate([
        '/nurse/instant-appointment/book-dietitian-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Physiotherapist')) {
      this.router.navigate([
        '/nurse/instant-appointment/book-physio-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Phlebotomist')) {
      this.router.navigate([
        '/nurse/instant-appointment/book-phlebotomist-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }
  }

  getDietitianRoutings(doctor: any) {
    if (doctor.roleName.includes('Doctor')) {
      this.router.navigate([
        '/dietitian/instant-appointment/book-doctor-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Nurse')) {
      this.router.navigate([
        '/dietitian/instant-appointment/book-nurse-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Dietitian')) {
      this.router.navigate([
        '/dietitian/instant-appointment/book-dietitian-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Physiotherapist')) {
      this.router.navigate([
        '/dietitian/instant-appointment/book-physio-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Phlebotomist')) {
      this.router.navigate([
        '/dietitian/instant-appointment/book-phlebotomist-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }
  }

  getPhysioRoutings(doctor: any) {
    if (doctor.roleName.includes('Doctor')) {
      this.router.navigate([
        '/physio-rehab-coordinator/instant-appointment/book-doctor-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Nurse')) {
      this.router.navigate([
        '/physio-rehab-coordinator/instant-appointment/book-nurse-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Dietitian')) {
      this.router.navigate([
        '/physio-rehab-coordinator/instant-appointment/book-dietitian-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Physiotherapist')) {
      this.router.navigate([
        '/physio-rehab-coordinator/instant-appointment/book-physio-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Phlebotomist')) {
      this.router.navigate([
        '/physio-rehab-coordinator/instant-appointment/book-phlebotomist-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }
  }

  getPhlebotomistRoutings(doctor: any) {
    if (doctor.roleName.includes('Doctor')) {
      this.router.navigate([
        '/phlebotomist/instant-appointment/book-doctor-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Nurse')) {
      this.router.navigate([
        '/phlebotomist/instant-appointment/book-nurse-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Dietitian')) {
      this.router.navigate([
        '/phlebotomist/instant-appointment/book-dietitian-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Physiotherapist')) {
      this.router.navigate([
        '/phlebotomist/instant-appointment/book-physio-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }

    if (doctor.roleName.includes('Phlebotomist')) {
      this.router.navigate([
        '/phlebotomist/instant-appointment/book-phlebotomist-slot/',
        this.adminService.encrypt(this.patientId),
        this.adminService.encrypt(doctor.id),
        this.adminService.encrypt(doctor.facilityId),
        this.adminService.encrypt(doctor.departmentId),
      ]);
      return;
    }
  }
}
