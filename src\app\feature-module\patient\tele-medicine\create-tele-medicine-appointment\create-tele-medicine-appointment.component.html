<div class="main-container">
  <div class="appointment-card" [formGroup]="bookAppointmentForm">
    <!-- Header -->
    <div class="page-header pb-3">
      <div class="row align-items-center">
        <div class="col-md-8">
          <h1 class="page-title">Book Appointment</h1>
        </div>
        <div class="col-md-4 d-flex justify-content-end">
          <button class="btn btn-outline-secondary" (click)="goBack()">
            <i class="fas fa-arrow-left me-2"></i>Back
          </button>
        </div>
      </div>
    </div>

    <!-- Progress Steps -->
    <div class="progress-section py-3">
      <div class="step-buttons">
        <button
          class="step-btn"
          *ngFor="let step of [1, 2, 3]"
          [class.active]="currentStep === step"
          [class.completed]="step < currentStep"
          (click)="goToStep(step)"
        >
          <ng-container [ngSwitch]="step">
            <span *ngSwitchCase="1"
              ><i class="fas fa-user me-2"></i>Basic Info</span
            >
            <span *ngSwitchCase="2"
              ><i class="fas fa-clock me-2"></i>Select Slot</span
            >
            <span *ngSwitchCase="3"
              ><i class="fas fa-check me-2"></i>Confirm</span
            >
          </ng-container>
        </button>
      </div>
    </div>

    <!-- Content Area -->
    <div class="content-area pt-3">
      <!-- Step 1 -->
      <div class="step-content" [class.active]="currentStep === 1">
        <div class="step-header justify-content-between d-flex">
          <h2 class="step-title">Basic Information</h2>
          <div class="step-actions">
            <button class="btn btn-success" (click)="nextStep()">
              Next <i class="fas fa-arrow-right ms-2"></i>
            </button>
          </div>
        </div>

        <div class="row g-4">
          <!-- Appointment Type -->
          <div class="col-md-6">
            <label class="form-label"
              >Appointment Type <span class="text-danger">*</span></label
            >
            <select class="form-control" formControlName="appointmentType">
              <option value="">Select Appointment Type</option>
              <option value="Outpatient">Outpatient</option>
              <option value="Telemedicine">Telemedicine</option>
            </select>
          </div>
          <!-- Start Date -->
          <div class="col-md-6">
            <label class="form-label"
              >Date
              <span class="text-danger">*</span>
            </label>
            <div class="form-icon">
              <input
                type="text"
                class="form-control datetimepicker"
                id="dob"
                placeholder="DD MMM YYYY"
                [formControl]="appointmentDate"
                name="dob"
                bsDatepicker
                #dobPicker="bsDatepicker"
                [bsConfig]="datepickerConfig"
                autocomplete="off"
                autocorrect="off"
                spellcheck="false"
                [minDate]="maxDate"
              />

              <span
                class="icon"
                (click)="dobPicker.show()"
                style="cursor: pointer"
                ><i class="fa-regular fa-calendar-days"></i
              ></span>
            </div>
            <div
              *ngIf="
                appointmentDate.invalid &&
                (appointmentDate.dirty || appointmentDate.touched)
              "
              class="text-danger mt-1"
            >
              <small *ngIf="appointmentDate.errors?.['required']"
                >Date is required.</small
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2 -->
      <div class="step-content" [class.active]="currentStep === 2">
        <div class="step-header justify-content-between d-flex">
          <h2 class="step-title">Select Time Slot</h2>
          <div class="step-actions">
            <button class="btn btn-danger" (click)="previousStep()">
              <i class="fas fa-arrow-left me-2"></i>Previous
            </button>
            <button class="btn btn-success" (click)="nextStep()">
              Next <i class="fas fa-arrow-right ms-2"></i>
            </button>
          </div>
        </div>

        <!-- Primary Slot -->
        <div class="time-slot-card">
          <div class="slot-header">
            <span class="slot-badge primary">Primary </span>
            <p class="slot-description">
              Your preferred time slot <span class="text-danger">*</span>
            </p>
          </div>
          <div class="row g-3">
            <div class="col-md-6">
              <label class="form-label">Start Time</label>
              <timepicker
                [formControlName]="'primaryStartTime'"
                [mousewheel]="true"
              ></timepicker>
              <div
                *ngIf="
                  primaryStartTime.invalid &&
                  (primaryStartTime.dirty || primaryStartTime.touched)
                "
                class="text-danger mt-1"
              >
                <small *ngIf="primaryStartTime.errors?.['required']"
                  >Start Time is required.</small
                >
              </div>
            </div>
            <div class="col-md-6">
              <label class="form-label">End Time</label>
              <timepicker
                [formControlName]="'primaryEndTime'"
                [mousewheel]="true"
              ></timepicker>
              <div
                *ngIf="
                  primaryEndTime.invalid &&
                  (primaryEndTime.dirty || primaryEndTime.touched)
                "
                class="text-danger mt-1"
              >
                <small *ngIf="primaryEndTime.errors?.['required']"
                  >End Time is required.</small
                >
              </div>
            </div>
            <div *ngIf="bookAppointmentForm.hasError('primarySlotInvalid')">
              <div class="text-danger">
                Primary slot must be between 15–60 mins
              </div>
            </div>
          </div>
        </div>

        <!-- Secondary Slot -->
        <div class="time-slot-card">
          <div class="slot-header">
            <span class="slot-badge secondary">Secondary</span>
            <p class="slot-description">Alternative time slot</p>
          </div>
          <div class="row g-3">
            <div class="col-md-6">
              <!-- Start Time -->
              <label class="form-label">Start Time</label>
              <timepicker
                [formControlName]="'secondaryStartTime'"
                [mousewheel]="true"
              ></timepicker>
            </div>
            <div class="col-md-6">
              <label class="form-label">End Time</label>
              <timepicker
                [formControlName]="'secondaryEndTime'"
                [mousewheel]="true"
              ></timepicker>
            </div>
            <div *ngIf="bookAppointmentForm.hasError('secondarySlotInvalid')">
              <div class="text-danger">
                Secondary slot must be between 15–60 mins
              </div>
            </div>
          </div>
        </div>

        <!-- Tertiary Slot -->
        <div class="time-slot-card">
          <div class="slot-header">
            <span class="slot-badge tertiary">Tertiary</span>
            <p class="slot-description">Backup time slot</p>
          </div>
          <div class="row g-3">
            <div class="col-md-6">
              <label class="form-label">Start Time</label>
              <timepicker
                [formControlName]="'teritaryStartTime'"
                [mousewheel]="true"
              ></timepicker>
            </div>
            <div class="col-md-6">
              <label class="form-label">End Time</label>
              <timepicker
                [formControlName]="'teritaryEndTime'"
                [mousewheel]="true"
              ></timepicker>
            </div>
            <div *ngIf="bookAppointmentForm.hasError('tertiarySlotInvalid')">
              <div class="text-danger">
                Teritary slot must be between 15–60 mins
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3 -->
      <div class="step-content" [class.active]="currentStep === 3">
        <div class="step-header justify-content-between d-flex">
          <h2 class="step-title">Confirm Appointment</h2>
          <div class="step-actions">
            <button class="btn btn-danger" (click)="previousStep()">
              <i class="fas fa-arrow-left me-2"></i>Previous
            </button>
            <!-- <button class="btn btn-success" (click)="confirmAppointment()">
              <i class="fas fa-calendar-check me-2"></i>Confirm Booking
            </button> -->
            <SubmitBtn
              label="Confirm Booking"
              loadingLabel="Confirming..."
              (click)="confirmAppointment()"
              [isSubmitting]="isFormSubmitting()"
              icon="fas fa-calendar-check me-2"
              color="success"
            ></SubmitBtn>
          </div>
        </div>

        <div class="alert alert-info">
          <i class="fas fa-info-circle me-2"></i>
          Please review your appointment details before confirming.
        </div>

        <div class="summary-card">
          <h5 class="mb-3">Appointment Summary</h5>
          <div class="summary-item">
            <span class="summary-label">Appointment Type:</span>
            <span class="summary-value">{{ summary.appointmentType }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Date:</span>
            <span class="summary-value" *ngIf="summary.appointmentDate">{{
              summary.appointmentDate
                ? (summary.appointmentDate | date : "dd-MM-YYYY")
                : ""
            }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Primary Time:</span>
            <span class="summary-value">{{ summary.timeRange }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Status:</span>
            <span class="badge bg-warning">Pending Confirmation</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
