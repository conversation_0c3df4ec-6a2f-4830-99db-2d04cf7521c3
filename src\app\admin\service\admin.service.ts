import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { environment } from 'src/environments/environment';
import * as CryptoJS from 'crypto-js';

@Injectable({
  providedIn: 'root',
})
export class AdminService {
  private apiUrl: string = environment.apiUrl;

  constructor(private http: HttpClient, private authService: AuthService) {}

  // encrypt(value: string): string {
  //   const rawKey = this.authService.getBasicAuth();

  //   if (!rawKey || typeof rawKey !== 'string') {
  //     throw new Error('Invalid secret key');
  //   }

  //   const secretKey = CryptoJS.SHA256(rawKey).toString(CryptoJS.enc.Hex); // ✅ HASH the key

  //   const stringValue = String(value); // 👈 convert number to string
  //   const encrypted = CryptoJS.AES.encrypt(stringValue, secretKey).toString();

  //   return encodeURIComponent(encrypted); // ✅ URL safe
  // }

  // decrypt(encryptedValue: string): string {
  //   const rawKey = this.authService.getBasicAuth();

  //   if (!rawKey || typeof rawKey !== 'string') {
  //     throw new Error('Invalid secret key');
  //   }

  //   const secretKey = CryptoJS.SHA256(rawKey).toString(CryptoJS.enc.Hex);
  //   const decoded = decodeURIComponent(encryptedValue);
  //   const bytes = CryptoJS.AES.decrypt(decoded, secretKey);
  //   return bytes.toString(CryptoJS.enc.Utf8);
  // }

  encrypt(id: any) {
    if (id == null) {
      console.warn('Trying to encrypt null/undefined ID');
      return '';
    }
    const value = String(id);
    const secretKey = this.authService.getBasicAuth();
    const encrypted = CryptoJS.AES.encrypt(value, secretKey).toString();
    return this.toBase64Url(encrypted); // ✅ convert to URL-safe
  }

  toBase64Url(str: string): string {
    return str.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  }

  decrypt(encrypted: string): string {
    if (!encrypted) {
      console.warn('Trying to decrypt an empty string');
      return '';
    }

    const encryptedBase64 = this.fromBase64Url(encrypted);
    const secretKey = this.authService.getBasicAuth();

    try {
      const bytes = CryptoJS.AES.decrypt(encryptedBase64, secretKey);
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);
      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      return '';
    }
  }

  fromBase64Url(str: string): string {
    // Convert from URL-safe Base64 to standard Base64
    str = str.replace(/-/g, '+').replace(/_/g, '/');

    // Pad with `=` if needed
    while (str.length % 4) {
      str += '=';
    }

    return str;
  }

  // decrypt(base64UrlStr: string): string {
  //   const secretKey = this.authService.getBasicAuth();
  //   const base64 = this.fromBase64Url(base64UrlStr);
  //   const bytes = CryptoJS.AES.decrypt(base64, secretKey);
  //   return bytes.toString(CryptoJS.enc.Utf8);
  // }

  // fromBase64Url(str: string): string {
  //   // Replace back to standard base64
  //   let base64 = str.replace(/-/g, '+').replace(/_/g, '/');
  //   // Pad with `=` if needed
  //   while (base64.length % 4) {
  //     base64 += '=';
  //   }
  //   return base64;
  // }

  createActivity(payload: any) {
    const endPoint = this.apiUrl + `activity/createactivities`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  getActivities() {
    const endPoint = this.apiUrl + `activity/getallactivities`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  getAllCollabrationTypes() {
    const endPoint = this.apiUrl + `activity/getallcollabtypes`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  createCollabrationTypes(data: any) {
    const endPoint = this.apiUrl + `activity/createcollabtypes`;
    return this.http.post<any>(endPoint, data);
  }

  updateCollabrationTypes(data: any) {
    const endPoint = this.apiUrl + `activity/updatecollabtypes`;
    return this.http.post<any>(endPoint, data);
  }

  deleteCollborationType(id: any) {
    const endPoint = this.apiUrl + `activity/deletecollabtype/${id}`;
    return this.http.delete<any>(endPoint);
  }

  getCollaborationFacilities(activityId: any) {
    const endPoint =
      this.apiUrl + `activity/getbothinternalcollaborationfacilities`;
    return firstValueFrom(this.http.post<any>(endPoint, { activityId }));
  }

  getCollaborationUsersByCollabId(collabId: any) {
    const endPoint = this.apiUrl + `facility/getcollabusersbycollabid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id: collabId }));
  }

  getExternalFacilityByFacilityId(facilityId: any) {
    const endPoint = this.apiUrl + `facility/getallexternalfacilities`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { hostFacilityId: facilityId })
    );
  }

  createActivityCollaboration(payload: any) {
    const endPoint = this.apiUrl + `activity/createactivitycollab`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  createcCollabUsers(payload: any) {
    const endPoint = this.apiUrl + `facility/createcollabusers`;
    return this.http.post(endPoint, payload);
  }

  createFacilityCollaboration(payload: any) {
    const endPoint = this.apiUrl + `facility/createfacilitycollab`;
    return this.http.post(endPoint, payload);
  }

  getAllPatientsListForActivitySelection() {
    const endPoint = this.apiUrl + `user-accounts/getlistallpatients`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  getAllDiagnosisList() {
    const endPoint = this.apiUrl + `activity/getallmasteraiagnosistitlelist`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  selectPatientAndEnrollToActivity(payload: any) {
    const endPoint = this.apiUrl + `user-accounts/createpatientenroll`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getEntrolledPatientsByActivityId(activityId: any, facilityId: any) {
    const endPoint = this.apiUrl + `user-accounts/getlistallpatientenrollments`;
    return firstValueFrom(
      this.http.post<any>(endPoint, {
        activityId: activityId,
        facilityId: facilityId,
      })
    );
  }

  getPatMedHistoryByVisitPatId(visitId: any, patientId: any) {
    const endPoint = this.apiUrl + `activity/getpatmedihistbyvisitidpatid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, {
        visitId: visitId,
        patientId: patientId,
      })
    );
  }

  deleteEnrolledUserFromActivity(eId: any) {
    const endPoint = this.apiUrl + `user-accounts/deleteuserenrollment/${eId}`;
    return firstValueFrom(this.http.delete<any>(endPoint));
  }

  createNewPatientEntrollment(payload: any) {
    const endPoint = this.apiUrl + `user-accounts/createpatientenrollment`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  createMedicalForm(payload: any) {
    const endPoint = this.apiUrl + `activity/createmedicalform`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  updateMedicalForm(payload: any) {
    const endPoint = this.apiUrl + `activity/updatepatmedicalform`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  // Care Plan
  createCarePlan(payload: any) {
    const endPoint = this.apiUrl + `activity/createcareplan`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  updateCarePlan(payload: any) {
    const endPoint = this.apiUrl + `activity/updatecareplan`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getCarePlanByCareId(careId: any) {
    const endPoint = this.apiUrl + `activity/getcareplanbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, {
        id: careId,
      })
    );
  }

  deleteCarePlan(cid: any) {
    const endPoint = this.apiUrl + `activity/deletecareplan/${cid}`;
    return this.http.delete<any>(endPoint);
  }
  getAllCarePlans() {
    const endPoint = this.apiUrl + `activity/getallcareplans`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  createCarePlanActivity(payload: any) {
    const endPoint = this.apiUrl + `activity/createcareplanactivity`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getCarePlanActivityByPlanId(planId: any) {
    const endPoint = this.apiUrl + `activity/getcareplanactivitybyplanid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, {
        planId: planId,
      })
    );
  }

  updateCarePlanActivity(payload: any) {
    const endPoint = this.apiUrl + `activity/updatecareplanactivity`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getCarePlanActivityByActivityId(careId: any) {
    const endPoint = this.apiUrl + `activity/getcareplanactivitybyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, {
        activityId: careId,
      })
    );
  }

  deleteCarePlanActivity(cid: any) {
    const endPoint = this.apiUrl + `activity/deletecareplanactivity/${cid}`;
    return this.http.delete<any>(endPoint);
  }

  createCarePlanPurchase(payload: any) {
    const endPoint = this.apiUrl + `activity/createcareplanpurchase`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getCarePlanPurchaseByPlanId(planId: any) {
    const endPoint = this.apiUrl + `activity/getcareplanpurchasebyplanid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, {
        planId: planId,
      })
    );
  }

  createCarePlanActivityTracking(payload: any) {
    const endPoint = this.apiUrl + `activity/createcareplanactivitytracking`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getCarePlanActivityTrackingByPurchaseId(purchaseId: any) {
    const endPoint =
      this.apiUrl + `activity/getcareplanactivitytracbypurchaseid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, {
        purchaseId: purchaseId,
      })
    );
  }

  getCarePlanActivityTrackingByActivityId(activityId: any) {
    const endPoint =
      this.apiUrl + `activity/getcareplanactivitytracbyactivityid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, {
        activityId: activityId,
      })
    );
  }

  // Packages
  createLabPackages(payload: any) {
    const endPoint = this.apiUrl + `lab/createlabpackages`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getLabPackageById(packageId: any) {
    const endPoint = this.apiUrl + `lab/getlabpackbypackageid`;
    return firstValueFrom(this.http.post<any>(endPoint, { packageId }));
  }

  getAllLabPackages() {
    const endPoint = this.apiUrl + `lab/getalllabpackages`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  updateLabPackages(payload: any) {
    const endPoint = this.apiUrl + `lab/updatelabpackages`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  deleteLabPackages(packageId: any) {
    const endPoint = this.apiUrl + `lab/deactivelabpackbypackageid`;
    return this.http.post<any>(endPoint, { packageId });
  }

  // Lab Test
  createLabPackageTest(payload: any) {
    const endPoint = this.apiUrl + `lab/createlabpackagetest`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getlabpacktestbytestid(testId: any) {
    const endPoint = this.apiUrl + `lab/getlabpacktestbytestid`;
    return firstValueFrom(this.http.post<any>(endPoint, { testid: testId }));
  }

  getAllLabPackageTest() {
    const endPoint = this.apiUrl + `lab/getalllabpackagetest`;
    return firstValueFrom(this.http.get<any>(endPoint));
  }

  updateLabPackageTest(payload: any) {
    const endPoint = this.apiUrl + `lab/updatelabpackagetest`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  deleteLabPackageTest(packageId: any) {
    const endPoint = this.apiUrl + `lab/deactivelabpacktestbytestid`;
    return this.http.post<any>(endPoint, { testid: packageId });
  }

  // Telemedicine
  createTelemedicine(payload: any) {
    const endPoint = this.apiUrl + `patients/createtelemedappointment`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getFullDietDetails(payload: any) {
    const endPoint = this.apiUrl + `activity/getfulldietdetails`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getFullRehabDetails(payload: any) {
    const endPoint = this.apiUrl + `activity/getallrehabcaredata`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  // Patient Consultation
  createPatientConsultation(payload: any) {
    const endPoint = this.apiUrl + `activity/createconsultation`;
    return this.http.post<any>(endPoint, payload);
  }

  updatePatientConsultation(payload: any) {
    const endPoint = this.apiUrl + `activity/updateconsultation`;
    return this.http.post<any>(endPoint, payload);
  }

  getPatientConsultationById(id: any) {
    const endPoint = this.apiUrl + `activity/getconsultationbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  updateConsultationStatus(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updateappointmentstatusCompleted`;
    return this.http.post<any>(endPoint, payload);
  }

  updatePhysioConsultationStatus(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/updateappointmentstatusCompleted`;
    return this.http.post<any>(endPoint, payload);
  }

  updateAppointmentOnGoingStatus(appointmentId: any) {
    const endPoint = this.apiUrl + `patients/updateappointmentongoingstatus`;
    return this.http.post<any>(endPoint, { appointmentId });
  }

  getFullDietData(payload: any) {
    const endPoint = this.apiUrl + `dietitian/getdietfulldetailsbypatientid`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getCompletedStatusByAppointmentId(payload: any) {
    const endPoint = this.apiUrl + `patients/getappointmentstatus`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getFullPhysioData(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/getphysiofulldetailsbypatientid`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  // Anthrometric
  createAntrometic(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createanthropometricentry`;
    return this.http.post<any>(endPoint, payload);
  }

  createMedicalCondition(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createdietmedicalcondition`;
    return this.http.post<any>(endPoint, payload);
  }

  updateAntrometic(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updateanthropometricentry`;
    return this.http.post<any>(endPoint, payload);
  }

  updateMedicalCondition(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatedietmedicalcondition`;
    return this.http.post<any>(endPoint, payload);
  }

  getAllAntrometricByConsulId(consultationId: any) {
    const endPoint = this.apiUrl + `dietitian/getanthropometricentrybyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: consultationId })
    );
  }

  getMedicalConditionByConsulId(consultationId: any) {
    const endPoint = this.apiUrl + `dietitian/getdietmedicalconditionbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: consultationId })
    );
  }

  getAntrometricBylId(id: any) {
    const endPoint = this.apiUrl + `activity/getanthropometricbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  // Dietary Assessment
  createDietaryAssesment(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createdietaryassessment`;
    // const endPoint = this.apiUrl + `activity/createdietaryassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  updateDietaryAssesment(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatedietaryassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  getDietaryAssesmentById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getdietaryassessmentbyid`;
    // const endPoint = this.apiUrl + `activity/getdietaryassessmentbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: id })
    );
  }

  getDietaryRecallById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getdietaryrecallentrybyid`;
    // const endPoint = this.apiUrl + `activity/getdietaryassessmentbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: id })
    );
  }

  createDietaryRecall(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createdietaryrecallentry`;
    // const endPoint = this.apiUrl + `activity/createdietaryassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  updateDietaryRecall(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatedietaryrecallentry`;
    return this.http.post<any>(endPoint, payload);
  }

  // Consultation Attachment
  createConsultationAttachment(payload: any) {
    const endPoint = this.apiUrl + `activity/createconsultationattachment`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  updateConsultationAttachment(payload: any) {
    const endPoint = this.apiUrl + `activity/updateconsultationattachment`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getConsultationAttachmentById(id: any) {
    const endPoint = this.apiUrl + `activity/getconsultattachmentbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  // LifeStyle
  createLifeStyle(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createlifestyleassessment`;
    // const endPoint = this.apiUrl + `activity/createlifestyleassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  createLifeStylePreferance(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createlifestylepreference`;
    // const endPoint = this.apiUrl + `activity/createlifestyleassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  updateLifeStyle(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatelifestyleassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  updateLifeStylPreferance(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatelifestylepreference`;
    return this.http.post<any>(endPoint, payload);
  }

  getLifeStyleById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getlifestyleassessmentbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: id })
    );
  }

  getLifeStylePreferanceById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getlifestylepreferencebyid`;
    // const endPoint = this.apiUrl + `activity/getlifestyleassessmentbyconsultid`;
    return firstValueFrom(this.http.post<any>(endPoint, { lifestyleId: id }));
  }

  // Nutrition Care Plan
  createNutritionCarePlan(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createnutricareplan`;
    // const endPoint = this.apiUrl + `activity/createnutritioncareplan`;
    return this.http.post<any>(endPoint, payload);
  }
  createNutritionCareItems(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createnutricareplanitem`;
    // const endPoint = this.apiUrl + `activity/createnutritioncareplan`;
    return this.http.post<any>(endPoint, payload);
  }

  updateNutritionCarePlan(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatenutricareplan`;
    return this.http.post<any>(endPoint, payload);
  }

  updateNutritionCarePlanItems(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatenutricareplanitem`;
    return this.http.post<any>(endPoint, payload);
  }

  getNutritionCarePlanById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getnutricareplanbyid`;
    // const endPoint = this.apiUrl + `activity/getnutritioncareplanbyconsultid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: id })
    );
  }

  getNutritionCarePlanItemsById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getnutricareplanitembyid`;
    // const endPoint = this.apiUrl + `activity/getnutritioncareplanbyconsultid`;
    return firstValueFrom(this.http.post<any>(endPoint, { carePlanId: id }));
  }

  // Nutrition Diganosis
  createNutritionDiganosis(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createnutrisummaries`;
    // const endPoint = this.apiUrl + `activity/createnutritiondiagnosis`;
    return this.http.post<any>(endPoint, payload);
  }
  createNutritionGoals(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createnutrisummarygoal`;
    // const endPoint = this.apiUrl + `activity/createnutritiondiagnosis`;
    return this.http.post<any>(endPoint, payload);
  }

  updateNutritionDiganosis(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatenutrisummaries`;
    return this.http.post<any>(endPoint, payload);
  }

  updateNutritionGoals(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatenutrisummarygoal`;
    return this.http.post<any>(endPoint, payload);
  }

  getNutritionDiganosisById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getnutrisummariesbyid`;
    // const endPoint = this.apiUrl + `activity/getnutritiondiagnosisbyconsultid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: id })
    );
  }

  getNutritionGoalsById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getnutrisummarygoalbyid`;
    // const endPoint = this.apiUrl + `activity/getnutritiondiagnosisbyconsultid`;
    return firstValueFrom(this.http.post<any>(endPoint, { summaryId: id }));
  }

  // follow Up
  createFollowAttachment(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createdietpatientattachment`;
    // const endPoint = this.apiUrl + `activity/createconsultationattachment`;
    return this.http.post<any>(endPoint, payload);
  }

  createFollowAttachmentResource(payload: any) {
    const endPoint =
      this.apiUrl + `dietitian/createpatientattachmentsharesource`;
    // const endPoint = this.apiUrl + `activity/createconsultationattachment`;
    return this.http.post<any>(endPoint, payload);
  }

  createFollowUp(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createdietfollowupplan`;
    return this.http.post<any>(endPoint, payload);
  }

  createDietPlan(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createdietplanitems`;
    return this.http.post<any>(endPoint, payload);
  }

  createPatientFeedback(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createpatientfeedback`;
    return this.http.post<any>(endPoint, payload);
  }

  createPatientConsent(payload: any) {
    const endPoint = this.apiUrl + `dietitian/createpatientconsentandremark`;
    return this.http.post<any>(endPoint, payload);
  }

  updateDietPlan(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatedietplanitem`;
    return this.http.patch<any>(endPoint, payload);
  }

  updateFollowUp(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatedietfollowupplan`;
    return this.http.post<any>(endPoint, payload);
  }

  updatePatientAttachment(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatedietpatientattachment`;
    return this.http.post<any>(endPoint, payload);
  }

  updatePatientResource(payload: any) {
    const endPoint =
      this.apiUrl + `dietitian/updatepatientattachmentsharesource`;
    return this.http.post<any>(endPoint, payload);
  }

  updatePatientFeedback(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatepatientfeedback`;
    return this.http.post<any>(endPoint, payload);
  }

  updateConsentRemark(payload: any) {
    const endPoint = this.apiUrl + `dietitian/updatepatientconsentandremark`;
    return this.http.post<any>(endPoint, payload);
  }

  getFollowUpById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getdietfollowupplanbyid`;
    // const endPoint = this.apiUrl + `activity/getfollowupplanbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: id })
    );
  }

  getdietPlanById(id: any, patientId: any) {
    const endPoint = this.apiUrl + `dietitian/getdietplanitems`;
    // const endPoint = this.apiUrl + `activity/getfollowupplanbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, {
        dietConsultationId: id,
        patientId: patientId,
      })
    );
  }

  getFeedbackById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getpatientfeedbackbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: id })
    );
  }

  getPatientConsentById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getpatientconsentandremarkbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: id })
    );
  }

  getPatientAttachmentById(id: any) {
    const endPoint = this.apiUrl + `dietitian/getdietpatientattachmentbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { dietConsultationId: id })
    );
  }

  getPatientAttachedResourceById(id: any) {
    const endPoint =
      this.apiUrl + `dietitian/getpatientattachmentsharesourcebyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { attachmentId: id }));
  }

  // Rehab Functional Assessment
  createFunctionalAssesment(payload: any) {
    // const endPoint = this.apiUrl + `activity/createfunctassessment`;
    const endPoint = this.apiUrl + `physiotherapy/createphysiofuncassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  updateFunctionalAssesment(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/updatephysiofuncassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  getFunctionalAssesmentById(id: any) {
    const endPoint = this.apiUrl + `physiotherapy/getphysiofuncassessmentbyid`;
    // const endPoint = this.apiUrl + `activity/getfunctassessmentbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  // Rehab Subjective Assessment
  createSubjectiveAssesment(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/createphysiosubassessment`;
    return this.http.post<any>(endPoint, payload);
  }
  updateSubjectiveAssesment(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/updatephysiosubassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  getSubjectiveAssesmentById(id: any) {
    const endPoint = this.apiUrl + `physiotherapy/getphysiosubassessmentbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { physioConsultationId: id })
    );
  }

  // Rehab Ojective Assessment
  createOjectiveAssesment(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/createphysioobjassessment`;
    return this.http.post<any>(endPoint, payload);
  }
  updateOjectiveAssesment(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/updatephysioobjassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  getOjectiveAssesmentById(id: any) {
    const endPoint = this.apiUrl + `physiotherapy/getphysioobjassessmentbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { physioConsultationId: id })
    );
  }

  createMuscle(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/createmulphysioobjassmuscletest`;
    return this.http.post<any>(endPoint, payload);
  }
  updateMuscle(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/updatephysioobjassmuscletest`;
    return this.http.post<any>(endPoint, payload);
  }

  getMuscleById(id: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/getphysioobjassmuscletestbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { objAssessmentId: id })
    );
  }

  createOutcomeMeasure(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/createmulphysioobjassoutcomemeasure`;
    return this.http.post<any>(endPoint, payload);
  }
  updateOutcomeMeasure(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/updatemulphysioobjassoutcomemeasure`;
    return this.http.post<any>(endPoint, payload);
  }

  getOutcomeMeasureById(id: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/getphysioobjassoutcomemeasurebyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { objAssessmentId: id })
    );
  }

  createROMs(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/createmulphysioobjassromass`;
    return this.http.post<any>(endPoint, payload);
  }
  updateROMs(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/updatemulphysioobjassromass`;
    return this.http.post<any>(endPoint, payload);
  }

  getROMsById(id: any) {
    const endPoint = this.apiUrl + `physiotherapy/getphysioobjassromassbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { objAssessmentId: id })
    );
  }

  createSpecialTests(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/createmulphysioobjassspecialtest`;
    return this.http.post<any>(endPoint, payload);
  }
  updateSpecialTests(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/updatemulphysioobjassspecialtest`;
    return this.http.post<any>(endPoint, payload);
  }

  getSpecialTestsById(id: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/getphysioobjassspecialtestbyid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { objAssessmentId: id })
    );
  }

  // Rehab CarePlan Assessment
  createCarePlanAssesment(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/createphysiocareplan`;
    return this.http.post<any>(endPoint, payload);
  }

  updateCarePlanAssesment(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/updatephysiocareplan`;
    return this.http.post<any>(endPoint, payload);
  }

  getCarePlanAssesmentById(id: any) {
    const endPoint = this.apiUrl + `physiotherapy/getphysiocareplanbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  // Rehab Excercise
  createExercise(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/createmulphysiocpexercise`;
    return this.http.post<any>(endPoint, payload);
  }

  updateExercise(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/updatemulphysiocpexercise`;
    return this.http.post<any>(endPoint, payload);
  }

  getExerciseById(id: any) {
    const endPoint = this.apiUrl + `physiotherapy/getphysiocpexercisebyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { carePlanId: id }));
  }

  createManualTherapy(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/createmulphysiocpmanualtherapies`;
    return this.http.post<any>(endPoint, payload);
  }

  updateManualTherapy(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/updatemulphysiocpmanualtherapies`;
    return this.http.post<any>(endPoint, payload);
  }

  getManualTherapyById(id: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/getphysiocpmanualtherapiesbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { carePlanId: id }));
  }

  createIntervention(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/createmulphysiocpintervention`;
    return this.http.post<any>(endPoint, payload);
  }

  updateIntervention(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/updatemulphysiocpintervention`;
    return this.http.post<any>(endPoint, payload);
  }

  getInterventionById(id: any) {
    const endPoint = this.apiUrl + `physiotherapy/getphysiocpinterventionbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { carePlanId: id }));
  }

  createPrecautions(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/createmulphysiocpprecacontras`;
    return this.http.post<any>(endPoint, payload);
  }

  updatePrecautions(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/updatemulphysiocpprecacontras`;
    return this.http.post<any>(endPoint, payload);
  }

  getPrecautionsById(id: any) {
    const endPoint = this.apiUrl + `physiotherapy/getphysiocpprecaucontrasbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { carePlanId: id }));
  }

  // Rehab Session Summary
  createSessionSummary(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/createphysiosessionsummary`;
    return this.http.post<any>(endPoint, payload);
  }

  updateSessionSummary(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/updatephysiosessionsummary`;
    return this.http.post<any>(endPoint, payload);
  }

  getSessionSummaryById(id: any) {
    const endPoint = this.apiUrl + `physiotherapy/getphysiosessionsummarybyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  createUpdateActivities(payload: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/upsertphysiosummaryactivities`;
    return this.http.post<any>(endPoint, payload);
  }

  getActivitiesById(id: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/getphysiosummaryactivitiesbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { sessionId: id }));
  }

  getElbaActivitiesById(id: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/getphysiorehablogactivitiesbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { logEntryId: id }));
  }

  createUpdateElbaActivities(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/upsertrehablogactivities`;
    return this.http.post<any>(endPoint, payload);
  }

  // Rehab Session Summary
  createElba(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/createphysiorehablogentry`;
    return this.http.post<any>(endPoint, payload);
  }

  updateElba(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/updatephysiorehablogentry`;
    return this.http.post<any>(endPoint, payload);
  }

  getElbaById(id: any) {
    const endPoint = this.apiUrl + `physiotherapy/getphysiorehablogentrybyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  // Rehab ROM
  createROM(payload: any) {
    const endPoint = this.apiUrl + `activity/createromstrength`;
    return this.http.post<any>(endPoint, payload);
  }

  updateROM(payload: any) {
    const endPoint = this.apiUrl + `activity/updateromstrength`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getROMById(id: any) {
    const endPoint = this.apiUrl + `activity/getromstrengthbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  // Rehab Neuro Assessment
  createNeuroAssesment(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/createphysioneuroassessment`;
    // const endPoint = this.apiUrl + `activity/createneuroassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  updateNeuroAssesment(payload: any) {
    const endPoint = this.apiUrl + `physiotherapy/updatephysioneuroassessment`;
    return this.http.post<any>(endPoint, payload);
  }

  getNeuroAssesmentById(id: any) {
    const endPoint =
      this.apiUrl + `physiotherapy/getphysioneurocassessmentbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  // Rehab Daily Activity
  createDailyActivity(payload: any) {
    const endPoint = this.apiUrl + `activity/createrehabadl`;
    return this.http.post<any>(endPoint, payload);
  }

  updateDailyActivity(payload: any) {
    const endPoint = this.apiUrl + `activity/updaterehabadl`;
    return this.http.post<any>(endPoint, payload);
  }

  getDailyActivityById(id: any) {
    const endPoint = this.apiUrl + `activity/getrehabadlbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  // Rehab Goals Note
  createRehabGoals(payload: any) {
    const endPoint = this.apiUrl + `activity/createrehabgoals`;
    return this.http.post<any>(endPoint, payload);
  }

  updateRehabGoals(payload: any) {
    const endPoint = this.apiUrl + `activity/updaterehabgoals`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getRehabGoalsById(id: any) {
    const endPoint = this.apiUrl + `activity/getrehabgoalsbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  // Rehab Physiotherapy Plan
  createPhysiotherapyPlan(payload: any) {
    const endPoint = this.apiUrl + `activity/createphysioplan`;
    return this.http.post<any>(endPoint, payload);
  }

  updatePhysiotherapyPlan(payload: any) {
    const endPoint = this.apiUrl + `activity/updatephysioplan`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getPhysiotherapyPlanById(id: any) {
    const endPoint = this.apiUrl + `activity/getphysioplanbyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }

  // Rehab Progress Note
  createProgressNote(payload: any) {
    const endPoint = this.apiUrl + `activity/createprogressnote`;
    return this.http.post<any>(endPoint, payload);
  }

  updateProgressNote(payload: any) {
    const endPoint = this.apiUrl + `activity/updateprogressnote`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getProgressNoteById(id: any) {
    const endPoint = this.apiUrl + `activity/getprogressnotebyid`;
    return firstValueFrom(this.http.post<any>(endPoint, { id }));
  }
}
