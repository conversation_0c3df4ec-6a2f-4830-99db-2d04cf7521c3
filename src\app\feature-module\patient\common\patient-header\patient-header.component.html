<!-- patient-header.component.html -->
<header class="modern-header sticky-top">
  <nav class="navbar navbar-expand-lg py-2 py-md-0 pt-md-1">
    <div class="container-fluid">
      <div class="d-flex align-items-center">
        <!-- Mobile Toggle Button (only visible on mobile) -->
        <button
          *ngIf="isMobile"
          class="mobile-toggle d-md-none me-3"
          type="button"
          (click)="toggleSidebar()"
          aria-label="Toggle navigation"
        >
          <i class="fas fa-bars"></i>
        </button>

        <!-- Brand Logo -->
        <a class="navbar-brand" routerLink="/patient/dashboard">
          <img
            src="/assets/images/logo/logo-title.png"
            alt="Logo"
            class="logo-img"
          />
          <!-- <span class="brand-text d-none d-sm-inline">HealthCare</span> -->
        </a>
      </div>

      <!-- Desktop Navigation Items -->
      <div
        class="navbar-nav ms-auto d-none d-md-flex align-items-center flex-row"
      >
        <!-- Theme Toggle -->
        <div class="nav-item me-3">
          <button
            class="btn btn-link nav-link border-0 p-2"
            (click)="toggleTheme()"
            [title]="isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode'"
          >
            <i
              [class]="
                isDark ? 'fas fa-sun text-warning' : 'fas fa-moon text-primary'
              "
            ></i>
          </button>
        </div>

        <!-- Notifications -->
        <!-- <div class="nav-item dropdown me-3">
          <button
            class="btn btn-link nav-link border-0 p-2 position-relative"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            <i class="fas fa-bell text-primary"></i>
            <span
              *ngIf="getUnreadNotificationsCount() > 0"
              class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
            >
              {{ getUnreadNotificationsCount() }}
            </span>
          </button>

          <div
            class="dropdown-menu dropdown-menu-end notifications-dropdown shadow-lg"
          >
            <div
              class="dropdown-header d-flex justify-content-between align-items-center"
            >
              <h6 class="mb-0">Notifications</h6>
              <small class="text-muted"
                >{{ getUnreadNotificationsCount() }} new</small
              >
            </div>
            <div class="dropdown-divider"></div>

            <div
              class="notifications-list"
              style="max-height: 300px; overflow-y: auto"
            >
              <a
                *ngFor="let notification of notifications"
                href="javascript:void(0);"
                class="dropdown-item notification-item"
                [class.unread]="!notification.isRead"
                (click)="markNotificationAsRead(notification.id)"
              >
                <div class="d-flex align-items-start">
                  <img
                    [src]="notification.avatar"
                    [alt]="notification.name"
                    class="notification-avatar me-3"
                  />
                  <div class="notification-content flex-grow-1">
                    <div
                      class="d-flex justify-content-between align-items-start"
                    >
                      <h6 class="notification-name mb-1">
                        {{ notification.name }}
                      </h6>
                      <small class="text-muted">{{ notification.time }}</small>
                    </div>
                    <p class="notification-message mb-0">
                      {{ notification.message }}
                      <span *ngIf="notification.doctor" class="fw-bold">{{
                        notification.doctor
                      }}</span>
                    </p>
                  </div>
                </div>
              </a>
            </div>

            <div class="dropdown-divider"></div>
            <a
              class="dropdown-item text-center py-2"
              href="javascript:void(0);"
            >
              <small>View All Notifications</small>
            </a>
          </div>
        </div> -->

        <!-- User Profile Dropdown -->
        <div class="nav-item dropdown" *ngIf="user">
          <button
            class="btn btn-link nav-link border-0 p-0"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            <div class="d-flex align-items-center">
              <img
                [src]="user.photoLinkUrl || 'assets/images/avatar-image.webp'"
                [alt]="user.firstName || 'User'"
                class="user-avatar me-2"
              />
              <div class="user-info d-none d-lg-block text-start">
                <div class="user-name">
                  {{ user?.firstName || "Hendrita" }}
                  {{ user?.lastName || "Hayes" }}
                </div>
                <small class="user-role text-white">
                  {{ userData?.roleDescription || "Patient" }}
                </small>
              </div>
              <i class="fas fa-chevron-down ms-2 d-none d-lg-inline"></i>
            </div>
          </button>

          <div class="dropdown-menu dropdown-menu-end user-dropdown shadow-lg">
            <div class="dropdown-header">
              <div class="d-flex align-items-center">
                <img
                  [src]="user.photoLinkUrl || 'assets/images/avatar-image.webp'"
                  [alt]="user.firstName || 'User'"
                  class="user-avatar-lg me-3"
                />
                <div>
                  <h6 class="mb-0">
                    {{ user?.firstName || "Hendrita" }}
                    {{ user?.lastName || "Hayes" }}
                  </h6>
                  <small class="text-white">{{
                    userData?.roleDescription || "Patient"
                  }}</small>
                </div>
              </div>
            </div>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" routerLink="/patient/profile">
              <i class="fas fa-user me-2"></i>My Profile
            </a>
            <!-- <a class="dropdown-item" routerLink="/patient/settings">
              <i class="fas fa-cog me-2"></i>Settings
            </a> -->
            <div class="dropdown-divider"></div>
            <a class="dropdown-item text-danger" (click)="logout()">
              <i class="fas fa-sign-out-alt me-2"></i>Logout
            </a>
          </div>
        </div>
      </div>
    </div>
  </nav>
</header>

<!-- Mobile Sidebar Overlay -->
<div
  *ngIf="isMobile && isSidebarOpen"
  class="mobile-sidebar-overlay"
  (click)="closeSidebar()"
></div>

<!-- Mobile Sidebar -->
<div *ngIf="isMobile" class="mobile-sidebar" [class.active]="isSidebarOpen">
  <!-- Sidebar Header -->
  <div class="sidebar-header">
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <img
          src="/assets/images/logo/logo-title.png"
          alt="Logo"
          class="sidebar-logo me-2"
        />
        <!-- <h5 class="mb-0">HealthCare</h5> -->
      </div>
      <button class="btn btn-link p-0" (click)="closeSidebar()">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>

  <!-- User Info -->
  <div class="sidebar-user-info" *ngIf="user">
    <div class="d-flex align-items-center p-3">
      <img
        [src]="user.photoLinkUrl || 'assets/images/avatar-image.webp'"
        [alt]="user.firstName || 'User'"
        class="user-avatar-sidebar me-3"
      />
      <div>
        <h6 class="mb-0">
          {{ user?.firstName || "Hendrita" }} {{ user?.lastName || "Hayes" }}
        </h6>
        <small class="text-muted">{{
          userData?.roleDescription || "Patient"
        }}</small>
      </div>
    </div>
  </div>

  <!-- Sidebar Navigation -->
  <div class="sidebar-nav">
    <ul class="nav flex-column">
      <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/dashboard"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-th-large me-3"></i>
          <span>Dashboard</span>
        </a>
      </li>
      <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/clinical-visits"
          (click)="closeSideMenu()"
        >
          <i class="fa fa-tents me-3"></i>
          <span>Medical Camp</span>
          <!-- <span>Clinical Visits</span> -->
        </a>
      </li>
      <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/lab-tests"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-flask me-3"></i>
          <span>Lab Tests</span>
        </a>
      </li>
      <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/vitals"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-heartbeat me-3"></i>
          <span>Vitals</span>
        </a>
      </li>
      <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/life-style"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-dumbbell me-3"></i>
          <span>LifeStyle</span>
        </a>
      </li>
      <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/insurance"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-shield-alt me-3"></i>
          <span>Insurance</span>
        </a>
      </li>
      <li class="nav-item" routerLinkActive="active">
        <a class="nav-link" (click)="goToCarePlansPage()">
          <i class="fas fa-notes-medical me-3"></i>
          <span>Care Plans</span>
        </a>
      </li>

      <!-- <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/appointments"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-calendar-alt me-3"></i>
          <span>Appointments</span>
        </a>
      </li> -->

      <!-- <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          (click)="closeSideMenu()"
          routerLink="/patient/follow-up/facility/{{
            userData.facilityId
          }}/doctors"
        >
          <i class="fas fa-calendar-check me-3"></i>
          <span>Follow Ups</span>
        </a>
      </li> -->
      <!-- <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/family"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-users me-3"></i>
          <span>Family Members</span>
        </a>
      </li> -->
      <!-- <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/medical-history"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-file-medical-alt me-3"></i>
          <span>Medical History</span>
        </a>
      </li> -->
      <!-- <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/chat"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-envelope me-3"></i>
          <span>Messages</span>
        </a>
      </li> -->

      <!-- <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/documents"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-file-alt me-3"></i>
          <span>Documents</span>
        </a>
      </li> -->
      <!-- <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/reviews/list"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-star me-3"></i>
          <span>Reviews</span>
        </a>
      </li> -->
      <li class="nav-item" routerLinkActive="active">
        <a
          class="nav-link"
          routerLink="/patient/profile"
          (click)="closeSideMenu()"
        >
          <i class="fas fa-user me-3"></i>
          <span>My Profile</span>
        </a>
      </li>
      <li class="nav-item border-top mt-3 pt-3">
        <a class="nav-link text-danger" (click)="logout()">
          <i class="fas fa-sign-out-alt me-3"></i>
          <span>Logout</span>
        </a>
      </li>
    </ul>
  </div>
</div>
