import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HealthAdvisorComponent } from './health-advisor.component';
import { HaDashboardComponent } from './ha-dashboard/ha-dashboard.component';
import { HaProfileComponent } from './ha-profile/ha-profile.component';
import { HaAppointmentsComponent } from './ha-appointments/ha-appointments.component';
import { HaAllPatientsComponent } from './ha-all-patients/ha-all-patients.component';
import { HaMyPatientsComponent } from './ha-my-patients/ha-my-patients.component';
import { authGuard } from 'src/app/shared/auth/auth.guard';
import { HaAppointmentHistoryComponent } from './ha-appointment-history/ha-appointment-history.component';
import { HaBookAppointmentComponent } from './ha-book-appointment/ha-book-appointment.component';
import { HaLabTestSelectPatientComponent } from './ha-lab-tests/ha-lab-test-select-patient/ha-lab-test-select-patient.component';
import { HaLabTestPatientPackagesListComponent } from './ha-lab-tests/ha-lab-test-patient-packages-list/ha-lab-test-patient-packages-list.component';
import { HaLabTestSelectPackageComponent } from './ha-lab-tests/ha-lab-test-select-package/ha-lab-test-select-package.component';
import { HaLabTestPackageViewComponent } from './ha-lab-tests/ha-lab-test-package-view/ha-lab-test-package-view.component';
import { HaLabTestSelectActivityComponent } from './ha-lab-tests/ha-lab-test-select-activity/ha-lab-test-select-activity.component';
import { HaLabTestCartComponent } from './ha-lab-tests/ha-lab-test-cart/ha-lab-test-cart.component';
import { CollabrationTypeListComponent } from './collabration/collabration-type-list/collabration-type-list.component';
import { AddFacilityCollabrationComponent } from './collabration/add-facility-collabration/add-facility-collabration.component';
import { AddUpdateCollabrationTypeComponent } from './collabration/add-update-collabration-type/add-update-collabration-type.component';
import { CollabUsersListComponent } from './collabration/collab-users-list/collab-users-list.component';
import { NetworkFacilityListComponent } from './collabration/network-facility-list/network-facility-list.component';
import { ViewCollabUsersComponent } from './collabration/view-collab-users/view-collab-users.component';
import { CollabrationComponent } from './collabration/collabration/collabration.component';
import { PartnerFacilityListComponent } from './collabration/partner-facility-list/partner-facility-list.component';
import { HaActivityComponent } from './activity/ha-activity/ha-activity.component';
import { HaActivityAddActivityCollaborationComponent } from './activity/ha-activity-add-activity-collaboration/ha-activity-add-activity-collaboration.component';
import { MedicalCampComponent } from './medical-camp/medical-camp.component';
import { SelectMedicalCampPatientsComponent } from './medical-camp/select-medical-camp-patients/select-medical-camp-patients.component';
import { PatientVisitListComponent } from './medical-camp/patient-visit-list/patient-visit-list.component';
import { PatientVisitDetailsComponent } from './medical-camp/patient-visit-details/patient-visit-details.component';
import { ViewPatientVisitDetailsComponent } from './medical-camp/view-patient-visit-details/view-patient-visit-details.component';

const routes: Routes = [
  {
    path: '',
    component: HealthAdvisorComponent,
    children: [
      {
        path: 'dashboard',
        component: HaDashboardComponent,
      },
      {
        path: 'profile',
        component: HaProfileComponent,
      },
      {
        path: 'my-patients',
        component: HaMyPatientsComponent,
      },
      {
        path: 'all-patients',
        component: HaAllPatientsComponent,
      },
      {
        path: 'appointments',
        component: HaAppointmentsComponent,
      },
      {
        path: 'appointments/book',
        component: HaBookAppointmentComponent,
      },
      {
        path: 'appointments/history',
        component: HaAppointmentHistoryComponent,
      },
      {
        path: 'medical-camp',
        component: MedicalCampComponent,
      },
      {
        path: 'medical-camp/:activityId/select-patient',
        component: SelectMedicalCampPatientsComponent,
      },
      {
        path: 'medical-camp/:activityId/select-patient/:patientId/visit-list',
        component: PatientVisitListComponent,
      },
      {
        path: 'medical-camp/:activityId/select-patient/:patientId/create-visit-details/:visitId',
        component: PatientVisitDetailsComponent,
      },
      {
        path: 'medical-camp/:activityId/select-patient/:patientId/view-visit-details/:visitId',
        component: ViewPatientVisitDetailsComponent,
      },
      {
        path: 'lab-tests/select-activity',
        component: HaLabTestSelectActivityComponent,
      },
      {
        path: 'lab-tests/:activityId/select-patient',
        component: HaLabTestSelectPatientComponent,
      },
      {
        path: 'lab-tests/:activityId/:patientId',
        component: HaLabTestPatientPackagesListComponent,
      },

      {
        path: 'lab-tests/:activityId/:patientId/packages',
        component: HaLabTestSelectPackageComponent,
      },
      {
        path: 'lab-tests/:activityId/:patientId/packages/:packageId',
        component: HaLabTestPackageViewComponent,
      },

      {
        path: 'lab-tests/:activityId/:patientId/cart/:cartId',
        component: HaLabTestCartComponent,
      },
      {
        path: 'availability',
        loadComponent: () =>
          import('./availability/availability/availability.component').then(
            (m) => m.AvailabilityComponent
          ),
        canActivate: [authGuard],
      },
      {
        path: 'lab-tests/invoice/:activityId/:patientId/cart/:cartId/:purchaseId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/lt-invoice/lt-invoice.component'
          ).then((m) => m.LtInvoiceComponent),
      },

      {
        path: 'view-lab-test-reports/:activityId/:patientId/:packageId/:ppId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/view-lab-test-reports/view-lab-test-reports.component'
          ).then((m) => m.ViewLabTestReportsComponent),
      },

      {
        path: 'view-lab-test-invoice/:activityId/:patientId/:packageId/:ppId/:purchaseId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/view-lab-test-invoice/view-lab-test-invoice.component'
          ).then((m) => m.ViewLabTestInvoiceComponent),
      },

      {
        path: 'collabration',
        component: CollabrationComponent,
        canActivate: [authGuard],
      },
      {
        path: 'partner-facility-list/:facilityId',
        component: PartnerFacilityListComponent,
        canActivate: [authGuard],
      },
      {
        path: 'collabration/collabration-type-list',
        component: CollabrationTypeListComponent,
        canActivate: [authGuard],
      },
      {
        path: 'collabration/collabration-type-list/add-collabration-type',
        component: AddUpdateCollabrationTypeComponent,
        canActivate: [authGuard],
      },
      {
        path: 'collabration/collabration-type-list/update-collabration-type/:collabTypeId',
        component: AddUpdateCollabrationTypeComponent,
        canActivate: [authGuard],
      },
      {
        path: 'collabration-network/:facilityId',
        component: NetworkFacilityListComponent,
        canActivate: [authGuard],
      },
      {
        path: 'collabration-network/add-facility-collaboration/:hostFacility/:partnerFaciity',
        component: AddFacilityCollabrationComponent,
        canActivate: [authGuard],
      },
      {
        path: 'collabration-network/collaboration-users/:collabId/:hostFacility/:partnerFacilityId',
        component: CollabUsersListComponent,
        canActivate: [authGuard],
      },
      {
        path: 'collabration-network/view-collaboration-users/:collabId',
        component: ViewCollabUsersComponent,
        canActivate: [authGuard],
      },
      {
        path: 'pdf/view/:type/:src/:doc',
        loadComponent: () =>
          import('../../shared/view-attachment/view-attachment.component').then(
            (m) => m.ViewAttachmentComponent
          ),
      },

      {
        path: 'follow-up/facility/:facilityId/doctors',
        loadComponent: () =>
          import(
            '../../shared/components/pages/follow-up-doctors-list/follow-up-doctors-list.component'
          ).then((m) => m.FollowUpDoctorsListComponent),
        canActivate: [authGuard],
      },

      {
        path: 'follow-up/facility/:facilityId/doctors/:doctorId/patients',
        loadComponent: () =>
          import(
            '../../shared/components/pages/follow-up-patients-list/follow-up-patients-list.component'
          ).then((m) => m.FollowUpPatientsListComponent),
      },

      {
        path: 'follow-up/facility/:facilityId/doctors/:doctorId/patients/:patientId/prescriptions',
        loadComponent: () =>
          import(
            '../../shared/components/pages/follow-up-prescriptions-list/follow-up-prescriptions-list.component'
          ).then((m) => m.FollowUpPrescriptionsListComponent),
      },
      {
        path: 'follow-up/facility/:facilityId/doctors/:doctorId/patients/:patientId/prescriptions/:visitId/labtestorders',
        loadComponent: () =>
          import(
            '../../shared/components/pages/follow-up-lab-test-orders/follow-up-lab-test-orders.component'
          ).then((m) => m.FollowUpLabTestOrdersComponent),
      },
      {
        path: 'follow-up/facility/:facilityId/doctors/:doctorId/patients/:patientId/prescriptions/:prescriptionId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/follow-up-prescriptions-view/follow-up-prescriptions-view.component'
          ).then((m) => m.FollowUpPrescriptionsViewComponent),
      },

      {
        path: 'prescriptions/add-lab-test/:visitId/:patientId/:doctorId',
        loadComponent: () =>
          import(
            '../../shared/components/forms/lab-test-add-form/lab-test-add-form.component'
          ).then((m) => m.LabTestAddFormComponent),
      },
      {
        path: 'prescriptions/add-medicine/:visitId/:patientId/:doctorId/:prescriptionId',
        loadComponent: () =>
          import(
            '../../shared/components/forms/prescription-medicine-add-form/prescription-medicine-add-form.component'
          ).then((m) => m.PrescriptionMedicineAddFormComponent),
      },
      {
        path: 'patient-profile/:patientId',
        loadComponent: () =>
          import(
            '../../shared/profiles/patient-profile/patient-profile.component'
          ).then((m) => m.PatientProfileComponent),
      },
      {
        path: 'doctor-profile/:docId',
        loadComponent: () =>
          import(
            '../../shared/profiles/doctor-profile/doctor-profile.component'
          ).then((m) => m.DoctorProfileComponent),
      },

      {
        path: 'activity',
        component: HaActivityComponent,
        canActivate: [authGuard],
      },
      {
        path: 'activity/:activityId/collaboration',
        component: HaActivityAddActivityCollaborationComponent,
      },
      {
        path: 'activity/:activityId/:facilityId/patients/create',
        loadComponent: () =>
          import(
            '../../shared/components/pages/activity/create-patient-page/create-patient-page.component'
          ).then((m) => m.CreatePatientPageComponent),
      },
      {
        path: 'activity/:activityId/:facilityId/patients/list-tab',
        loadComponent: () =>
          import(
            '../../shared/components/pages/activity/patients-tab-page/patients-tab-page.component'
          ).then((m) => m.PatientsTabPageComponent),
        children: [
          {
            path: '',
            redirectTo: 'list',
            pathMatch: 'full',
          },

          {
            path: 'list',
            loadComponent: () =>
              import(
                '../../shared/components/pages/activity/all-patients-list-page/all-patients-list-page.component'
              ).then((m) => m.AllPatientsListPageComponent),
          },
          {
            path: 'modify',
            loadComponent: () =>
              import(
                '../../shared/components/pages/activity/modify-patients-list-page/modify-patients-list-page.component'
              ).then((m) => m.ModifyPatientsListPageComponent),
          },
        ],
      },

      {
        path: 'lt/patients/:activityId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/lt-select-patient/lt-select-patient.component'
          ).then((m) => m.LtSelectPatientComponent),
      },
      {
        path: 'lt/packages/:activityId/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/lt-patient-packages/lt-patient-packages.component'
          ).then((m) => m.LtPatientPackagesComponent),
      },
      {
        path: 'lt/package-view/:activityId/:patientId/:packageId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/view-package-details/view-package-details.component'
          ).then((m) => m.ViewPackageDetailsComponent),
      },
      {
        path: 'lt/reports/:activityId/:patientId/:packageId/:ppId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/view-lab-test-reports/view-lab-test-reports.component'
          ).then((m) => m.ViewLabTestReportsComponent),
      },
      {
        path: 'lt/invoice/:activityId/:patientId/:packageId/:ppId/:purchaseId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/view-lab-test-invoice/view-lab-test-invoice.component'
          ).then((m) => m.ViewLabTestInvoiceComponent),
      },
      {
        path: 'pdf/view/:type/:src/:doc',
        loadComponent: () =>
          import('../../shared/view-attachment/view-attachment.component').then(
            (m) => m.ViewAttachmentComponent
          ),
      },
      {
        path: 'care-plans',
        loadComponent: () =>
          import(
            '../../shared/care-plans/care-plans-list/care-plans-list.component'
          ).then((m) => m.CarePlansListComponent),
      },
      {
        path: 'care-plans/add-edit-care-plan',
        loadComponent: () =>
          import(
            '../../shared/care-plans/add-edit-care-plan/add-edit-care-plan.component'
          ).then((m) => m.AddEditCarePlanComponent),
      },
      {
        path: 'care-plans/:carePlanId/care-plan-activity',
        loadComponent: () =>
          import(
            '../../shared/care-plans/care-plan-activity-list/care-plan-activity-list.component'
          ).then((m) => m.CarePlanActivityListComponent),
      },
      {
        path: 'care-plans/:carePlanId/care-plan-activity/add-edit-Activity',
        loadComponent: () =>
          import(
            '../../shared/care-plans/add-edit-care-plan-activity/add-edit-care-plan-activity.component'
          ).then((m) => m.AddEditCarePlanActivityComponent),
      },

      {
        path: 'cp',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-select-activity/cp-select-activity.component'
          ).then((m) => m.CpSelectActivityComponent),
      },
      {
        path: 'cp/:activityId/patients',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-select-patient/cp-select-patient.component'
          ).then((m) => m.CpSelectPatientComponent),
      },
      {
        path: 'cp/:patientId/:activityId/purchased-care-plans',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-patient-purchased-care-plans/cp-patient-purchased-care-plans.component'
          ).then((m) => m.CpPatientPurchasedCarePlansComponent),
      },
      {
        path: 'cp/:patientId/:activityId/select-care-plan',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-select-plan/cp-select-plan.component'
          ).then((m) => m.CpSelectPlanComponent),
      },
      {
        path: 'cp/:patientId/:activityId/:planId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-plan-deatils/cp-plan-deatils.component'
          ).then((m) => m.CpPlanDeatilsComponent),
      },
      {
        path: 'cp/view-care-plan/:patientId/:activityId/:planId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-view-plan-deatils/cp-view-plan-deatils.component'
          ).then((m) => m.CpViewPlanDeatilsComponent),
      },
      {
        path: 'cp/cart/:patientId/:activityId/:cartId/checkout',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-cart/cp-cart.component'
          ).then((m) => m.CpCartComponent),
      },
      {
        path: 'cp/invoice/:patientId/:activityId/:purchaseId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-invoice/cp-invoice.component'
          ).then((m) => m.CpInvoiceComponent),
      },
      {
        path: 'cp/activity-month-view/:patientId/:activityId/:planId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-monthly-view/cp-monthly-view.component'
          ).then((m) => m.CpMonthlyViewComponent),
      },
      {
        path: 'cp/activity-month-activities/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-monthly-activity-view/cp-monthly-activity-view.component'
          ).then((m) => m.CpMonthlyActivityViewComponent),
      },
      {
        path: 'cp/activity-month-activities/specialist-consultation/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-specialist-consultation-detail-page/cp-specialist-consultation-detail-page.component'
          ).then((m) => m.CpSpecialistConsultationDetailPageComponent),
      },
      {
        path: 'cp/activity-month-activities/general-practitioner/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-general-practice-detail-page/cp-general-practice-detail-page.component'
          ).then((m) => m.CpGeneralPracticeDetailPageComponent),
      },
      {
        path: 'cp/activity-month-activities/nurse-activity/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-nurse-activity-detail-page/cp-nurse-activity-detail-page.component'
          ).then((m) => m.CpNurseActivityDetailPageComponent),
      },
      {
        path: 'cp/activity-month-activities/health-advisor-role/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-health-advisor-role-detail-page/cp-health-advisor-role-detail-page.component'
          ).then((m) => m.CpHealthAdvisorRoleDetailPageComponent),
      },
      {
        path: 'cp/activity-month-activities/recommended-tests/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-recommended-tests-detail-page/cp-recommended-tests-detail-page.component'
          ).then((m) => m.CpRecommendedTestsDetailPageComponent),
      },

      {
        path: 'cp/dietician-details/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/dietician/cp-dietician-details-view/cp-dietician-details-view.component'
          ).then((m) => m.CpDieticianDetailsViewComponent),
      },
      {
        path: 'cp/dietician-assesment-form/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/dietician/cp-dietician-assesment-form/cp-dietician-assesment-form.component'
          ).then((m) => m.CpDieticianAssesmentFormComponent),
      },
      {
        path: 'cp/rehab-details/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/rehab/cp-rehab-details-view/cp-rehab-details-view.component'
          ).then((m) => m.CpRehabDetailsViewComponent),
      },
      {
        path: 'cp/rehab-assesment-form/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/rehab/cp-rehab-assesment-form/cp-rehab-assesment-form.component'
          ).then((m) => m.CpRehabAssesmentFormComponent),
      },
      {
        path: 'cp/activity-month-activities/ba/select-doctor/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-select-doctor-for-appointment/cp-select-doctor-for-appointment.component'
          ).then((m) => m.CpSelectDoctorForAppointmentComponent),
      },
      {
        path: 'cp/activity-month-activities/ba/select-doc-slot/:patientId/:activityId/:planId/:monthNumber/:doctorId/:facilityId/:deptId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-choose-slot-and-book-appointment/cp-choose-slot-and-book-appointment.component'
          ).then((m) => m.CpChooseSlotAndBookAppointmentComponent),
      },

      {
        path: 'patient-requests',
        loadComponent: () =>
          import(
            '../../shared/components/pages/patient-requests/ha-pr-sel-patient/ha-pr-sel-patient.component'
          ).then((m) => m.HaPrSelPatientComponent),
      },
      {
        path: 'patient-requests/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/patient-requests/ha-patient-requests/ha-patient-requests.component'
          ).then((m) => m.HaPatientRequestsComponent),
      },
      {
        path: 'vc',
        loadComponent: () =>
          import(
            '../../shared/components/pages/meet/video-meet/video-meet.component'
          ).then((m) => m.VideoMeetComponent),
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HealthAdvisorRoutingModule {}
