import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HealthAdvisorComponent } from './health-advisor.component';
import { HaDashboardComponent } from './ha-dashboard/ha-dashboard.component';
import { HaProfileComponent } from './ha-profile/ha-profile.component';
import { HaAppointmentsComponent } from './ha-appointments/ha-appointments.component';
import { HaAllPatientsComponent } from './ha-all-patients/ha-all-patients.component';
import { HaMyPatientsComponent } from './ha-my-patients/ha-my-patients.component';
import { authGuard } from 'src/app/shared/auth/auth.guard';
import { HaAppointmentHistoryComponent } from './ha-appointment-history/ha-appointment-history.component';
import { HaBookAppointmentComponent } from './ha-book-appointment/ha-book-appointment.component';
import { HaLabTestSelectPatientComponent } from './ha-lab-tests/ha-lab-test-select-patient/ha-lab-test-select-patient.component';
import { HaLabTestPatientPackagesListComponent } from './ha-lab-tests/ha-lab-test-patient-packages-list/ha-lab-test-patient-packages-list.component';
import { HaLabTestSelectPackageComponent } from './ha-lab-tests/ha-lab-test-select-package/ha-lab-test-select-package.component';
import { HaLabTestPackageViewComponent } from './ha-lab-tests/ha-lab-test-package-view/ha-lab-test-package-view.component';
import { HaLabTestSelectActivityComponent } from './ha-lab-tests/ha-lab-test-select-activity/ha-lab-test-select-activity.component';
import { HaLabTestCartComponent } from './ha-lab-tests/ha-lab-test-cart/ha-lab-test-cart.component';
import { CollabrationTypeListComponent } from './collabration/collabration-type-list/collabration-type-list.component';
import { AddFacilityCollabrationComponent } from './collabration/add-facility-collabration/add-facility-collabration.component';
import { AddUpdateCollabrationTypeComponent } from './collabration/add-update-collabration-type/add-update-collabration-type.component';
import { CollabUsersListComponent } from './collabration/collab-users-list/collab-users-list.component';
import { NetworkFacilityListComponent } from './collabration/network-facility-list/network-facility-list.component';
import { ViewCollabUsersComponent } from './collabration/view-collab-users/view-collab-users.component';
import { CollabrationComponent } from './collabration/collabration/collabration.component';
import { PartnerFacilityListComponent } from './collabration/partner-facility-list/partner-facility-list.component';
import { HaActivityComponent } from './activity/ha-activity/ha-activity.component';
import { HaActivityAddActivityCollaborationComponent } from './activity/ha-activity-add-activity-collaboration/ha-activity-add-activity-collaboration.component';
import { MedicalCampComponent } from './medical-camp/medical-camp.component';
import { SelectMedicalCampPatientsComponent } from './medical-camp/select-medical-camp-patients/select-medical-camp-patients.component';
import { PatientVisitListComponent } from './medical-camp/patient-visit-list/patient-visit-list.component';
import { PatientVisitDetailsComponent } from './medical-camp/patient-visit-details/patient-visit-details.component';
import { ViewPatientVisitDetailsComponent } from './medical-camp/view-patient-visit-details/view-patient-visit-details.component';
import { AmAppointmentsTableComponent } from '../admin-manager/am-appointments-table/am-appointments-table.component';
import { HaAppointmentsTableComponent } from './ha-appointments-table/ha-appointments-table.component';
import { SelectPhlebotomistComponent } from './../../shared/components/pages/lab-test-appointments/select-phlebotomist/select-phlebotomist.component';
import { SelectPhlebotomistSlotAndBookComponent } from 'src/app/shared/components/pages/lab-test-appointments/select-phlebotomist-slot-and-book/select-phlebotomist-slot-and-book.component';

const routes: Routes = [
  {
    path: '',
    component: HealthAdvisorComponent,
    children: [
      {
        path: 'dashboard',
        component: HaDashboardComponent,
        title: 'Health Advisor Dashboard',
      },
      {
        path: 'facility',
        loadChildren: () =>
          import('../facility/facility.module').then((m) => m.FacilityModule),
        canActivate: [authGuard],
        title: 'Health Advisor Facility',
      },
      {
        path: 'facility/:fid/users',
        loadChildren: () =>
          import('../users/users.module').then((m) => m.UsersModule),
        canActivate: [authGuard],
        title: 'Health Advisor Users',
      },
      {
        path: 'profile',
        component: HaProfileComponent,
        title: 'Health Advisor Profile',
      },
      {
        path: 'my-patients',
        component: HaMyPatientsComponent,
        title: 'Health Advisor My Patients',
      },
      {
        path: 'all-patients',
        component: HaAllPatientsComponent,
        title: 'Health Advisor All Patients',
      },
      {
        path: 'appointments',
        component: HaAppointmentsComponent,
        title: 'Health Advisor Appointments',
      },
      {
        path: 'all-appointments/:facilityId',
        component: HaAppointmentsTableComponent,
        title: 'Health Advisor All Appointments',
      },
      {
        path: 'appointments/book',
        component: HaBookAppointmentComponent,
        title: 'Health Advisor Book Appointment',
      },
      {
        path: 'appointments/history',
        component: HaAppointmentHistoryComponent,
        title: 'Health Advisor Appointment History',
      },
      {
        path: 'medical-camp',
        component: MedicalCampComponent,
        title: 'Health Advisor Medical Camp',
      },
      {
        path: 'medical-camp/:activityId/select-patient',
        component: SelectMedicalCampPatientsComponent,
        title: 'Health Advisor Select Medical Camp Patients',
      },
      {
        path: 'medical-camp/:activityId/select-patient/:patientId/visit-list',
        component: PatientVisitListComponent,
        title: 'Health Advisor Patient Visit List',
      },
      {
        path: 'medical-camp/:activityId/select-patient/:patientId/create-visit-details/:visitId',
        component: PatientVisitDetailsComponent,
        title: 'Health Advisor Patient Visit Details',
      },
      {
        path: 'medical-camp/:activityId/select-patient/:patientId/view-visit-details/:visitId',
        component: ViewPatientVisitDetailsComponent,
        title: 'Health Advisor View Patient Visit Details',
      },
      {
        path: 'lab-tests/select-activity',
        component: HaLabTestSelectActivityComponent,
        title: 'Health Advisor Lab Tests Select Activity',
      },
      {
        path: 'lab-tests/:activityId/select-patient',
        component: HaLabTestSelectPatientComponent,
        title: 'Health Advisor Lab Tests Select Patient',
      },
      {
        path: 'lab-tests/:activityId/:patientId',
        component: HaLabTestPatientPackagesListComponent,
        title: 'Health Advisor Lab Tests Patient Packages List',
      },

      {
        path: 'lab-tests/:activityId/:patientId/packages',
        component: HaLabTestSelectPackageComponent,
        title: 'Health Advisor Lab Tests Select Package',
      },
      {
        path: 'lab-tests/:activityId/:patientId/packages/:packageId',
        component: HaLabTestPackageViewComponent,
        title: 'Health Advisor Lab Tests Package View',
      },
      {
        path: 'lab-tests/:activityId/:patientId/cart/:cartId',
        component: HaLabTestCartComponent,
        title: 'Health Advisor Lab Tests Cart',
      },
      {
        path: 'lab-tests/book-phlebotomist/:activityId/:patientId/:packageId/:ppId',
        loadComponent: () =>
          import(
            './../../shared/components/pages/lab-test-appointments/select-phlebotomist/select-phlebotomist.component'
          ).then((m) => SelectPhlebotomistComponent),
        title: 'Health Advisor Lab Tests Book Phlebotomist',
      },
      {
        path: 'lab-tests/book-phlebotomist-slot/:activityId/:patientId/:packageId/:ppId/:doctorId/:facilityId/:deptId',
        loadComponent: () =>
          import(
            './../../shared/components/pages/lab-test-appointments/select-phlebotomist-slot-and-book/select-phlebotomist-slot-and-book.component'
          ).then((m) => SelectPhlebotomistSlotAndBookComponent),
        title: 'Health Advisor Lab Tests Book Phlebotomist Slot',
      },

      {
        path: 'availability',
        loadComponent: () =>
          import('./availability/availability/availability.component').then(
            (m) => m.AvailabilityComponent
          ),
        canActivate: [authGuard],
        title: 'Health Advisor Availability',
      },
      {
        path: 'lab-tests/invoice/:activityId/:patientId/cart/:cartId/:purchaseId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/lt-invoice/lt-invoice.component'
          ).then((m) => m.LtInvoiceComponent),
        title: 'Health Advisor Lab Tests Invoice',
      },

      {
        path: 'view-lab-test-reports/:activityId/:patientId/:packageId/:ppId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/view-lab-test-reports/view-lab-test-reports.component'
          ).then((m) => m.ViewLabTestReportsComponent),
        title: 'Health Advisor View Lab Test Reports',
      },

      {
        path: 'view-lab-test-invoice/:activityId/:patientId/:packageId/:ppId/:purchaseId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/view-lab-test-invoice/view-lab-test-invoice.component'
          ).then((m) => m.ViewLabTestInvoiceComponent),
        title: 'Health Advisor View Lab Test Invoice',
      },

      {
        path: 'collabration',
        component: CollabrationComponent,
        canActivate: [authGuard],
        title: 'Health Advisor Collabration',
      },
      {
        path: 'partner-facility-list/:facilityId',
        component: PartnerFacilityListComponent,
        canActivate: [authGuard],
        title: 'Health Advisor Partner Facility List',
      },
      {
        path: 'collabration/collabration-type-list',
        component: CollabrationTypeListComponent,
        canActivate: [authGuard],
        title: 'Health Advisor Collabration Type List',
      },
      {
        path: 'collabration/collabration-type-list/add-collabration-type',
        component: AddUpdateCollabrationTypeComponent,
        canActivate: [authGuard],
        title: 'Health Advisor Add Collabration Type',
      },
      {
        path: 'collabration/collabration-type-list/update-collabration-type/:collabTypeId',
        component: AddUpdateCollabrationTypeComponent,
        canActivate: [authGuard],
        title: 'Health Advisor Update Collabration Type',
      },
      {
        path: 'collabration-network/:facilityId',
        component: NetworkFacilityListComponent,
        canActivate: [authGuard],
        title: 'Health Advisor Collabration Network',
      },
      {
        path: 'collabration-network/add-facility-collaboration/:hostFacility/:partnerFaciity',
        component: AddFacilityCollabrationComponent,
        canActivate: [authGuard],
        title: 'Health Advisor Add Facility Collaboration',
      },
      {
        path: 'collabration-network/collaboration-users/:collabId/:hostFacility/:partnerFacilityId',
        component: CollabUsersListComponent,
        canActivate: [authGuard],
        title: 'Health Advisor Collaboration Users',
      },
      {
        path: 'collabration-network/view-collaboration-users/:collabId',
        component: ViewCollabUsersComponent,
        canActivate: [authGuard],
        title: 'Health Advisor View Collaboration Users',
      },
      {
        path: 'pdf/view/:type/:src/:doc',
        loadComponent: () =>
          import('../../shared/view-attachment/view-attachment.component').then(
            (m) => m.ViewAttachmentComponent
          ),
        title: 'Health Advisor PDF View',
      },

      {
        path: 'follow-up/facility/:facilityId/doctors',
        loadComponent: () =>
          import(
            '../../shared/components/pages/follow-up-doctors-list/follow-up-doctors-list.component'
          ).then((m) => m.FollowUpDoctorsListComponent),
        canActivate: [authGuard],
        title: 'Health Advisor Follow Up Doctors List',
      },

      {
        path: 'follow-up/facility/:facilityId/doctors/:doctorId/patients',
        loadComponent: () =>
          import(
            '../../shared/components/pages/follow-up-patients-list/follow-up-patients-list.component'
          ).then((m) => m.FollowUpPatientsListComponent),
        title: 'Health Advisor Follow Up Patients List',
      },

      {
        path: 'follow-up/facility/:facilityId/doctors/:doctorId/patients/:patientId/prescriptions',
        loadComponent: () =>
          import(
            '../../shared/components/pages/follow-up-prescriptions-list/follow-up-prescriptions-list.component'
          ).then((m) => m.FollowUpPrescriptionsListComponent),
        title: 'Health Advisor Follow Up Prescriptions List',
      },
      {
        path: 'follow-up/facility/:facilityId/doctors/:doctorId/patients/:patientId/prescriptions/:visitId/labtestorders',
        loadComponent: () =>
          import(
            '../../shared/components/pages/follow-up-lab-test-orders/follow-up-lab-test-orders.component'
          ).then((m) => m.FollowUpLabTestOrdersComponent),
        title: 'Health Advisor Follow Up Lab Test Orders',
      },
      {
        path: 'follow-up/facility/:facilityId/doctors/:doctorId/patients/:patientId/prescriptions/:prescriptionId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/follow-up-prescriptions-view/follow-up-prescriptions-view.component'
          ).then((m) => m.FollowUpPrescriptionsViewComponent),
        title: 'Health Advisor Follow Up Prescriptions View',
      },

      {
        path: 'prescriptions/add-lab-test/:visitId/:patientId/:doctorId',
        loadComponent: () =>
          import(
            '../../shared/components/forms/lab-test-add-form/lab-test-add-form.component'
          ).then((m) => m.LabTestAddFormComponent),
        title: 'Health Advisor Add Lab Test',
      },
      {
        path: 'prescriptions/add-medicine/:visitId/:patientId/:doctorId/:prescriptionId',
        loadComponent: () =>
          import(
            '../../shared/components/forms/prescription-medicine-add-form/prescription-medicine-add-form.component'
          ).then((m) => m.PrescriptionMedicineAddFormComponent),
        title: 'Health Advisor Add Medicine',
      },
      {
        path: 'patient-profile/:patientId',
        loadComponent: () =>
          import(
            '../../shared/profiles/patient-profile/patient-profile.component'
          ).then((m) => m.PatientProfileComponent),
        title: 'Health Advisor Patient Profile',
      },
      {
        path: 'doctor-profile/:docId',
        loadComponent: () =>
          import(
            '../../shared/profiles/doctor-profile/doctor-profile.component'
          ).then((m) => m.DoctorProfileComponent),
        title: 'Health Advisor Doctor Profile',
      },

      {
        path: 'activity',
        component: HaActivityComponent,
        canActivate: [authGuard],
        title: 'Health Advisor Activity',
      },
      {
        path: 'activity/:activityId/collaboration',
        component: HaActivityAddActivityCollaborationComponent,
        title: 'Health Advisor Activity Collaboration',
      },
      {
        path: 'activity/:activityId/:facilityId/patients/create',
        loadComponent: () =>
          import(
            '../../shared/components/pages/activity/create-patient-page/create-patient-page.component'
          ).then((m) => m.CreatePatientPageComponent),
        title: 'Health Advisor Create Patient',
      },
      {
        path: 'activity/:activityId/:facilityId/patients/list-tab',
        loadComponent: () =>
          import(
            '../../shared/components/pages/activity/patients-tab-page/patients-tab-page.component'
          ).then((m) => m.PatientsTabPageComponent),
        children: [
          {
            path: '',
            redirectTo: 'list',
            pathMatch: 'full',
          },

          {
            path: 'list',
            loadComponent: () =>
              import(
                '../../shared/components/pages/activity/all-patients-list-page/all-patients-list-page.component'
              ).then((m) => m.AllPatientsListPageComponent),
            title: 'Health Advisor All Patients List',
          },
          {
            path: 'modify',
            loadComponent: () =>
              import(
                '../../shared/components/pages/activity/modify-patients-list-page/modify-patients-list-page.component'
              ).then((m) => m.ModifyPatientsListPageComponent),
            title: 'Health Advisor Modify Patients List',
          },
        ],
      },

      {
        path: 'lt/patients/:activityId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/lt-select-patient/lt-select-patient.component'
          ).then((m) => m.LtSelectPatientComponent),
        title: 'Health Advisor Lab Tests Select Patient',
      },
      {
        path: 'lt/packages/:activityId/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/lt-patient-packages/lt-patient-packages.component'
          ).then((m) => m.LtPatientPackagesComponent),
        title: 'Health Advisor Lab Tests Patient Packages',
      },
      {
        path: 'lt/package-view/:activityId/:patientId/:packageId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/view-package-details/view-package-details.component'
          ).then((m) => m.ViewPackageDetailsComponent),
        title: 'Health Advisor Lab Tests Package View',
      },
      {
        path: 'lt/reports/:activityId/:patientId/:packageId/:ppId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/view-lab-test-reports/view-lab-test-reports.component'
          ).then((m) => m.ViewLabTestReportsComponent),
        title: 'Health Advisor View Lab Test Reports',
      },
      {
        path: 'lt/invoice/:activityId/:patientId/:packageId/:ppId/:purchaseId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/lab-tests/view-lab-test-invoice/view-lab-test-invoice.component'
          ).then((m) => m.ViewLabTestInvoiceComponent),
        title: 'Health Advisor View Lab Test Invoice',
      },
      {
        path: 'pdf/view/:type/:src/:doc',
        loadComponent: () =>
          import('../../shared/view-attachment/view-attachment.component').then(
            (m) => m.ViewAttachmentComponent
          ),
        title: 'Health Advisor PDF View',
      },
      {
        path: 'care-plans',
        loadComponent: () =>
          import(
            '../../shared/care-plans/care-plans-list/care-plans-list.component'
          ).then((m) => m.CarePlansListComponent),
        title: 'Health Advisor Care Plans',
      },
      {
        path: 'care-plans/add-edit-care-plan',
        loadComponent: () =>
          import(
            '../../shared/care-plans/add-edit-care-plan/add-edit-care-plan.component'
          ).then((m) => m.AddEditCarePlanComponent),
        title: 'Health Advisor Add Care Plan',
      },
      {
        path: 'care-plans/:carePlanId/care-plan-activity',
        loadComponent: () =>
          import(
            '../../shared/care-plans/care-plan-activity-list/care-plan-activity-list.component'
          ).then((m) => m.CarePlanActivityListComponent),
        title: 'Health Advisor Care Plan Activity',
      },
      {
        path: 'care-plans/:carePlanId/care-plan-activity/add-edit-Activity',
        loadComponent: () =>
          import(
            '../../shared/care-plans/add-edit-care-plan-activity/add-edit-care-plan-activity.component'
          ).then((m) => m.AddEditCarePlanActivityComponent),
        title: 'Health Advisor Add Care Plan Activity',
      },

      {
        path: 'cp',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-select-activity/cp-select-activity.component'
          ).then((m) => m.CpSelectActivityComponent),
        title: 'Health Advisor Care Plan Select Activity',
      },
      {
        path: 'cp/:activityId/patients',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-select-patient/cp-select-patient.component'
          ).then((m) => m.CpSelectPatientComponent),
        title: 'Health Advisor Care Plan Select Patient',
      },
      {
        path: 'cp/:patientId/:activityId/purchased-care-plans',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-patient-purchased-care-plans/cp-patient-purchased-care-plans.component'
          ).then((m) => m.CpPatientPurchasedCarePlansComponent),
        title: 'Health Advisor Care Plan Patient Purchased Care Plans',
      },
      {
        path: 'cp/:patientId/:activityId/select-care-plan',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-select-plan/cp-select-plan.component'
          ).then((m) => m.CpSelectPlanComponent),
        title: 'Health Advisor Care Plan Select Plan',
      },
      {
        path: 'cp/:patientId/:activityId/:planId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-plan-deatils/cp-plan-deatils.component'
          ).then((m) => m.CpPlanDeatilsComponent),
        title: 'Health Advisor Care Plan Plan Details',
      },
      {
        path: 'cp/view-care-plan/:patientId/:activityId/:planId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-view-plan-deatils/cp-view-plan-deatils.component'
          ).then((m) => m.CpViewPlanDeatilsComponent),
        title: 'Health Advisor Care Plan View Plan Details',
      },
      {
        path: 'cp/cart/:patientId/:activityId/:cartId/checkout',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-cart/cp-cart.component'
          ).then((m) => m.CpCartComponent),
        title: 'Health Advisor Care Plan Cart',
      },
      {
        path: 'cp/invoice/:patientId/:activityId/:purchaseId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/cp-invoice/cp-invoice.component'
          ).then((m) => m.CpInvoiceComponent),
        title: 'Health Advisor Care Plan Invoice',
      },
      {
        path: 'cp/activity-month-view/:patientId/:activityId/:planId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-monthly-view/cp-monthly-view.component'
          ).then((m) => m.CpMonthlyViewComponent),
        title: 'Health Advisor Care Plan Monthly View',
      },
      {
        path: 'cp/activity-month-activities/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-monthly-activity-view/cp-monthly-activity-view.component'
          ).then((m) => m.CpMonthlyActivityViewComponent),
        title: 'Health Advisor Care Plan Monthly Activity View',
      },
      {
        path: 'cp/activity-month-activities/specialist-consultation/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-specialist-consultation-detail-page/cp-specialist-consultation-detail-page.component'
          ).then((m) => m.CpSpecialistConsultationDetailPageComponent),
        title: 'Health Advisor Care Plan Specialist Consultation Detail Page',
      },
      {
        path: 'cp/activity-month-activities/general-practitioner/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-general-practice-detail-page/cp-general-practice-detail-page.component'
          ).then((m) => m.CpGeneralPracticeDetailPageComponent),
        title: 'Health Advisor Care Plan General Practice Detail Page',
      },
      {
        path: 'cp/activity-month-activities/nurse-activity/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-nurse-activity-detail-page/cp-nurse-activity-detail-page.component'
          ).then((m) => m.CpNurseActivityDetailPageComponent),
        title: 'Health Advisor Care Plan Nurse Activity Detail Page',
      },
      {
        path: 'cp/activity-month-activities/health-advisor-role/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-health-advisor-role-detail-page/cp-health-advisor-role-detail-page.component'
          ).then((m) => m.CpHealthAdvisorRoleDetailPageComponent),
        title: 'Health Advisor Care Plan Health Advisor Role Detail Page',
      },
      {
        path: 'cp/activity-month-activities/recommended-tests/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-recommended-tests-detail-page/cp-recommended-tests-detail-page.component'
          ).then((m) => m.CpRecommendedTestsDetailPageComponent),
        title: 'Health Advisor Care Plan Recommended Tests Detail Page',
      },
      {
        path: 'cp/dietician-details/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/dietician/cp-dietician-details-view/cp-dietician-details-view.component'
          ).then((m) => m.CpDieticianDetailsViewComponent),
        title: 'Health Advisor Care Plan Dietician Details View',
      },
      {
        path: 'cp/dietician-assesment-form/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/dietician/cp-dietician-assesment-form/cp-dietician-assesment-form.component'
          ).then((m) => m.CpDieticianAssesmentFormComponent),
        title: 'Health Advisor Care Plan Dietician Assesment Form',
      },
      {
        path: 'cp/update-dietician-assesment/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/dietician/cp-dietician-assesment-form/cp-dietician-assesment-form.component'
          ).then((m) => m.CpDieticianAssesmentFormComponent),
        title: 'Health Advisor Care Plan Update Dietician Assesment Form',
      },
      {
        path: 'cp/rehab-details/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/rehab/cp-rehab-details-view/cp-rehab-details-view.component'
          ).then((m) => m.CpRehabDetailsViewComponent),
        title: 'Health Advisor Care Plan Rehab Details View',
      },
      {
        path: 'cp/rehab-assesment-form/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/rehab/cp-rehab-assesment-form/cp-rehab-assesment-form.component'
          ).then((m) => m.CpRehabAssesmentFormComponent),
        title: 'Health Advisor Care Plan Rehab Assesment Form',
      },
      {
        path: 'cp/update-rehab-assesment/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/rehab/cp-rehab-assesment-form/cp-rehab-assesment-form.component'
          ).then((m) => m.CpRehabAssesmentFormComponent),
        title: 'Health Advisor Care Plan Update Rehab Assesment Form',
      },
      {
        path: 'cp/activity-month-activities/ba/select-doctor/:patientId/:activityId/:planId/:monthNumber',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-select-doctor-for-appointment/cp-select-doctor-for-appointment.component'
          ).then((m) => m.CpSelectDoctorForAppointmentComponent),
        title: 'Health Advisor Care Plan Select Doctor For Appointment',
      },
      {
        path: 'cp/activity-month-activities/ba/select-doc-slot/:patientId/:activityId/:planId/:monthNumber/:doctorId/:facilityId/:deptId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/care-plan-purchase/activities/cp-choose-slot-and-book-appointment/cp-choose-slot-and-book-appointment.component'
          ).then((m) => m.CpChooseSlotAndBookAppointmentComponent),
        title: 'Health Advisor Care Plan Choose Slot And Book Appointment',
      },

      {
        path: 'patient-requests',
        loadComponent: () =>
          import(
            '../../shared/components/pages/patient-requests/ha-pr-sel-patient/ha-pr-sel-patient.component'
          ).then((m) => m.HaPrSelPatientComponent),
        title: 'Health Advisor Patient Requests',
      },
      {
        path: 'patient-requests/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/patient-requests/ha-patient-requests/ha-patient-requests.component'
          ).then((m) => m.HaPatientRequestsComponent),
        title: 'Health Advisor Patient Requests',
      },

      {
        path: 'book-appointment/select-patient',
        loadComponent: () =>
          import(
            '../../shared/components/pages/book-appointment/ba-sel-patient/ba-sel-patient.component'
          ).then((m) => m.BaSelPatientComponent),
        title: 'Health Advisor Book Appointment Select Patient',
      },
      {
        path: 'book-appointment/select-doctor/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/book-appointment/ba-sel-doc/ba-sel-doc.component'
          ).then((m) => m.BaSelDocComponent),
        title: 'Health Advisor Book Appointment Select Doctor',
      },
      {
        path: 'book-appointment/select-doc-slot/:patientId/:doctorId/:facilityId/:deptId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/book-appointment/ba-sel-slot-and-book/ba-sel-slot-and-book.component'
          ).then((m) => m.BaSelSlotAndBookComponent),
        title: 'Health Advisor Book Appointment Select Doctor Slot And Book',
      },
      {
        path: 'act/create-visit-details/:activityId/:patientId/:visitId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/activity-clinical-visit/act-patient-visit-forms/act-patient-visit-forms.component'
          ).then((m) => m.ActPatientVisitFormsComponent),
        title: 'Health Advisor Create Activity Visit Details',
      },
      {
        path: 'act/view-visit-details/:activityId/:patientId/:visitId',
        loadComponent: () =>
          import(
            './../../shared/components/pages/visit-activity/view-activity-based-clinical-visit/view-activity-based-clinical-visit.component'
          ).then((m) => m.ViewActivityBasedClinicalVisitComponent),
        title: 'Health Advisor View Activity Visit Details',
      },
      {
        path: 'view-patient-history/:patientId',
        loadComponent: () =>
          import(
            './../../shared/view-patient-history/view-patient-history.component'
          ).then((m) => m.ViewPatientHistoryComponent),
        title: 'Health Advisor View Patient History',
      },

      {
        path: 'vc',
        loadComponent: () =>
          import(
            '../../shared/components/pages/meet/video-meet/video-meet.component'
          ).then((m) => m.VideoMeetComponent),
        title: 'Health Advisor Video Consultation',
      },

      {
        path: 'instant-appointment/select-patient',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/patient/apt-sel-patient/apt-sel-patient.component'
          ).then((m) => m.AptSelPatientComponent),
        title: 'Health Advisor Instant Appointment Select Patient',
      },
      {
        path: 'instant-appointment/select-appointment/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/patient/apt-sel-apt-type/apt-sel-apt-type.component'
          ).then((m) => m.AptSelAptTypeComponent),
        title: 'Health Advisor Instant Appointment Select Appointment',
      },
      {
        path: 'instant-appointment/select-department/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/patient/apt-sel-dept/apt-sel-dept.component'
          ).then((m) => m.AptSelDeptComponent),
        title: 'Health Advisor Instant Appointment Select Department',
      },
      {
        path: 'instant-appointment/confirm/:patientId/:deptId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/patient/apt-confirm-slots-or-instant-booking/apt-confirm-slots-or-instant-booking.component'
          ).then((m) => m.AptConfirmSlotsOrInstantBookingComponent),
        title:
          'Health Advisor Instant Appointment Confirm Slots Or Instant Booking',
      },
      {
        path: 'instant-appointment/confirm-specialist-slots/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/patient/apt-confirm-specialist-slots/apt-confirm-specialist-slots.component'
          ).then((m) => m.AptConfirmSpecialistSlotsComponent),
        title: 'Health Advisor Instant Appointment Confirm Specialist Slots',
      },
      {
        path: 'instant-appointment/book-doctor/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/doctor/sel-doctor/sel-doctor.component'
          ).then((m) => m.SelDoctorComponent),
        title: 'Health Advisor Instant Appointment Book Doctor',
      },
      {
        path: 'instant-appointment/book-doctor-slot/:patientId/:doctorId/:facilityId/:deptId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/doctor/sel-docor-slot-and-book/sel-docor-slot-and-book.component'
          ).then((m) => m.SelDocorSlotAndBookComponent),
        title: 'Health Advisor Instant Appointment Book Doctor Slot And Book',
      },
      {
        path: 'instant-appointment/book-dietitian/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/dietitian/sel-dietitian/sel-dietitian.component'
          ).then((m) => m.SelDietitianComponent),
        title: 'Health Advisor Instant Appointment Book Dietitian',
      },
      {
        path: 'instant-appointment/book-dietitian-slot/:patientId/:doctorId/:facilityId/:deptId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/dietitian/sel-dietitian-slot-and-book/sel-dietitian-slot-and-book.component'
          ).then((m) => m.SelDietitianSlotAndBookComponent),
        title:
          'Health Advisor Instant Appointment Book Dietitian Slot And Book',
      },
      {
        path: 'instant-appointment/book-physio/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/physio/sel-physio/sel-physio.component'
          ).then((m) => m.SelPhysioComponent),
        title: 'Health Advisor Instant Appointment Book Physio',
      },
      {
        path: 'instant-appointment/book-physio-slot/:patientId/:doctorId/:facilityId/:deptId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/physio/sel-physio-slot-and-book/sel-physio-slot-and-book.component'
          ).then((m) => m.SelPhysioSlotAndBookComponent),
        title: 'Health Advisor Instant Appointment Book Physio Slot And Book',
      },
      {
        path: 'instant-appointment/book-phlebotomist/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/phlebotomist/sel-phle/sel-phle.component'
          ).then((m) => m.SelPhleComponent),
        title: 'Health Advisor Instant Appointment Book Phlebotomist',
      },
      {
        path: 'instant-appointment/book-phlebotomist-slot/:patientId/:doctorId/:facilityId/:deptId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/phlebotomist/sel-phle-slot-and-book/sel-phle-slot-and-book.component'
          ).then((m) => m.SelPhleSlotAndBookComponent),
        title:
          'Health Advisor Instant Appointment Book Phlebotomist Slot And Book',
      },
      {
        path: 'instant-appointment/book-nurse/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/nurse/sel-nurse/sel-nurse.component'
          ).then((m) => m.SelNurseComponent),
        title: 'Health Advisor Instant Appointment Book Nurse',
      },
      {
        path: 'instant-appointment/book-nurse-slot/:patientId/:doctorId/:facilityId/:deptId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/booking/nurse/sel-nurse-slot-and-book/sel-nurse-slot-and-book.component'
          ).then((m) => m.SelNurseSlotAndBookComponent),
        title: 'Health Advisor Instant Appointment Book Nurse Slot And Book',
      },
      {
        path: 'instant-appointment/patient-history',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/view/all-patient-appointments/all-patient-appointments.component'
          ).then((m) => m.AllPatientAppointmentsComponent),
        title: 'Health Advisor Instant Appointment Patient History',
      },
      {
        path: 'instant-appointment/patient-history/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/view/one-patient-appointments/one-patient-appointments.component'
          ).then((m) => m.OnePatientAppointmentsComponent),
        title: 'Health Advisor Instant Appointment Patient History',
      },
      {
        path: 'instant-appointment/view-appointment/:appId',
        loadComponent: () =>
          import(
            '../../shared/view-appointment/view-appointment.component'
          ).then((m) => m.ViewAppointmentComponent),
        title: 'Health Advisor Instant Appointment View Appointment',
      },

      {
        path: 'slot-instant-appointment/book-specialist/:patientId/:confirmedSlotId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/book-via-slots/bvs-select-specialist/bvs-select-specialist.component'
          ).then((m) => m.BvsSelectSpecialistComponent),
        title: 'Health Advisor Slot Instant Appointment Book Specialist',
      },
      {
        path: 'slot-instant-appointment/book-specialist-slot/:patientId/:confirmedSlotId/:doctorId/:facilityId/:deptId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/book-via-slots/bvs-select-specialist-slot-and-book/bvs-select-specialist-slot-and-book.component'
          ).then((m) => m.BvsSelectSpecialistSlotAndBookComponent),
        title:
          'Health Advisor Slot Instant Appointment Book Specialist Slot And Book',
      },

      {
        path: 'manage-appointments',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/manage/manage-all-appointments/manage-all-appointments.component'
          ).then((m) => m.ManageAllAppointmentsComponent),
        title: 'Health Advisor Manage Appointments',
      },
      {
        path: 'manage-appointments/reschedule/:appId/:slotId/:date/:slotType/:docId/:departmentId/:facilityId/:patientId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/manage/re-schedule-appointment/re-schedule-appointment.component'
          ).then((m) => m.ReScheduleAppointmentComponent),
        title: 'Health Advisor Manage Appointments Reschedule',
      },

      {
        path: 'create/doc-visit-details/:patientId/:visitId',
        loadComponent: () =>
          import(
            './../doctor/doc-clinical-visit/doc-create-visit-details/doc-create-visit-details.component'
          ).then((m) => m.DocCreateVisitDetailsComponent),
        title: 'Health Advisor Create Doc Visit Details',
      },
      {
        path: 'view/doc-visit-details/:patientId/:visitId',
        loadComponent: () =>
          import(
            './../../shared/view-visit-details/view-visit-details.component'
          ).then((m) => m.ViewVisitDetailsComponent),
        title: 'Health Advisor View Doc Visit Details',
      },
      {
        path: 'view/doc-visit-summary/:patientId/:visitId',
        loadComponent: () =>
          import('./../../shared/visit-summary/visit-summary.component').then(
            (m) => m.VisitSummaryComponent
          ),
        title: 'Health Advisor View Doc Visit Summary',
      },
      {
        path: 'view/dietician-assesment-form/:patientId/:dietConsultationId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/assesments/view-dietitian-form-details/view-dietitian-form-details.component'
          ).then((m) => m.ViewDietitianFormDetailsComponent),
        title: 'Health Advisor View Dietician Assesment Form',
      },
      {
        path: 'view/rehab-assesment-form/:patientId/:physioConsultationId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/assesments/view-physio-form-details/view-physio-form-details.component'
          ).then((m) => m.ViewPhysioFormDetailsComponent),
        title: 'Health Advisor View Rehab Assesment Form',
      },
      {
        path: 'view/phlebotomist-app-details/:patientId/:appointmentId',
        loadComponent: () =>
          import(
            '../../shared/components/pages/appointments/assesments/view-phlebotomist-app-details/view-phlebotomist-app-details.component'
          ).then((m) => m.ViewPhlebotomistAppDetailsComponent),
        title: 'Health Advisor View Phlebotomist App Details',
      },

      {
        path: 'patient-directory',
        loadComponent: () =>
          import(
            '../../shared/components/pages/patient-directory/all-patients-page/all-patients-page.component'
          ).then((m) => m.AllPatientsPageComponent),
        title: 'Health Advisor Patient Directory',
      },
      {
        path: 'view-patient-history/:patientId',
        loadComponent: () =>
          import(
            './../../shared/view-patient-history/view-patient-history.component'
          ).then((m) => m.ViewPatientHistoryComponent),
        title: 'Health Advisor View Patient History',
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HealthAdvisorRoutingModule {}
