<div class="stickybar">
  <div class="profile-sidebar doctor-sidebar profile-sidebar-new">
    <div class="widget-profile pro-widget-content pb-1">
      <div class="profile-info-widget">
        <a class="booking-doc-img">
          <!-- <img src='assets/images/avatar-image.webp' alt="User Image" /> -->

          <img
            *ngIf="user && user.photoLinkUrl"
            [src]="user.photoLinkUrl"
            alt="User Image"
            class="rounded-circle avatar-img"
          />
          <img
            *ngIf="user && !user.photoLinkUrl"
            src="assets/images/avatar-image.webp"
            alt="User Image"
            class="rounded-circle avatar-img"
          />
        </a>
        <div class="profile-det-info">
          <h3>
            <a
              >Dr {{ user && user.firstName ? user.firstName : "N/A" }}
              {{ user && user.lastName ? user.lastName : "N/A" }}</a
            >
          </h3>
          <div class="patient-details">
            <h5 class="mb-0">
              ID :
              {{ user && user.userId ? user.userId : "PT254654" }}
            </h5>
          </div>

          <div class="d-flex align-items-center justify-content-center pt-2">
            <span class="text-capitalize"
              >{{ user && user.gender ? user.gender : "N/A" }}
            </span>
            <!-- <span class="badge doctor-role-badge border-0 m-0"
              ><i class="fa-solid fa-circle text-success"></i
              >{{ user && user.dob ? calculateAge(user.dob) : "N/A" }}</span
            > -->
          </div>
          <!-- <div *ngIf="user && user.doctorExperience">
            Experience :
            {{
              convertDaysToYearsMonthsDays((user && user.doctorExperience) || 0)
            }}
          </div> -->
          <span class="badge doctor-role-badge"
            ><i class="fa-solid fa-circle"></i
            >{{
              userData && userData.roleDescription
                ? userData.roleDescription
                : "N/A"
            }}</span
          >
        </div>
      </div>
    </div>
    <!-- <div class="doctor-available-head">
        <div class="input-block input-block-new">
          <label class="form-label" for="availability"
            >Availability <span class="text-danger">*</span></label
          >
          <mat-select class="custom-mat-select" placeholder="I am Available Now">
            <mat-option value="available">I am Available Now</mat-option>
            <mat-option value="not-available">Not Available</mat-option>
          </mat-select>
        </div>
      </div> -->
    <div class="dashboard-widget">
      <nav class="dashboard-menu">
        <ul>
          <li routerLinkActive="active">
            <a [routerLink]="routes.haDashboard">
              <i class="isax isax-category-2"></i>
              <span>Dashboard</span>
            </a>
          </li>

          <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/lab-tests/select-activity']">
              <i class="fa fa-flask"></i>
              <span>Lab Tests</span>
            </a>
          </li>

          <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/medical-camp']">
              <i class="fa fa-tents"></i>
              <span>Medical Camp</span>
            </a>
          </li>
          <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/cp']">
              <i class="fa fa-heartbeat"></i>
              <span>Care Plans</span>
            </a>
          </li>
          <!--  -->
          <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/collabration']">
              <i class="fe fe-users"></i>
              <span>Collaboration</span>
            </a>
          </li>
          <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/activity']">
              <i class="fe fe-activity"></i>
              <span>Activity</span>
            </a>
          </li>
          <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/availability']">
              <i class="isax isax-calendar-1"></i>
              <span>Availability</span>
            </a>
          </li>
          <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/patient-requests']">
              <i class="fa fa-user-injured"></i>
              <span>Patient Requests</span>
            </a>
          </li>
          <!-- <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/appointments']">
              <i class="isax isax-calendar-1"></i>
              <span>Appointments</span>
            </a>
          </li>
          <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/availability']">
              <i class="isax isax-calendar-1"></i>
              <span>Availability</span>
            </a>
          </li>
          <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/vc']">
              <i class="fa fa-video"></i>
              <span>Video Consultation</span>
            </a>
          </li> -->

          <!-- <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/my-patients']">
              <i class="fa-solid fa-user-injured"></i>
              <span>My Patients</span>
            </a>
          </li> -->

          <!-- <li routerLinkActive="active">
            <a [routerLink]="['/health-advisor/all-patients']">
              <i class="isax isax-clock"></i>
              <span>All Patients</span>
            </a>
          </li>-->

          <!-- <li routerLinkActive="active">
            <a
              routerLink="follow-up/facility/{{ userData.facilityId }}/doctors"
            >
              <i class="fa-solid fa-calendar-check"></i>
              <span>Follow Ups</span>
            </a>
          </li> -->

          <li routerLinkActive="active">
            <a (click)="logout()">
              <i class="isax isax-logout"></i>
              <span>Logout</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</div>
