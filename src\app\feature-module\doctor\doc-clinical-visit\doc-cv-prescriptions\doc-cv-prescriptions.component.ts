import {
  Component,
  effect,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { DoctorService } from '../../services/doctor.service';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { FormFieldComponent } from 'src/app/shared/form-field/form-field.component';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';
import { firstValueFrom } from 'rxjs';
import { PatientService } from 'src/app/feature-module/patient/patient.service';
import {
  injectMutation,
  injectQuery,
} from '@tanstack/angular-query-experimental';
import { LoadingSpinnerComponent } from 'src/app/shared/loading-spinner/loading-spinner.component';
import { ErrorAlertComponent } from 'src/app/shared/error-alert/error-alert.component';
import moment from 'moment';
import Swal from 'sweetalert2';
import { ToastrService } from 'ngx-toastr';
import { AdminService } from 'src/app/admin/service/admin.service';

@Component({
  selector: 'app-doc-cv-prescriptions',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    LoadingSpinnerComponent,
    ErrorAlertComponent,
    FormFieldComponent,
  ],
  templateUrl: './doc-cv-prescriptions.component.html',
  styleUrl: './doc-cv-prescriptions.component.scss',
})
export class DocCvPrescriptionsComponent implements OnInit, OnChanges {
  authService = inject(AuthService);
  routeActive = inject(ActivatedRoute);
  router = inject(Router);
  doctorService = inject(DoctorService);
  util = inject(UtilFunctions);
  patientService = inject(PatientService);
  toastr = inject(ToastrService);
  adminService = inject(AdminService);

  @Input({ required: true }) visitId: any;
  @Input() isUpdate: boolean = false;
  @Output() getAllPrescriptions = new EventEmitter<any>();
  @Input() currentPatient: any;
  @Input() users: any;

  user = this.authService.getDataFromSession('user');
  patientId = this.adminService.decrypt(
    this.routeActive.snapshot.params['patientId']
  );

  isAddFormActive: boolean = false;

  prescriptionForm!: FormGroup;
  today = new Date();
  uniquePrescriptions: any[] = [];

  addedMedicines: any[] = [];
  editMedStatus = false;
  editMed: any = {};

  updatePresStatus: boolean = false;
  updatePrescription: any = {};

  constructor(private fb: FormBuilder) {
    this.buildPrescriptionForm();

    effect(() => {
      if (this.prescriptionList.isSuccess() && this.prescriptionList.data()) {
        const res = this.prescriptionList.data();
        console.log('prescriptionList', res);
        console.log('Visit Id', this.visitId);

        let filteredPrescriptionList = res.filter(
          (item: any) => item.visitId == this.visitId
        );
        const uniquePrescriptions = filteredPrescriptionList.filter(
          (prescription: { id: any }, index: any, self: any[]) =>
            index ===
            self.findIndex((p: { id: any }) => p.id === prescription.id)
        );
        this.getAllPrescriptions.emit(uniquePrescriptions);
        this.uniquePrescriptions = uniquePrescriptions;

        if (uniquePrescriptions.length == 0 && res.length > 0) {
          const previousData = res.slice(0, 2).map((d: any) => {
            return d.medicines;
          });

          console.log('previousData', previousData.flat());

          this.addedMedicines = previousData.flat().map((med: any) => {
            return {
              ...med,
              medi_id: crypto.randomUUID(),
            };
          });
        }
      }
    });
  }

  // Quries
  prescriptionList = injectQuery(() => ({
    queryKey: ['all-prescriptions', this.patientId],
    queryFn: async () => {
      const res: any = await firstValueFrom(
        this.patientService.getPatPrescriptionByPatientId(this.patientId)
      );

      return res;
    },
    refetchOnWindowFocus: false,
  }));

  // Mutations

  addPrescriptionMutation = injectMutation(() => ({
    mutationFn: (payload: any) =>
      firstValueFrom(this.patientService.createPatientPrescription(payload)),
    onSuccess: () => {
      this.prescriptionList.refetch();
      this.isAddFormActive = false;
      this.addedMedicines = [];
      this.prescriptionForm.reset();
      this.editMedStatus = false;
      this.editMed = {};
      this.toastr.success('Prescription Added Successfully');
    },
  }));

  deletePrescriptionMutation = injectMutation(() => ({
    mutationFn: (id: any) =>
      firstValueFrom(this.patientService.deletePrescription(id)),
    onSuccess: () => {
      this.prescriptionList.refetch();
      this.toastr.success('Prescription Deleted Successfully');
    },
  }));

  updatePrescriptionMutation = injectMutation(() => ({
    mutationFn: (payload: any) =>
      firstValueFrom(
        this.patientService.updatePatPrescriptionWithPrescriptionItem(payload)
      ),
    onSuccess: () => {
      this.prescriptionList.refetch();
      this.isAddFormActive = false;
      this.addedMedicines = [];
      this.prescriptionForm.reset();
      this.updatePresStatus = false;
      this.updatePrescription = {};
      this.editMedStatus = false;
      this.editMed = {};
      this.toastr.success('Prescription Updated Successfully');
    },
  }));

  ngOnInit(): void {}

  ngOnChanges(): void {}

  buildPrescriptionForm() {
    this.prescriptionForm = this.fb.group({
      medicineName: new FormControl('', Validators.required),
      strength: new FormControl('', Validators.required),
      frequency: new FormControl('', Validators.required),
      dosage: new FormControl(''),
      duration: new FormControl('', Validators.required),
      route: new FormControl('Oral'),
      timing: new FormControl('After Food'),
      instructions: new FormControl(''),
      prescriptionItemId: new FormControl(''),
    });
  }

  findCurrentUserFromUsers() {
    return this.users.find((u: any) => u.value == this.user.userId);
  }

  // toggleView(data: any) {
  //   this.router.navigate(['/doctor/prescription-items', data.id]);
  // }

  toggleView(data: any) {
    this.router.navigate(['/doctor/prescription-items', data.id], {
      queryParams: {
        // patientName: 'Kalees',
        patientName:
          this.currentPatient.firstName + ' ' + this.currentPatient.lastName,
      },
    });
  }

  toggleDeleteModal(data: any) {
    this.util.deleteModal().then((res) => {
      if (res) this.deletePrescriptionMutation.mutate(data.id);
    });
  }

  editMedicine(med: any) {
    this.editMedStatus = true;
    this.editMed = med;
    this.prescriptionForm.patchValue({
      medicineName: med.medicineName,
      strength: med.strength,
      frequency: med.frequency,
      duration: med.duration,
    });
  }

  updatePrescriptionFn(pres: any) {
    this.updatePresStatus = true;
    this.updatePrescription = pres;
    this.addedMedicines = pres.medicines.map((med: any) => {
      return {
        ...med,
        medi_id: med.id,
      };
    });
    this.isAddFormActive = true;

    console.log('updatePrescription', this.updatePrescription);
    console.log('addedMedicines', this.addedMedicines);
  }

  buildPayload() {
    return {
      medi_id: crypto.randomUUID(),
      medicineName: this.prescriptionForm.value.medicineName,
      strength: this.prescriptionForm.value.strength,
      frequency: this.prescriptionForm.value.frequency,
      dosage: this.prescriptionForm.value.dosage,
      duration: this.prescriptionForm.value.duration,
      route: this.prescriptionForm.value.route,
      timing: this.prescriptionForm.value.timing,
      instructions: this.prescriptionForm.value.instructions,
      reportedBy: this.user.roleDescription || '',
      reportedId: this.user.userId || '',
    };
  }

  submitForm() {
    if (this.prescriptionForm.valid) {
      let payload = this.buildPayload();

      if (this.editMedStatus) {
        this.editMedStatus = false;
        this.addedMedicines = this.addedMedicines.map((med) => {
          if (med.medi_id === this.editMed.medi_id) {
            return { ...payload, medi_id: this.editMed.medi_id };
          }
          return med;
        });
        this.editMed = {};
        this.prescriptionForm.reset();
        return;
      }
      console.log('payload', payload);
      this.addedMedicines.push(payload);
      this.prescriptionForm.reset();
    } else {
      console.log('Error:', this.prescriptionForm.errors);
      this.prescriptionForm.markAllAsTouched();
      console.error('Please Fill all the Required Fields');
    }
  }

  buildPrescriptionPayload() {
    return {
      visitId: +this.visitId,
      patientId: +this.patientId,
      prescribedBy: this.user.userId,
      prescriptionDate: moment(new Date()).format('YYYY-MM-DD'),
      medicines: this.addedMedicines,
    };
  }

  addPrescription() {
    if (this.addedMedicines.length === 0) {
      Swal.fire('Please Add at least one Medicine', '', 'error');
      console.error('Please Add at least one Medicine');
      return;
    }

    let payload = this.buildPrescriptionPayload();
    this.addPrescriptionMutation.mutate(payload);
  }

  buildUpdatePrescriptionPayload() {
    return {
      prescriptionId: this.updatePrescription.id,
      visitId: +this.visitId,
      patientId: +this.patientId,
      prescribedBy: this.user.userId,
      prescriptionDate: moment(new Date()).format('YYYY-MM-DD'),
      medicines: this.addedMedicines,
    };
  }

  updatePrescriptionSubmitFn() {
    if (this.addedMedicines.length === 0) {
      Swal.fire('Please Add at least one Medicine', '', 'error');
      console.error('Please Add at least one Medicine');
      return;
    }

    let payload = this.buildUpdatePrescriptionPayload();

    console.log('update payload', payload);

    this.updatePrescriptionMutation.mutate(payload);
  }

  get medicineName() {
    return this.prescriptionForm.get('medicineName') as FormControl;
  }
  get strength() {
    return this.prescriptionForm.get('strength') as FormControl;
  }
  get frequency() {
    return this.prescriptionForm.get('frequency') as FormControl;
  }
  get duration() {
    return this.prescriptionForm.get('duration') as FormControl;
  }
}
