import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import moment from 'moment';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { ToastrService } from 'ngx-toastr';
import { AdminService } from 'src/app/admin/service/admin.service';
import { SelectSearchComponent } from 'src/app/shared/select-search/select-search.component';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';

@Component({
  selector: 'attachements-resources',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    BsDatepickerModule,
    SelectSearchComponent,
  ],
  templateUrl: './cp-diet-attachements-resources.component.html',
  styleUrl: './cp-diet-attachements-resources.component.scss',
})
export class CpDietAttachementsResourcesComponent implements OnInit {
  @Input() patientId: any;
  @Input() consultationId: any | null = null;
  @Input() appointmentId: any;
  @Input() isCompleted: any;
  @Input() isUpdate: boolean = false;

  attachmentForm!: FormGroup;
  user = this.util.user;

  attachmentData: any = [];
  attachmentResourceData: any = [];
  selectedFile: File | null = null;
  uploadedFile: any;
  fileName: any;
  profileImageUrl: string | ArrayBuffer | null = null;

  constructor(
    private fb: FormBuilder,
    private util: UtilFunctions,
    private toastr: ToastrService,
    private adminService: AdminService
  ) {
    this.attachmentForm = this.fb.group({
      fileType: new FormControl(''),
      category: new FormControl(''),
      shareViaWhatsapp: new FormControl(''),
      shareViaEmail: new FormControl(''),
      source: new FormControl(''),
      attachments: new FormControl(''),
    });
  }

  ngOnInit(): void {
    if (this.consultationId) {
      this.getPatientAttachment(this.consultationId);
    }
  }

  asFormControl(control: AbstractControl | null): FormControl {
    return control as FormControl;
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;

    if (input.files && input.files[0]) {
      this.uploadedFile = input.files[0];
      console.log('file', this.uploadedFile);
      // Validate size (< 4MB)
      if (this.uploadedFile.size > 4 * 1024 * 1024) {
        alert('File size should be less than 4MB');
        return;
      }

      // Validate type
      // const allowedTypes = ['image/jpeg', 'image/png', 'image/svg+xml'];
      // if (!allowedTypes.includes(this.uploadedFile.type)) {
      //   alert('Only JPG, PNG, and SVG formats are allowed');
      //   return;
      // }
      const reader = new FileReader();
      reader.onload = () => {
        this.profileImageUrl = reader.result;
      };
      reader.readAsDataURL(this.uploadedFile);
    }
  }

  removeImage(): void {
    this.uploadedFile = null;
  }

  async getPatientAttachment(consultationId: any) {
    try {
      const data: any = await this.adminService.getPatientAttachmentById(
        Number(consultationId)
      );
      if (data?.length > 0) {
        this.attachmentData = data[0];
        this.getPatientAttachmentResource(this.attachmentData['id']);
        this.attachmentForm.patchValue({
          fileType: this.attachmentData.fileType || '',
          category: this.attachmentData.category || '',
          source: this.attachmentData.source || '',
          patientQuestions: this.attachmentData.patientQuestions || '',
        });
        this.fileName = this.attachmentData.fileName;
      } else {
        this.attachmentData = [];
        this.fileName = null;
      }
    } catch (error) {
      this.toastr.error('Failed to fetch Follow Up data.');
      console.error(error);
    }
  }

  async getPatientAttachmentResource(consultationId: any) {
    try {
      const data: any = await this.adminService.getPatientAttachedResourceById(
        Number(consultationId)
      );
      if (data?.length > 0) {
        this.attachmentResourceData = data[0];
        this.attachmentForm.patchValue({
          shareViaWhatsapp:
            this.attachmentResourceData.sharedVia == 'WhatsApp'
              ? this.attachmentResourceData.sharedTo
              : '',
          shareViaEmail:
            this.attachmentResourceData.sharedVia == 'Email'
              ? this.attachmentResourceData.sharedTo
              : '',
        });
      } else {
        this.attachmentResourceData = [];
      }
    } catch (error) {
      this.toastr.error('Failed to fetch Follow Up data.');
      console.error(error);
    }
  }

  upload() {
    if (this.attachmentData.length > 0) {
      const fd = new FormData();
      fd.append(
        'id',
        this.attachmentData['id'] ? this.attachmentData['id'] : ''
      );
      fd.append('patientId', this.patientId ? this.patientId : '');
      fd.append(
        'dietConsultationId',
        this.consultationId ? this.consultationId : 0
      );
      fd.append('source', this.source.value ? this.source.value : '');
      fd.append('fileType', this.fileType.value ? this.fileType.value : '');
      fd.append('category', this.category.value ? this.category.value : '');
      fd.append('shareViaWhatsapp', this.shareViaWhatsapp.value ? '1' : '0');
      fd.append('shareViaEmail', this.shareViaEmail.value ? '1' : '0');
      fd.append('createdDate', moment(new Date()).format('YYYY-MM-DD'));
      fd.append('uploadedBy', this.user.firstName);
      if (this.uploadedFile) {
        fd.append('file', this.uploadedFile);
      }
      this.adminService.updatePatientAttachment(fd).subscribe({
        next: (res: any) => {
          this.updateAttachmentResource(this.attachmentData.id);
        },
        error: (err) => {
          this.toastr.error('Failed to save Follow Up.');
          console.error(err);
        },
      });
    } else {
      const fd = new FormData();
      fd.append('patientId', this.patientId ? this.patientId : '');
      fd.append(
        'dietConsultationId',
        this.consultationId ? this.consultationId : 0
      );
      fd.append('source', this.source.value ? this.source.value : '');
      fd.append('fileType', this.fileType.value ? this.fileType.value : '');
      fd.append('category', this.category.value ? this.category.value : '');
      fd.append('shareViaWhatsapp', this.shareViaWhatsapp.value ? '1' : '0');
      fd.append('shareViaEmail', this.shareViaEmail.value ? '1' : '0');
      fd.append('createdDate', moment(new Date()).format('YYYY-MM-DD'));
      fd.append('uploadedBy', this.user.firstName);
      if (this.uploadedFile) {
        fd.append('file', this.uploadedFile);
      }
      this.adminService.createFollowAttachment(fd).subscribe({
        next: (res: any) => {
          this.createAttachmentResource(res['insertedId']);
        },
        error: (err) => {
          this.toastr.error('Failed to save Follow Up.');
          console.error(err);
        },
      });
    }
  }

  createAttachmentResource(attachmentId: any) {
    let payload = {
      attachmentId: attachmentId ? Number(attachmentId) : 0,
      sharedVia: this.shareViaWhatsapp.value ? 'Whatsapp' : 'Email',
      sharedTo: this.shareViaWhatsapp.value
        ? this.shareViaWhatsapp.value
        : this.shareViaEmail.value,
      sharedAt: new Date(),
      createdDate: new Date(),
    };
    this.adminService.createFollowAttachmentResource(payload).subscribe({
      next: (res: any) => {
        this.toastr.success('Attachment Resource saved successfully.');
        this.updateAppointmentStatus();
      },
      error: (err) => {
        this.toastr.error('Failed to save Follow Up.');
        console.error(err);
      },
    });
  }

  updateAttachmentResource(attachmentId: any) {
    let payload = {
      id: this.attachmentResourceData.id,
      attachmentId: attachmentId ? Number(attachmentId) : 0,
      sharedVia: this.shareViaWhatsapp.value ? 'Whatsapp' : 'Email',
      sharedTo: this.shareViaWhatsapp.value
        ? this.shareViaWhatsapp.value
        : this.shareViaEmail.value,
      sharedAt: new Date(),
      createdDate: new Date(),
    };
    this.adminService.updatePatientResource(payload).subscribe({
      next: (res: any) => {
        this.toastr.success('Attachment Resource Updated successfully.');
      },
      error: (err) => {
        this.toastr.error('Failed to save Follow Up.');
        console.error(err);
      },
    });
  }

  updateAppointmentStatus() {
    this.adminService
      .updateAppointmentOnGoingStatus(Number(this.appointmentId))
      .subscribe({
        next: () => {
          this.toastr.success('Appointment Status Updated successfully.');
        },
        error: (err) => {
          this.toastr.error('Failed to Update Appointment Status.');
          console.error(err);
        },
      });
  }

  get shareViaWhatsapp() {
    return this.attachmentForm.get('shareViaWhatsapp') as FormControl;
  }
  get category() {
    return this.attachmentForm.get('category') as FormControl;
  }
  get shareViaEmail() {
    return this.attachmentForm.get('shareViaEmail') as FormControl;
  }
  get source() {
    return this.attachmentForm.get('source') as FormControl;
  }
  get attachments() {
    return this.attachmentForm.get('attachments') as FormControl;
  }
  get fileType() {
    return this.attachmentForm.get('fileType') as FormControl;
  }
}
