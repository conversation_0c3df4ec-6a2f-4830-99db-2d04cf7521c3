import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import moment from 'moment';
import {
  BsDatepickerConfig,
  BsDatepickerDirective,
  BsDatepickerModule,
} from 'ngx-bootstrap/datepicker';
import { ToastrService } from 'ngx-toastr';
import { AdminService } from 'src/app/admin/service/admin.service';
import { PageWithSearchPaginationComponent } from 'src/app/shared/page-with-search-pagination/page-with-search-pagination.component';
import { SelectSearchComponent } from 'src/app/shared/select-search/select-search.component';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';

@Component({
  selector: 'app-cp-diet-plan',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    BsDatepickerModule,
    SelectSearchComponent,
    PageWithSearchPaginationComponent,
  ],
  templateUrl: './cp-diet-plan.component.html',
  styleUrl: './cp-diet-plan.component.scss',
})
export class CpDietPlanComponent implements OnInit {
  editManualTherapy(_t63: number) {
    throw new Error('Method not implemented.');
  }
  deleteManualTherapy(_t63: number) {
    throw new Error('Method not implemented.');
  }
  @Input() patientId: any;
  @Input() consultationId: any | null = null;
  @Input() appointmentId: any;
  @Input() isCompleted: any;
  @Input() isUpdate: boolean = false;

  dietPlanForm!: FormGroup;
  dietData: any = [];
  patchedData: any;
  filterDietData: any;

  user = this.util.user;
  constructor(
    private fb: FormBuilder,
    private util: UtilFunctions,
    private toastr: ToastrService,
    private adminService: AdminService
  ) {
    this.dietPlanForm = this.fb.group({
      planDate: new FormControl(''),
      time: new FormControl(''),
      meal: new FormControl(''),
      foodItems: new FormControl(''),
      quantity: new FormControl(''),
      notes: new FormControl(''),
    });
  }

  ngOnInit(): void {
    if (this.consultationId) {
      this.getDietPlan(this.consultationId);
    }
  }

  asFormControl(control: AbstractControl | null): FormControl {
    return control as FormControl;
  }

  async getDietPlan(consultationId: any) {
    try {
      const data: any = await this.adminService.getdietPlanById(
        Number(consultationId),
        Number(this.patientId)
      );
      if (data?.length > 0) {
        this.dietData = data;
      } else {
        this.dietData = [];
      }
    } catch (error) {
      this.toastr.error('Failed to fetch Diet Plan data.');
      console.error(error);
    }
  }

  editDiet(id: any) {
    let diet = this.dietData.find((item: any) => item.id == id);
    this.patchedData = diet;
    this.dietPlanForm.patchValue({
      planDate: diet?.planDate || '',
      time: diet.mealTime || '',
      meal: diet.mealLabel || '',
      foodItems: diet.foodItem || '',
      quantity: diet.quantity || '',
      notes: diet.notes || '',
    });
  }

  saveDietPlan() {
    if (this.dietPlanForm.invalid) {
      this.toastr.error('Please fill all required fields.');
      return;
    }

    if (this.dietData.length > 0) {
      let payload = {
        id: this.patchedData.id ? this.patchedData.id : null,
        dietConsultationId: this.consultationId
          ? Number(this.consultationId)
          : 0,
        patientId: Number(this.patientId),
        planDate: this.planDate.value ? this.planDate.value : null,
        mealTime: this.time.value ? this.time.value : null,
        mealLabel: this.meal.value ? this.meal.value : null,
        foodItem: this.foodItems.value ? this.foodItems.value : null,
        quantity: this.quantity.value ? this.quantity.value : null,
        notes: this.notes.value ? this.notes.value : null,
        createdDate: new Date(),
      };
      this.adminService.updateDietPlan(payload).subscribe({
        next: () => {
          this.toastr.success('Diet Plan saved successfully.');
          this.getDietPlan(this.consultationId);
          this.dietPlanForm.reset();
        },
        error: (err) => {
          this.toastr.error('Failed to save Diet Plan.');
          console.error(err);
        },
      });
    } else {
      let payload = {
        dietConsultationId: this.consultationId
          ? Number(this.consultationId)
          : 0,
        patientId: Number(this.patientId),
        planDate: moment(new Date()).format('YYYY-MM-DD'),
        mealTime: this.time.value ? this.time.value : null,
        mealLabel: this.meal.value ? this.meal.value : null,
        foodItem: this.foodItems.value ? this.foodItems.value : null,
        quantity: this.quantity.value ? this.quantity.value : null,
        notes: this.notes.value ? this.notes.value : null,
        createdDate: new Date(),
      };
      this.adminService.createDietPlan(payload).subscribe({
        next: () => {
          this.toastr.success('Diet Plan saved successfully.');
          this.getDietPlan(this.consultationId);
          this.updateAppointmentStatus();
          this.dietPlanForm.reset();
        },
        error: (err) => {
          this.toastr.error('Failed to save Diet Plan.');
          console.error(err);
        },
      });
    }
  }

  updateAppointmentStatus() {
    this.adminService
      .updateAppointmentOnGoingStatus(Number(this.appointmentId))
      .subscribe({
        next: () => {
          this.toastr.success('Appointment Status Updated successfully.');
        },
        error: (err) => {
          this.toastr.error('Failed to Update Appointment Status.');
          console.error(err);
        },
      });
  }

  get planDate() {
    return this.dietPlanForm.get('planDate') as FormControl;
  }
  get time() {
    return this.dietPlanForm.get('time') as FormControl;
  }
  get meal() {
    return this.dietPlanForm.get('meal') as FormControl;
  }
  get foodItems() {
    return this.dietPlanForm.get('foodItems') as FormControl;
  }
  get quantity() {
    return this.dietPlanForm.get('quantity') as FormControl;
  }
  get notes() {
    return this.dietPlanForm.get('notes') as FormControl;
  }
}
