import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { CommonService } from 'src/app/shared/common/common.service';
import { routes } from 'src/app/shared/routes/routes';
import { UserAccountsService } from 'src/app/shared/services/user-accounts.service';
import { UserService } from 'src/app/shared/services/user.service';
import { buildEncryptedRouteFromTemplate } from 'src/app/shared/utils/buildEncryptedRouteFromTemplate';
@Component({
  selector: 'app-am-sidebar',
  standalone: false,
  templateUrl: './am-sidebar.component.html',
  styleUrl: './am-sidebar.component.scss',
})
export class AmSidebarComponent {
  public routes = routes;
  public base = '';
  public page = '';
  public last = '';

  user: any;
  userData: any | null = null;
  constructor(
    private router: Router,
    private toastr: ToastrService,
    private common: CommonService,
    private authService: AuthService,
    private userService: UserService,
    private userAccountService: UserAccountsService
  ) {
    this.common.base.subscribe((res: string) => {
      this.base = res;
    });
    this.common.page.subscribe((res: string) => {
      this.page = res;
    });
    this.common.last.subscribe((res: string) => {
      this.last = res;
    });
    this.user = this.authService.getDataFromSession('user');
    console.log('facility', this.user);
    this.getUserById(this.user['userId']);
    // console.log('--User : ', this.user);
  }

  calculateAge1(dob: string): string {
    const birthDate = new Date(dob);
    const today = new Date();

    let years = today.getFullYear() - birthDate.getFullYear();
    let months = today.getMonth() - birthDate.getMonth();

    if (months < 0) {
      years--;
      months += 12;
    }

    return `${years} Years ${months.toString().padStart(2, '0')} Months`;
  }

  calculateAge(dob: string): string {
    const birthDate = new Date(dob);
    const today = new Date();

    let years = today.getFullYear() - birthDate.getFullYear();
    let months = today.getMonth() - birthDate.getMonth();
    let days = today.getDate() - birthDate.getDate();

    if (days < 0) {
      months--;
      const lastDayOfMonth = new Date(
        today.getFullYear(),
        today.getMonth(),
        0
      ).getDate();
      days += lastDayOfMonth;
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    return `${years} Years ${months.toString().padStart(2, '0')} Months ${days
      .toString()
      .padStart(2, '0')} Days`;
  }

  enRouteFacility() {
    let encryptedLink: any;
    if (!encryptedLink) {
      encryptedLink = this.getRoute(
        '/admin/manager/follow-up/facility/:facilityId/doctors',
        {
          facilityId: String(this.user.facilityId),
        }
      );
    }
    this.router.navigate([encryptedLink]);
  }

  getRoute(url: any, keyObj: any) {
    let route = buildEncryptedRouteFromTemplate(url, keyObj, this.authService);
    return route;
  }

  async getUserById(userId: string) {
    if (!userId) {
      this.toastr.error('Patient Id Invalid', '', { timeOut: 3000 });
      return;
    }

    try {
      const patientData: any = await this.userService.getUserByUserId(userId);
      if (patientData.length > 0) {
        this.userData = patientData[0];
        if (!patientData[0].photoLink) {
          console.warn('No photoLink available for the patient.');
          return;
        }
        const pathname = this.getNormalizedPathname(patientData[0].photoLink);
        const securedUrl = await this.fetchSecuredUrl(pathname);
        this.user.photoLinkUrl = securedUrl;
      } else {
        this.toastr.error('No patient data found', '', { timeOut: 3000 });
      }
    } catch (error) {
      console.error('Error fetching patient details:', error);
      this.toastr.error('Error fetching patient details', '', {
        timeOut: 3000,
      });
    }
  }

  private getNormalizedPathname(filePath: string): string {
    if (!filePath) {
      console.error('Invalid file path:', filePath);
      return '';
    }

    try {
      const url = new URL(filePath, window.location.origin); // Handle relative paths
      return url.pathname.replace(/^\/+/, '');
    } catch (error) {
      console.error('Error parsing URL:', error, 'File Path:', filePath);
      return '';
    }
  }

  async fetchSecuredUrl(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.userAccountService.getSecureImgUrl({ fileUrl: url }).subscribe({
        next: (response: any) => {
          resolve(response['url']);
        },
        error: (error: any) => {
          this.toastr.error('', error.error.message, { timeOut: 3000 });
          reject(error);
        },
      });
    });
  }

  logout() {
    this.userData = null;
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/login']);
    this.toastr.success('Logged out successfully!');
  }
}
