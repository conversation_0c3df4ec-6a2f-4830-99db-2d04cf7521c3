import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AdminService } from 'src/app/admin/service/admin.service';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { CommonService } from 'src/app/shared/common/common.service';
import { routes } from 'src/app/shared/routes/routes';
import { UserAccountsService } from 'src/app/shared/services/user-accounts.service';
import { UserService } from 'src/app/shared/services/user.service';
import { buildEncryptedRouteFromTemplate } from 'src/app/shared/utils/buildEncryptedRouteFromTemplate';

@Component({
  selector: 'app-patient-sidebar',
  templateUrl: './patient-sidebar.component.html',
  styleUrl: './patient-sidebar.component.scss',
  standalone: false,
})
export class PatientSidebarComponent {
  public routes = routes;
  public base = '';
  public page = '';
  public last = '';
  patientData: any;
  loggedInUser: any | null = null;
  userData: any | null = null;
  patientId: string = '';
  user: any;

  constructor(
    private router: Router,
    private toastr: ToastrService,
    private common: CommonService,
    private authService: AuthService,
    private userService: UserService,
    private userAccountService: UserAccountsService,
    private adminService: AdminService
  ) {
    this.loggedInUser = this.authService.getDataFromSession('user');
    this.patientId = this.loggedInUser['userId'];
    this.getUserById(this.loggedInUser['userId']);

    this.userService.onUserRefresh().subscribe(() => {
      this.getUserById(this.loggedInUser['userId']);
    });
    this.common.base.subscribe((base: string) => {
      this.base = base;
    });
    this.common.page.subscribe((page: string) => {
      this.page = page;
    });
    this.common.last.subscribe((last: string) => {
      this.last = last;
    });
  }

  getRoute(url: any, keyObj: any) {
    let route = buildEncryptedRouteFromTemplate(url, keyObj, this.authService);
    return route;
  }

  goToCarePlansPage() {
    this.router.navigate([
      '/patient/cp/',
      this.adminService.encrypt(this.patientId),
      'purchased-care-plans',
    ]);
  }

  redirectToFacility() {
    let encryptedLink: any;
    if (!encryptedLink) {
      encryptedLink = this.getRoute(
        '/patient/follow-up/facility/:facilityId/doctors',
        {
          facilityId: String(this.loggedInUser.facilityId),
        }
      );
    }
    this.router.navigate([encryptedLink]);
  }

  async getUserById(userId: string) {
    if (!userId) {
      this.toastr.error('Patient Id Invalid', '', { timeOut: 3000 });
      return;
    }

    try {
      const patientData: any = await this.userService.getUserByUserId(userId);
      if (patientData.length > 0) {
        this.userData = patientData[0];
        if (!patientData[0].photoLink) {
          console.warn('No photoLink available for the patient.');
          return;
        }
        const pathname = this.getNormalizedPathname(patientData[0].photoLink);
        const securedUrl = await this.fetchSecuredUrl(pathname);
        this.userData.photoLinkUrl = securedUrl;
      } else {
        this.toastr.error('No patient data found', '', { timeOut: 3000 });
      }
    } catch (error) {
      console.error('Error fetching patient details:', error);
      this.toastr.error('Error fetching patient details', '', {
        timeOut: 3000,
      });
    }
  }

  calculateAge(dobString: string): string {
    const dob = new Date(dobString);
    const today = new Date();

    let years = today.getFullYear() - dob.getFullYear();
    let months = today.getMonth() - dob.getMonth();
    let days = today.getDate() - dob.getDate();

    if (days < 0) {
      months--;
      const prevMonth = new Date(today.getFullYear(), today.getMonth(), 0);
      days += prevMonth.getDate();
    }
    if (months < 0) {
      years--;
      months += 12;
    }
    return `${years} years, ${months} months, ${days} days`;
  }

  async fetchSecuredUrl(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.userAccountService.getSecureImgUrl({ fileUrl: url }).subscribe({
        next: (response: any) => {
          resolve(response['url']);
        },
        error: (error: any) => {
          this.toastr.error('', error.error.message, { timeOut: 3000 });
          reject(error);
        },
      });
    });
  }

  // private getNormalizedPathname(filePath: string): string {
  //   return new URL(filePath).pathname.replace(/^\/+/, '');
  // }

  private getNormalizedPathname(filePath: string): string {
    if (!filePath) {
      console.error('Invalid file path:', filePath);
      return '';
    }

    try {
      const url = new URL(filePath, window.location.origin); // Handle relative paths
      return url.pathname.replace(/^\/+/, '');
    } catch (error) {
      console.error('Error parsing URL:', error, 'File Path:', filePath);
      return '';
    }
  }

  logout() {
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/login']);
    this.toastr.success('Logged out successfully!');
  }
}
