import {
  Component,
  EventEmitter,
  HostListener,
  inject,
  Output,
} from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { CommonService } from 'src/app/shared/common/common.service';
import { DataService } from 'src/app/shared/data/data.service';
import { header } from 'src/app/shared/models/sidebar-model';
import { routes } from 'src/app/shared/routes/routes';
import { UserAccountsService } from 'src/app/shared/services/user-accounts.service';
import { SidebarService } from 'src/app/shared/sidebar/sidebar.service';
import { DoctorService } from '../../services/doctor.service';
import { AdminService } from 'src/app/admin/service/admin.service';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';

@Component({
  selector: 'app-doc-header',
  standalone: false,
  templateUrl: './doc-header.component.html',
  styleUrl: './doc-header.component.scss',
})
export class DocHeaderComponent {
  @Output() toggleSidebar = new EventEmitter<void>();

  onToggleClick() {
    this.toggleSidebar.emit();
  }

  userData: any | null = null;
  docId: string = '';
  user: any;
  public searchField = false;
  public routes = routes;
  public header: header[];
  base = '';
  page = '';
  last = '';
  isSearch = false;
  isdark = true;
  islight = false;
  themeColor = 'light-mode';
  constructor(
    private common: CommonService,
    private data: DataService,
    public sidebar: SidebarService,
    private router: Router,
    private authService: AuthService,
    private userAccountService: UserAccountsService,
    private toastr: ToastrService,
    private doctorService: DoctorService,
    private adminService: AdminService
  ) {
    this.common.base.subscribe((res: string) => {
      this.base = res;
    });
    this.common.page.subscribe((res: string) => {
      this.page = res;
    });
    this.common.last.subscribe((res: string) => {
      this.last = res;
    });
    this.header = this.data.header;

    this.userData = this.authService.getDataFromSession('user');
    this.docId = this.userData['userId'];
    // this.getDoctorDetailsById(this.userData['userId']);
  }

  public hideSidebar(): void {
    this.sidebar.closeSidebar();
  }
  toggleSearch() {
    this.searchField = !this.searchField;
  }
  openSearch(): void {
    this.isSearch = !this.isSearch;
  }
  public navigation() {
    this.router.navigate([routes.search1]);
  }

  openRoleSelectionPage() {
    this.router.navigate([
      `/role-selection/${this.adminService.encrypt(this.userData.email)}`,
    ]);
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    const scroll =
      window.pageYOffset ||
      document.documentElement.scrollTop ||
      document.body.scrollTop ||
      0;

    const headerOne = document.querySelector('.header-one');
    if (headerOne && scroll > 35) {
      headerOne.classList.add('header-space');
    } else if (headerOne) {
      headerOne.classList.remove('header-space');
    }

    const headerTen = document.querySelector('.header-ten');
    if (headerTen && scroll > 35) {
      headerTen.classList.add('header-space');
    } else if (headerTen) {
      headerTen.classList.remove('header-space');
    }
  }
  ngOnInit(): void {
    const themeColor = localStorage.getItem('themeColor') || 'light-mode';
    this.sidebar.changeThemeColor(themeColor);
  }
  darkMode(): void {
    this.isdark = !this.isdark;
    this.islight = !this.islight;
  }

  onSubmit(): void {
    this.router.navigateByUrl('/search-doctor/search1');
  }
  navigate(): void {
    this.router.navigate([routes.search1]);
  }

  // async getDoctorDetailsById(docId: string) {
  //   if (!docId) {
  //     this.toastr.error('Patient Id Invalid', '', { timeOut: 3000 });
  //     return;
  //   }

  //   try {
  //     const patientData: any = await this.userAccountService.getPatientById(
  //       patientId
  //     );
  //     if (patientData.length > 0) {
  //       this.user = patientData[0];
  //     } else {
  //       this.toastr.error('No doctor data found', '', { timeOut: 3000 });
  //     }
  //   } catch (error) {
  //     console.error('Error fetching doctor details:', error);
  //     this.toastr.error('Error fetching doctor details', '', {
  //       timeOut: 3000,
  //     });
  //   }
  // }

  util = inject(UtilFunctions);

  doctorDetails = injectQuery(() => ({
    queryKey: ['doctor-details', this.docId],
    queryFn: async () => {
      const res: any = await this.doctorService.getDoctorById(
        this.docId,
        this.userData.facilityId
      );

      await Promise.all(
        res.map(async (doc: any) => {
          if (doc.photoLink) {
            const pathname = this.util.getNormalizedPathname(doc.photoLink);
            const securedUrl = await this.util.fetchSecuredUrl(pathname);
            doc.photoLink = securedUrl;
          } else {
            doc.photoLink = this.util.getNormalAvatar();
          }
        })
      );

      return res[0];
    },
    refetchOnWindowFocus: false,
  }));

  async getDoctorDetailsById(docId: string) {
    if (!docId) {
      this.toastr.error('Doctor Id Invalid', '', { timeOut: 3000 });
      return;
    }

    try {
      const patientData: any = await this.doctorService.getDoctorById(
        docId,
        this.userData.facilityId
      );
      if (patientData.length > 0) {
        this.user = patientData[0];

        if (!patientData[0].photoLink) {
          console.warn('No photoLink available for the doctor.');
          return;
        }
        const pathname = this.getNormalizedPathname(patientData[0].photoLink);
        const securedUrl = await this.fetchSecuredUrl(pathname);
        this.user.photoLinkUrl = securedUrl;

        // this.doctorService.setCurrentDoctorDetails(patientData[0]);
        // const globalUser = this.doctorService.getCurrentDoctorDetails();
        // console.log('--Current user detail header : ', globalUser());
        // this.user = globalUser();
      } else {
        this.toastr.error('No doctor data found', '', { timeOut: 3000 });
      }
    } catch (error) {
      console.error('Error fetching doctor details:', error);
      this.toastr.error('Error fetching doctor details', '', {
        timeOut: 3000,
      });
    }
  }

  async fetchSecuredUrl(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.userAccountService.getSecureImgUrl({ fileUrl: url }).subscribe({
        next: (response: any) => {
          resolve(response['url']);
        },
        error: (error: any) => {
          this.toastr.error('', error.error.message, { timeOut: 3000 });
          reject(error);
        },
      });
    });
  }

  private getFileType(filePath: string): string {
    const PDF_EXTENSION = '.pdf';
    return filePath.endsWith(PDF_EXTENSION) ? 'pdf' : 'image';
  }

  // private getNormalizedPathname(filePath: string): string {
  //   return new URL(filePath).pathname.replace(/^\/+/, '');
  // }

  private getNormalizedPathname(filePath: string): string {
    if (!filePath) {
      console.error('Invalid file path:', filePath);
      return '';
    }

    try {
      const url = new URL(filePath, window.location.origin); // Handle relative paths
      return url.pathname.replace(/^\/+/, '');
    } catch (error) {
      console.error('Error parsing URL:', error, 'File Path:', filePath);
      return '';
    }
  }

  logout() {
    this.userData = null;
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/login']);
    this.toastr.success('Logged out successfully!');
  }
}
