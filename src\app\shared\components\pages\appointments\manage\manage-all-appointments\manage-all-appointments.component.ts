import { CommonModule } from '@angular/common';
import { AdminFriendlyPageWrapperComponent } from 'src/app/shared/components/re-use/admin-friendly-page-wrapper/admin-friendly-page-wrapper.component';
import { Component, effect, inject } from '@angular/core';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';
import {
  injectMutation,
  injectQuery,
} from '@tanstack/angular-query-experimental';
import { LoadingSpinnerComponent } from 'src/app/shared/loading-spinner/loading-spinner.component';
import { ErrorAlertComponent } from 'src/app/shared/error-alert/error-alert.component';
import { FormsModule } from '@angular/forms';
import { PopUpModalComponent } from 'src/app/shared/components/re-use/pop-up-modal/pop-up-modal.component';
import { SubmitBtnComponent } from 'src/app/shared/components/re-use/submit-btn/submit-btn.component';
import { I_APPOINTMENT_FOR_CONFIRMATION } from '../../view/model';

@Component({
  selector: 'app-manage-all-appointments',
  imports: [
    AdminFriendlyPageWrapperComponent,
    CommonModule,
    LoadingSpinnerComponent,
    ErrorAlertComponent,
    FormsModule,
    PopUpModalComponent,
    SubmitBtnComponent,
  ],
  templateUrl: './manage-all-appointments.component.html',
  styleUrl: './manage-all-appointments.component.scss',
})
export class ManageAllAppointmentsComponent {
  util = inject(UtilFunctions);
  user = this.util.user;

  activeTab: 'instant' | 'scheduled' | 'face-to-face' | 'lab' = 'face-to-face';

  faceToFaceAppointments: any[] = [];
  filteredFaceToFaceAppointments: any[] = [];
  faceToFaceSearch: string = '';

  instantAppointments: any[] = [];
  filteredInstantAppointments: any[] = [];
  instantSearch: string = '';

  scheduledAppointments: any[] = [];
  filteredScheduledAppointments: any[] = [];
  scheduledSearch: string = '';

  labAppointments: any[] = [];
  filteredLabAppointments: any[] = [];
  labSearch: string = '';

  activeFaceToFaceTab: 'all' | 'upcoming' | 'past' | 'today' = 'all';
  activeIntantTab: 'all' | 'upcoming' | 'past' | 'today' = 'all';
  activeLabTab: 'all' | 'upcoming' | 'past' | 'today' = 'all';
  activeScheduledTab: 'all' | 'upcoming' | 'past' | 'today' = 'all';

  patientPopUpModal: boolean = false;
  patientPhoneConfirmation: boolean = false;
  isPatientConfirmationLoading: boolean = false;

  isSpecialistConfirmationLoading: boolean = false;
  specialistPopUpModal: boolean = false;
  specialistPhoneConfirmation: boolean = false;

  isHealthAdvisorConfirmationLoading: boolean = false;
  healthAdvisorPopUpModal: boolean = false;
  healthAdvisorPhoneConfirmation: boolean = false;

  selectedAppointment!: I_APPOINTMENT_FOR_CONFIRMATION;
  selectedSpecialist: any;

  openedAccordions: number[] = [];

  constructor() {
    window.scrollTo({ top: 0, behavior: 'smooth' });

    effect(() => {
      if (this.allAppointments.isSuccess() && this.allAppointments.data()) {
        this.faceToFaceAppointments = this.allAppointments
          .data()
          .filter(
            (apt: any) =>
              apt.appointmentService &&
              apt.appointmentService.includes('Face-to-Face')
          );

        this.instantAppointments = this.allAppointments
          .data()
          .filter(
            (apt: any) =>
              apt.appointmentService &&
              apt.appointmentService.includes('Instant')
          );

        this.scheduledAppointments = this.allAppointments
          .data()
          .filter(
            (apt: any) =>
              apt.appointmentService &&
              apt.appointmentService.includes('Scheduled')
          );

        this.labAppointments = this.allAppointments
          .data()
          .filter(
            (apt: any) =>
              apt.appointmentService &&
              apt.appointmentService.includes('Lab Tests')
          );

        this.filteredFaceToFaceAppointments = this.faceToFaceAppointments;
        this.filteredInstantAppointments = this.instantAppointments;
        this.filteredScheduledAppointments = this.scheduledAppointments;
        this.filteredLabAppointments = this.labAppointments;
      }
    });
  }

  // Quries
  allAppointments = injectQuery(() => ({
    queryKey: ['all-instant-appointments'],
    queryFn: async () => {
      const res: any =
        await this.util.patientService.getAllPatientAppointments();
      return res;
    },
    refetchOnWindowFocus: false,
  }));

  // Mutations
  createAppointmentConfirmationMutation = injectMutation(() => ({
    mutationFn: (payload: any) =>
      this.util.patientService.createAppointmentConfirmation(payload),
    onSuccess: (res: any) => {
      if (res && res.error) {
        this.util.swal.fire(
          'Error creating appointment confirmation',
          '',
          'error'
        );
        this.isPatientConfirmationLoading = false;
        this.isSpecialistConfirmationLoading = false;
        this.isHealthAdvisorConfirmationLoading = false;

        this.patientPopUpModal = false;
        this.specialistPopUpModal = false;
        this.healthAdvisorPopUpModal = false;
        return;
      }

      this.util.swal.fire(
        'Appointment confirmation created successfully',
        '',
        'success'
      );
      this.isPatientConfirmationLoading = false;
      this.isSpecialistConfirmationLoading = false;
      this.isHealthAdvisorConfirmationLoading = false;

      this.patientPopUpModal = false;
      this.specialistPopUpModal = false;
      this.healthAdvisorPopUpModal = false;
      this.allAppointments.refetch();
    },
    onError: (error: any) => {
      this.util.swal.fire(
        'Error creating appointment confirmation',
        '',
        'error'
      );
      this.isPatientConfirmationLoading = false;
      this.isSpecialistConfirmationLoading = false;
      this.isHealthAdvisorConfirmationLoading = false;

      this.patientPopUpModal = false;
      this.specialistPopUpModal = false;
      this.healthAdvisorPopUpModal = false;
    },
  }));

  updateAppointmentConfirmationMutation = injectMutation(() => ({
    mutationFn: (payload: any) =>
      this.util.patientService.updateAppointmentConfirmation(payload),
    onSuccess: (res: any) => {
      if (res && res.error) {
        this.util.swal.fire(
          'Error updating appointment confirmation',
          '',
          'error'
        );
        this.isPatientConfirmationLoading = false;
        this.isSpecialistConfirmationLoading = false;
        this.isHealthAdvisorConfirmationLoading = false;

        this.patientPopUpModal = false;
        this.specialistPopUpModal = false;
        this.healthAdvisorPopUpModal = false;
        return;
      }

      this.util.swal.fire(
        'Appointment confirmation updated successfully',
        '',
        'success'
      );
      this.isPatientConfirmationLoading = false;
      this.isSpecialistConfirmationLoading = false;
      this.isHealthAdvisorConfirmationLoading = false;

      this.patientPopUpModal = false;
      this.specialistPopUpModal = false;
      this.healthAdvisorPopUpModal = false;
      this.allAppointments.refetch();
    },
    onError: (error: any) => {
      this.util.swal.fire(
        'Error updating appointment confirmation',
        '',
        'error'
      );
      this.isPatientConfirmationLoading = false;
      this.isSpecialistConfirmationLoading = false;
      this.isHealthAdvisorConfirmationLoading = false;

      this.patientPopUpModal = false;
      this.specialistPopUpModal = false;
      this.healthAdvisorPopUpModal = false;
    },
  }));

  cancelAppointmentMutation = injectMutation(() => ({
    mutationFn: (payload: any) =>
      this.util.patientService.cancelAppointment(payload),
    onSuccess: (res: any) => {
      if (res && res.error) {
        this.util.swal.fire('Error cancelling appointment', '', 'error');
        return;
      }

      this.util.swal.fire('Appointment cancelled successfully', '', 'success');
      this.allAppointments.refetch();
    },
    onError: (error: any) => {
      this.util.swal.fire('Error cancelling appointment', '', 'error');
    },
  }));

  // Functions
  joinMeeting(meetLink: string) {
    // Open meeting link in new tab
    window.open(meetLink, '_blank');
  }

  setActiveTab(tab: 'instant' | 'scheduled' | 'face-to-face' | 'lab') {
    this.activeTab = tab;
  }

  toggleAccordion(index: number) {
    if (this.openedAccordions.includes(index)) {
      this.openedAccordions = this.openedAccordions.filter((i) => i !== index);
    } else {
      this.openedAccordions.push(index);
    }
  }

  filterFaceToFaceAppointments() {
    const appointments = this.faceToFaceAppointments || [];
    const searchTerm = this.faceToFaceSearch.toLowerCase();

    this.filteredFaceToFaceAppointments = appointments.filter((apt: any) => {
      // Only check top-level values (not nested objects/arrays)
      return Object.values(apt).some((val) => {
        if (val && typeof val === 'string') {
          return val.toLowerCase().includes(searchTerm);
        }
        return false;
      });
    });

    console.log(this.filteredFaceToFaceAppointments);
  }
  filterInstantAppointments() {
    const appointments = this.instantAppointments || [];
    const searchTerm = this.instantSearch.toLowerCase();

    this.filteredInstantAppointments = appointments.filter((apt: any) => {
      // Only check top-level values (not nested objects/arrays)
      return Object.values(apt).some((val) => {
        if (val && typeof val === 'string') {
          return val.toLowerCase().includes(searchTerm);
        }
        return false;
      });
    });

    console.log(this.filteredInstantAppointments);
  }
  filterScheduledAppointments() {
    const appointments = this.scheduledAppointments || [];
    const searchTerm = this.scheduledSearch.toLowerCase();

    this.filteredScheduledAppointments = appointments.filter((apt: any) => {
      // Only check top-level values (not nested objects/arrays)
      return Object.values(apt).some((val) => {
        if (val && typeof val === 'string') {
          return val.toLowerCase().includes(searchTerm);
        }
        return false;
      });
    });

    console.log(this.filteredScheduledAppointments);
  }
  filterLabAppointments() {
    const appointments = this.labAppointments || [];
    const searchTerm = this.labSearch.toLowerCase();

    this.filteredLabAppointments = appointments.filter((apt: any) => {
      // Only check top-level values (not nested objects/arrays)
      return Object.values(apt).some((val) => {
        if (val && typeof val === 'string') {
          return val.toLowerCase().includes(searchTerm);
        }
        return false;
      });
    });

    console.log(this.filteredLabAppointments, 'Lab');
  }

  private getAppointmentDateTime(apt: any): Date {
    return new Date(`${apt.appointmentDate}T${apt.appointmentTime}`);
  }

  getAllFaceToFaceAppointments() {
    this.activeFaceToFaceTab = 'all';
    const appointments = this.faceToFaceAppointments || [];
    this.filteredFaceToFaceAppointments = appointments;
    return appointments || [];
  }

  getAllInstantAppointments() {
    this.activeIntantTab = 'all';
    const appointments = this.instantAppointments || [];
    this.filteredInstantAppointments = appointments;
    return appointments || [];
  }

  getAllScheduledAppointments() {
    this.activeScheduledTab = 'all';
    const appointments = this.scheduledAppointments || [];
    this.filteredScheduledAppointments = appointments;
    return appointments || [];
  }

  getAllLabAppointments() {
    this.activeLabTab = 'all';
    const appointments = this.labAppointments || [];
    this.filteredLabAppointments = appointments;
    return appointments || [];
  }

  getUpcomingFaceToFaceAppointments() {
    this.activeFaceToFaceTab = 'upcoming';
    const appointments = this.faceToFaceAppointments || [];
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setHours(0, 0, 0, 0);
    tomorrow.setDate(tomorrow.getDate() + 1);
    this.filteredFaceToFaceAppointments = (appointments || []).filter(
      (apt: any) => {
        const aptDateTime = this.getAppointmentDateTime(apt);
        return aptDateTime > tomorrow;
      }
    );
  }

  getUpcomingInstantAppointments() {
    this.activeIntantTab = 'upcoming';
    const appointments = this.instantAppointments || [];
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setHours(0, 0, 0, 0);
    tomorrow.setDate(tomorrow.getDate() + 1);
    this.filteredInstantAppointments = (appointments || []).filter(
      (apt: any) => {
        const aptDateTime = this.getAppointmentDateTime(apt);
        return aptDateTime > tomorrow;
      }
    );
  }

  getUpcomingScheduledAppointments() {
    this.activeScheduledTab = 'upcoming';
    const appointments = this.scheduledAppointments || [];
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setHours(0, 0, 0, 0);
    tomorrow.setDate(tomorrow.getDate() + 1);
    this.filteredScheduledAppointments = (appointments || []).filter(
      (apt: any) => {
        const aptDateTime = this.getAppointmentDateTime(apt);
        return aptDateTime > tomorrow;
      }
    );
  }

  getUpcomingLabAppointments() {
    this.activeLabTab = 'upcoming';
    const appointments = this.labAppointments || [];
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setHours(0, 0, 0, 0);
    tomorrow.setDate(tomorrow.getDate() + 1);
    this.filteredLabAppointments = (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return aptDateTime > tomorrow;
    });
  }

  getPastFaceToFaceAppointments() {
    this.activeFaceToFaceTab = 'past';
    const appointments = this.faceToFaceAppointments || [];
    const now = new Date();
    this.filteredFaceToFaceAppointments = (appointments || []).filter(
      (apt: any) => {
        const aptDateTime = this.getAppointmentDateTime(apt);
        return aptDateTime < now;
      }
    );
  }

  getPastInstantAppointments() {
    this.activeIntantTab = 'past';
    const appointments = this.instantAppointments || [];
    const now = new Date();
    this.filteredInstantAppointments = (appointments || []).filter(
      (apt: any) => {
        const aptDateTime = this.getAppointmentDateTime(apt);
        return aptDateTime < now;
      }
    );
  }

  getPastScheduledAppointments() {
    this.activeScheduledTab = 'past';
    const appointments = this.scheduledAppointments || [];
    const now = new Date();
    this.filteredScheduledAppointments = (appointments || []).filter(
      (apt: any) => {
        const aptDateTime = this.getAppointmentDateTime(apt);
        return aptDateTime < now;
      }
    );
  }

  getPastLabAppointments() {
    this.activeLabTab = 'past';
    const appointments = this.labAppointments || [];
    const now = new Date();
    this.filteredLabAppointments = (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return aptDateTime < now;
    });
  }

  getTodayFaceToFaceAppointments() {
    this.activeFaceToFaceTab = 'today';
    const appointments = this.faceToFaceAppointments || [];
    const now = new Date();
    this.filteredFaceToFaceAppointments = (appointments || []).filter(
      (apt: any) => {
        const aptDateTime = this.getAppointmentDateTime(apt);

        return (
          aptDateTime.getFullYear() === now.getFullYear() &&
          aptDateTime.getMonth() === now.getMonth() &&
          aptDateTime.getDate() === now.getDate() &&
          aptDateTime > now
        );
      }
    );
  }

  getTodayInstantAppointments() {
    this.activeIntantTab = 'today';
    const appointments = this.instantAppointments || [];
    const now = new Date();
    this.filteredInstantAppointments = (appointments || []).filter(
      (apt: any) => {
        const aptDateTime = this.getAppointmentDateTime(apt);

        return (
          aptDateTime.getFullYear() === now.getFullYear() &&
          aptDateTime.getMonth() === now.getMonth() &&
          aptDateTime.getDate() === now.getDate() &&
          aptDateTime > now
        );
      }
    );
  }

  getTodayScheduledAppointments() {
    this.activeScheduledTab = 'today';
    const appointments = this.scheduledAppointments || [];
    const now = new Date();
    this.filteredScheduledAppointments = (appointments || []).filter(
      (apt: any) => {
        const aptDateTime = this.getAppointmentDateTime(apt);

        return (
          aptDateTime.getFullYear() === now.getFullYear() &&
          aptDateTime.getMonth() === now.getMonth() &&
          aptDateTime.getDate() === now.getDate() &&
          aptDateTime > now
        );
      }
    );
  }

  getTodayLabAppointments() {
    this.activeLabTab = 'today';
    const appointments = this.labAppointments || [];
    const now = new Date();
    this.filteredLabAppointments = (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);

      return (
        aptDateTime.getFullYear() === now.getFullYear() &&
        aptDateTime.getMonth() === now.getMonth() &&
        aptDateTime.getDate() === now.getDate() &&
        aptDateTime > now
      );
    });
  }

  getUpcomingF2FAppointmentsLength() {
    const appointments = this.faceToFaceAppointments || [];
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setHours(0, 0, 0, 0);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return aptDateTime > tomorrow;
    }).length;
  }

  getUpcomingInstantAppointmentsLength() {
    const appointments = this.instantAppointments || [];
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setHours(0, 0, 0, 0);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return aptDateTime > tomorrow;
    }).length;
  }

  getUpcomingScheduledAppointmentsLength() {
    const appointments = this.scheduledAppointments || [];
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setHours(0, 0, 0, 0);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return aptDateTime > tomorrow;
    }).length;
  }

  getUpcomingLabAppointmentsLength() {
    const appointments = this.labAppointments || [];
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setHours(0, 0, 0, 0);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return aptDateTime > tomorrow;
    }).length;
  }

  getPastF2FAppointmentsLength() {
    const appointments = this.faceToFaceAppointments || [];
    const now = new Date();
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return aptDateTime < now;
    }).length;
  }

  getPastInstantAppointmentsLength() {
    const appointments = this.instantAppointments || [];
    const now = new Date();
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return aptDateTime < now;
    }).length;
  }

  getPastScheduledAppointmentsLength() {
    const appointments = this.scheduledAppointments || [];
    const now = new Date();
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return aptDateTime < now;
    }).length;
  }

  getPastLabAppointmentsLength() {
    const appointments = this.labAppointments || [];
    const now = new Date();
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return aptDateTime < now;
    }).length;
  }

  getTodayF2FAppointmentsLength() {
    const appointments = this.faceToFaceAppointments || [];
    const now = new Date();
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return (
        aptDateTime.getFullYear() === now.getFullYear() &&
        aptDateTime.getMonth() === now.getMonth() &&
        aptDateTime.getDate() === now.getDate() &&
        aptDateTime > now
      );
    }).length;
  }

  getTodayInstantAppointmentsLength() {
    const appointments = this.instantAppointments || [];
    const now = new Date();
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return (
        aptDateTime.getFullYear() === now.getFullYear() &&
        aptDateTime.getMonth() === now.getMonth() &&
        aptDateTime.getDate() === now.getDate() &&
        aptDateTime > now
      );
    }).length;
  }

  getTodayScheduledAppointmentsLength() {
    const appointments = this.scheduledAppointments || [];
    const now = new Date();
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return (
        aptDateTime.getFullYear() === now.getFullYear() &&
        aptDateTime.getMonth() === now.getMonth() &&
        aptDateTime.getDate() === now.getDate() &&
        aptDateTime > now
      );
    }).length;
  }

  getTodayLabAppointmentsLength() {
    const appointments = this.labAppointments || [];
    const now = new Date();
    return (appointments || []).filter((apt: any) => {
      const aptDateTime = this.getAppointmentDateTime(apt);
      return (
        aptDateTime.getFullYear() === now.getFullYear() &&
        aptDateTime.getMonth() === now.getMonth() &&
        aptDateTime.getDate() === now.getDate() &&
        aptDateTime > now
      );
    }).length;
  }

  getStatusColor(status: string): string {
    switch (status?.toLowerCase()) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'danger';
      case 'rejected':
        return 'danger';
      case 'rescheduled':
        return 'warning';
      case 'booked':
        return 'primary';
      default:
        return 'secondary';
    }
  }

  getConfirmationIconClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'confirmed':
        return 'bg-success bg-opacity-10';
      case 'pending':
        return 'bg-warning bg-opacity-10';
      case 'rejected':
        return 'bg-danger bg-opacity-10';
      default:
        return 'bg-secondary bg-opacity-10';
    }
  }

  callCorrectUserConfirmation(
    appointment: I_APPOINTMENT_FOR_CONFIRMATION,
    userRole: string
  ) {
    let specialist = appointment.specialist.find((s: any) =>
      s.specialistRole.includes(userRole)
    );

    if (
      !userRole.includes('Patient') &&
      !userRole.includes('Health Advisor') &&
      !userRole.includes('Doctor')
    ) {
      specialist = appointment.specialist.find(
        (s: any) =>
          !s.specialistRole.includes('Patient') &&
          !s.specialistRole.includes('Super Admin') &&
          !s.specialistRole.includes('Health Advisor')
      );
    }

    console.log('Appointment : ', appointment);
    console.log('Specialist : ', specialist);
    console.log('User Role : ', userRole);

    if (userRole.includes('Patient')) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      this.patientPopUpModal = true;
      this.selectedSpecialist = specialist;
      this.selectedAppointment = appointment;
      return;
    }
    if (userRole.includes('Health Advisor')) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      this.healthAdvisorPopUpModal = true;
      this.selectedSpecialist = specialist;
      this.selectedAppointment = appointment;
      return;
    }
    if (!userRole.includes('Health Advisor') && !userRole.includes('Patient')) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      this.specialistPopUpModal = true;
      this.selectedSpecialist = specialist;
      this.selectedAppointment = appointment;
      return;
    }
  }

  onPatientConfirmation(
    specialist: any,
    appointment: I_APPOINTMENT_FOR_CONFIRMATION
  ) {
    this.isPatientConfirmationLoading = true;
    console.log('Patient Confirmation:', specialist, appointment);
    const payload = {
      phoneConfirmation: this.patientPhoneConfirmation,
      confirmationId: specialist.confirmationId,
      status: 'Confirmed',
      confirmedBy: this.user.userId + ' - ' + this.user.roleDescription,
      confirmedDate: new Date().toISOString(),
    };
    console.log('Payload : ', payload);
    this.updateAppointmentConfirmationMutation.mutate(payload);
    this.isPatientConfirmationLoading = false;
  }

  onSpecialistConfirmation(
    specialist: any,
    appointment: I_APPOINTMENT_FOR_CONFIRMATION
  ) {
    this.isSpecialistConfirmationLoading = true;
    console.log('Specialist Confirmation:', specialist, appointment);
    const payload = {
      phoneConfirmation: this.specialistPhoneConfirmation,
      confirmationId: specialist.confirmationId,
      status: 'Confirmed',
      confirmedBy: this.user.userId + ' - ' + this.user.roleDescription,
      confirmedDate: new Date().toISOString(),
    };
    console.log('Payload : ', payload);
    this.updateAppointmentConfirmationMutation.mutate(payload);
    this.isSpecialistConfirmationLoading = false;
  }

  onHealthAdvisorConfirmation(
    specialist: any,
    appointment: I_APPOINTMENT_FOR_CONFIRMATION
  ) {
    this.isHealthAdvisorConfirmationLoading = true;
    console.log('Health Advisor Confirmation:', specialist, appointment);
    const payload = {
      phoneConfirmation: this.healthAdvisorPhoneConfirmation,
      confirmationId: specialist.confirmationId,
      status: 'Confirmed',
      confirmedBy: this.user.userId + ' - ' + this.user.roleDescription,
      confirmedDate: new Date().toISOString(),
    };
    console.log('Payload : ', payload);
    this.updateAppointmentConfirmationMutation.mutate(payload);
    this.isHealthAdvisorConfirmationLoading = false;
  }

  onHealthAdvisorConfirmationWithoutSpecialist(
    appointment: I_APPOINTMENT_FOR_CONFIRMATION
  ) {
    this.util.swal
      .fire({
        title: 'Are you sure?',
        text: 'You are about to cancel this appointment',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, confirm it!',
      })
      .then(async (result) => {
        if (result.isConfirmed) {
          // Cancel appointment
          console.log('Confirm appointment:', appointment);

          const HealthAdvisorConfirmationPayload = {
            appointmentId: appointment.appointmentId,
            patientId: appointment.patientId,
            specialistId: +this.user.userId,
            specialistRole: 'Health Advisor',
            confirmedDate: new Date().toISOString(),
            confirmedBy: this.user.userId + ' - ' + this.user.roleDescription,
            status: 'Confirmed',
            notes: '',
          };

          console.log('Payload : ', HealthAdvisorConfirmationPayload);

          this.createAppointmentConfirmationMutation.mutate(
            HealthAdvisorConfirmationPayload
          );
        } else {
          return;
        }
      });
  }

  onHealthAdvisorCancelAppointment(
    appointment: I_APPOINTMENT_FOR_CONFIRMATION
  ) {
    this.util.swal
      .fire({
        title: 'Are you sure?',
        text: 'You are about to cancel this appointment',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, cancel it!',
      })
      .then(async (result) => {
        if (result.isConfirmed) {
          // Cancel appointment
          console.log('Cancel appointment:', appointment);
          const payload = {
            appointmentId: appointment.appointmentId,
          };

          this.cancelAppointmentMutation.mutate(payload);
        } else {
          return;
        }
      });
  }

  getSpecialistConfirmationStatus(appointment: any) {
    if (appointment.specialist) {
      const docConfirmation = appointment.specialist.find(
        (s: any) =>
          !s.specialistRole.includes('Patient') &&
          !s.specialistRole.includes('Super Admin') &&
          !s.specialistRole.includes('Health Advisor')
      );

      return docConfirmation?.confirmationStatus.includes('Confirmed');
    }
    return false;
  }

  getPatientConfirmationStatus(appointment: any) {
    if (appointment.specialist) {
      const docConfirmation = appointment.specialist.find((s: any) =>
        s.specialistRole.includes('Patient')
      );

      return docConfirmation?.confirmationStatus.includes('Confirmed');
    }
    return false;
  }

  getHealthAdvisorConfirmationStatus(appointment: any) {
    if (appointment.specialist) {
      const docConfirmation = appointment.specialist.find(
        (s: any) =>
          s.specialistRole.includes('Health Advisor') ||
          s.specialistRole.includes('Super Admin')
      );

      return docConfirmation?.confirmationStatus.includes('Confirmed');
    }
    return false;
  }

  getHealthAdvisorCancelStatus(appointment: any) {
    if (appointment.specialist) {
      const docConfirmation = appointment.specialist.find(
        (s: any) =>
          s.specialistRole.includes('Health Advisor') ||
          s.specialistRole.includes('Super Admin')
      );

      return docConfirmation?.confirmationStatus.includes('Cancelled');
    }
    return false;
  }

  getAppointmentConfirmationsLength(appointment: any) {
    if (appointment.specialist && appointment.specialist.length) {
      return appointment.specialist.length;
    }

    return 0;
  }

  onRescheduleAppointment(appointment: any) {
    console.log('Reschedule Appointment:', appointment);

    const specialistConfirmation = appointment.specialist.find(
      (s: any) =>
        !s.specialistRole.includes('Patient') &&
        !s.specialistRole.includes('Super Admin') &&
        !s.specialistRole.includes('Health Advisor')
    );

    this.util.router.navigate(
      [
        '/health-advisor/manage-appointments/reschedule',
        this.util.encrypt(appointment.appointmentId),
        this.util.encrypt(appointment.timeSlotId),
        this.util.encrypt(appointment.appointmentDate),
        this.util.encrypt(appointment.appointmentType),
        this.util.encrypt(appointment.doctorId),
        this.util.encrypt(appointment.departmentId),
        this.util.encrypt(appointment.facilityId),
        this.util.encrypt(appointment.patientId),
      ],
      {
        queryParams: {
          user: specialistConfirmation.specialistRole,
        },
      }
    );
  }

  isCancelledAppointment(app: any) {
    return app.appointmentStatus.includes('Cancelled');
  }

  getSpecialistRole(appointment: any) {
    if (appointment.specialist) {
      const docConfirmation = appointment.specialist.find(
        (s: any) =>
          !s.specialistRole.includes('Patient') &&
          !s.specialistRole.includes('Super Admin') &&
          !s.specialistRole.includes('Health Advisor')
      );

      return docConfirmation?.specialistRole;
    }
    return '';
  }

  viewVisit(appointment: any) {
    console.log('Specialist Role:', this.getSpecialistRole(appointment));
    console.log('Appointment:', appointment);
    if (this.getSpecialistRole(appointment).includes('Dietitian')) {
      console.log('View visit Dietician:', appointment);
      this.getDietFormRoutes(appointment);
    }
    if (this.getSpecialistRole(appointment).includes('Physiotherapy')) {
      console.log('View visit Physiotherapist:', appointment);
      this.getPhysioFormRoutes(appointment);
    }
    if (this.getSpecialistRole(appointment).includes('Doctor')) {
      console.log('View visit Doctor:', appointment);
      this.getVisitFormRoutes(appointment);
    }
    if (this.getSpecialistRole(appointment).includes('Phlebotomist')) {
      console.log('View visit Phlebotomist:', appointment);
      this.getPhlebotomistFormRoutes(appointment);
    }
  }

  getDietFormRoutes(appointment: any) {
    // this.util.swal.fire('Coming Soon', 'Diet Form', 'info');
    if (this.user.roleName.includes(this.util.ROLES.PATIENT)) {
      this.util.router.navigate([
        '/patient/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
      if (
        appointment.appointmentStatus.includes('Cancelled') ||
        appointment.appointmentStatus.includes('Completed')
      ) {
        this.util.router.navigate([
          '/health-advisor/view/dietician-assesment-form/',
          this.util.adminService.encrypt(appointment.patientId),
          this.util.adminService.encrypt(appointment.dietConsultationId),
        ]);
      } else {
        this.util.swal.fire('Coming Soon', 'Diet Form', 'info');
        return;
      }
    }

    if (this.user.roleName.includes(this.util.ROLES.ADMIN)) {
      this.util.router.navigate([
        '/admin/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DIETITIAN)) {
      this.util.router.navigate([
        '/dietitian/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DOCTOR)) {
      this.util.router.navigate([
        '/doctor/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.NURSE)) {
      this.util.router.navigate([
        '/nurse/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.PHYSIOTHERAPIST)) {
      this.util.router.navigate([
        '/physio-rehab-coordinator/view/dietician-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.dietConsultationId),
      ]);
    }
  }

  getPhysioFormRoutes(appointment: any) {
    // this.util.swal.fire('Coming Soon', 'Physio Form', 'info');
    if (this.user.roleName.includes(this.util.ROLES.PATIENT)) {
      this.util.router.navigate([
        '/patient/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
      if (
        appointment.appointmentStatus.includes('Cancelled') ||
        appointment.appointmentStatus.includes('Completed')
      ) {
        this.util.router.navigate([
          '/health-advisor/view/rehab-assesment-form/',
          this.util.adminService.encrypt(appointment.patientId),
          this.util.adminService.encrypt(appointment.physioConsultationId),
        ]);
      } else {
        this.util.swal.fire('Coming Soon', 'Physio Form', 'info');
        return;
      }
    }

    if (this.user.roleName.includes(this.util.ROLES.ADMIN)) {
      this.util.router.navigate([
        '/admin/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DOCTOR)) {
      this.util.router.navigate([
        '/doctor/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.NURSE)) {
      this.util.router.navigate([
        '/nurse/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DIETITIAN)) {
      this.util.router.navigate([
        '/dietitian/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.PHYSIOTHERAPIST)) {
      this.util.router.navigate([
        '/physio-rehab-coordinator/view/rehab-assesment-form/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.physioConsultationId),
      ]);
    }
  }

  getVisitFormRoutes(appointment: any) {
    if (this.user.roleName.includes(this.util.ROLES.PATIENT)) {
      this.util.router.navigate([
        '/patient/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
      if (
        appointment.appointmentStatus.includes('Cancelled') ||
        appointment.appointmentStatus.includes('Completed')
      ) {
        this.util.router.navigate([
          '/health-advisor/view/doc-visit-details/',
          this.util.adminService.encrypt(appointment.patientId),
          this.util.adminService.encrypt(appointment.visitId),
        ]);
      } else {
        this.util.router.navigate([
          '/health-advisor/create/doc-visit-details/',
          this.util.adminService.encrypt(appointment.patientId),
          this.util.adminService.encrypt(appointment.visitId),
        ]);
        return;
      }
    }

    if (this.user.roleName.includes(this.util.ROLES.ADMIN)) {
      this.util.router.navigate([
        '/admin/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DOCTOR)) {
      this.util.router.navigate([
        '/doctor/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.NURSE)) {
      this.util.router.navigate([
        '/nurse/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.DIETITIAN)) {
      this.util.router.navigate([
        '/dietitian/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.PHYSIOTHERAPIST)) {
      this.util.router.navigate([
        '/physio-rehab-coordinator/view/doc-visit-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.visitId),
      ]);
    }
  }

  getPhlebotomistFormRoutes(appointment: any) {
    if (this.user.roleName.includes(this.util.ROLES.PATIENT)) {
      this.util.router.navigate([
        '/patient/view/phlebotomist-app-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.appointmentId),
      ]);
    }

    if (this.user.roleName.includes(this.util.ROLES.HEALTH_ADVISOR)) {
      this.util.router.navigate([
        '/health-advisor/view/phlebotomist-app-details/',
        this.util.adminService.encrypt(appointment.patientId),
        this.util.adminService.encrypt(appointment.appointmentId),
      ]);
    }
  }
}
