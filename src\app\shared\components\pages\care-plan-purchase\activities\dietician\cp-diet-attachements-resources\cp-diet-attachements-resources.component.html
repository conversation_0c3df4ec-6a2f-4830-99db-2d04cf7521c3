<form [formGroup]="attachmentForm">
  <!-- Attachments & Resources -->
  <div
    class="card mb-4 shadow-sm border-0 rounded-3"
    style="background-color: whitesmoke"
  >
    <div
      class="card-header border-0 pb-3 d-flex justify-content-between align-items-center"
    >
      <h5 class="card-title mb-0 fw-bold text-primary">
        <i class="bi bi-paperclip me-2"></i> Attachments & Resources
      </h5>
      <div>
        <button
          type="button"
          class="btn btn-sm btn-primary mt-2 px-3 shadow-sm"
          (click)="upload()"
          [disabled]="isCompleted"
        >
          <i class="bi bi-cloud-arrow-up me-1"></i>
          {{ attachmentData !== null ? "Update" : "Save" }}
        </button>
      </div>
    </div>

    <div class="card-body">
      <div class="row g-4">
        <!-- Category -->
        <div class="col-md-6">
          <kt-select-search
            [items]="[
              { name: 'Meal Plan', value: 'Meal Plan' },
              { name: 'Handout', value: 'Handout' },
              { name: 'Recipe Sheet', value: 'Recipe Sheet' },
              { name: 'Image', value: 'Image' },
              { name: 'Diet Summary', value: 'Diet Summary' },
              { name: 'Booklet', value: 'Booklet' },
              { name: 'Other', value: 'Other' }
            ]"
            label="Category"
            placeholder="Select Category"
            [formControl]="asFormControl(attachmentForm.get('category'))"
            [required]="false"
          />
        </div>

        <!-- Source -->
        <div class="col-md-6">
          <kt-select-search
            [items]="[
              { name: 'Upload', value: 'Upload' },
              { name: 'Generated', value: 'Generated' }
            ]"
            label="Source"
            placeholder="Select Source"
            [formControl]="asFormControl(attachmentForm.get('source'))"
            [required]="false"
          />
        </div>

        <!-- Shared Via WhatsApp -->
        <div class="col-md-6">
          <label class="form-label fw-semibold">
            <i class="bi bi-whatsapp text-success me-1"></i> Shared Via WhatsApp
          </label>
          <input
            type="number"
            class="form-control form-control-sm shadow-sm"
            [formControl]="shareViaWhatsapp"
            placeholder="Shared Via WhatsApp"
          />
        </div>

        <!-- Shared Via E-Mail -->
        <div class="col-md-6">
          <label class="form-label fw-semibold">
            <i class="bi bi-envelope-fill text-danger me-1"></i> Shared Via
            E-Mail
          </label>
          <input
            type="text"
            class="form-control form-control-sm shadow-sm"
            [formControl]="shareViaEmail"
            placeholder="Shared Via E-Mail"
          />
        </div>

        <!-- File Type -->
        <div class="col-md-6">
          <label class="form-label fw-semibold">
            <i class="bi bi-file-earmark-text me-1"></i> File Type
          </label>
          <input
            type="text"
            class="form-control form-control-sm shadow-sm"
            [formControl]="fileType"
            placeholder="File Type (e.g., PDF, Image)"
          />
        </div>

        <!-- File Upload -->
        <div class="col-md-6">
          <label class="form-label fw-semibold">
            <i class="bi bi-upload me-1"></i> Upload Files
          </label>
          <input
            type="file"
            class="form-control form-control-sm shadow-sm"
            (change)="onFileSelected($event)"
          />
          <small class="form-text text-muted d-block mt-1">
            Upload meal plans, recipe booklets, or images
          </small>
        </div>
      </div>

      <!-- File Preview -->
      <div
        *ngIf="uploadedFile || fileName"
        class="alert alert-light border mt-4 p-2 d-flex align-items-center justify-content-between shadow-sm"
      >
        <span class="fw-medium">
          <i class="bi bi-file-earmark-check text-success me-2"></i>
          {{ uploadedFile ? uploadedFile.name : fileName }}
        </span>
        <a
          href="javascript:void(0)"
          class="text-danger fw-semibold small text-decoration-none"
          (click)="removeImage()"
        >
          <i class="bi bi-x-circle me-1"></i> Remove
        </a>
      </div>
    </div>
  </div>
</form>
