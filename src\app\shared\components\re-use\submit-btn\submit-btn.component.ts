import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'SubmitBtn',
  imports: [CommonModule],
  templateUrl: './submit-btn.component.html',
  styleUrl: './submit-btn.component.scss',
})
export class SubmitBtnComponent {
  @Input() label: string = 'Submit';
  @Input() loadingLabel: string = 'Submitting...';
  @Input() isSubmitting: boolean = false;
  @Input() type: string = 'submit';
  @Input() icon!: string;
  @Input() color:
    | 'primary'
    | 'secondary'
    | 'success'
    | 'danger'
    | 'warning'
    | 'info' = 'primary';

  // @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() radious: string = 'rounded';

  @Output() click = new EventEmitter<any>();

  onClick(event: any) {
    this.click.emit(event);
  }
}
