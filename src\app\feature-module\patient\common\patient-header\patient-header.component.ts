import { Component, HostListener, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AdminService } from 'src/app/admin/service/admin.service';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { CommonService } from 'src/app/shared/common/common.service';
import { DataService } from 'src/app/shared/data/data.service';
import { header } from 'src/app/shared/models/sidebar-model';
import { routes } from 'src/app/shared/routes/routes';
import { UserAccountsService } from 'src/app/shared/services/user-accounts.service';
import { SidebarService } from 'src/app/shared/sidebar/sidebar.service';

@Component({
  selector: 'app-patient-header',
  standalone: false,
  templateUrl: './patient-header.component.html',
  styleUrl: './patient-header.component.scss',
})
export class PatientHeaderComponent implements OnInit {
  userData: any | null = null;
  patientId: string = '';
  user: any;

  // UI State
  isSearch = false;
  isDark = false;
  isLight = true;
  isSidebarOpen = false;
  isMobile = false;

  // Notifications
  notifications = [
    {
      id: 1,
      avatar: 'assets/img/clients/client-01.jpg',
      name: 'Travis Tremble',
      time: '18.30 PM',
      message: 'Sent a amount of $210 for his Appointment',
      doctor: 'Dr.Ruby perin',
      isRead: false,
    },
    {
      id: 2,
      avatar: 'assets/img/clients/client-02.jpg',
      name: 'Travis Tremble',
      time: '12 Min Ago',
      message: 'has booked her appointment to',
      doctor: 'Dr. Hendry Watt',
      isRead: false,
    },
    {
      id: 3,
      avatar: 'assets/img/clients/client-03.jpg',
      name: 'Travis Tremble',
      time: '6 Min Ago',
      message: 'Sent a amount $210 for his Appointment',
      doctor: 'Dr.Maria Dyen',
      isRead: true,
    },
    {
      id: 4,
      avatar: 'assets/img/clients/client-04.jpg',
      name: 'Travis Tremble',
      time: '8.30 AM',
      message: 'Send a message to his doctor',
      doctor: '',
      isRead: true,
    },
  ];

  constructor(
    private common: CommonService,
    public sidebar: SidebarService,
    private router: Router,
    private authService: AuthService,
    private userAccountService: UserAccountsService,
    private toastr: ToastrService,
    private adminService: AdminService
  ) {
    this.userData = this.authService.getDataFromSession('user');
    if (this.userData && this.userData['userId']) {
      this.patientId = this.userData['userId'];
      this.getPatientDetailsById(this.userData['userId']);
    }
  }

  ngOnInit(): void {
    this.checkScreenSize();
    this.initializeTheme();
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenSize();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: any) {
    // Close sidebar when clicking outside on mobile
    if (this.isMobile && this.isSidebarOpen) {
      const sidebar = document.querySelector('.mobile-sidebar');
      const toggle = document.querySelector('.mobile-toggle');

      if (
        sidebar &&
        toggle &&
        !sidebar.contains(event.target) &&
        !toggle.contains(event.target)
      ) {
        this.closeSidebar();
      }
    }
  }

  goToCarePlansPage() {
    this.router.navigate([
      '/patient/cp/',
      this.adminService.encrypt(this.patientId),
      'purchased-care-plans',
    ]);
    this.closeSideMenu();
  }

  checkScreenSize() {
    this.isMobile = window.innerWidth < 768; // md breakpoint
    if (!this.isMobile) {
      this.isSidebarOpen = false;
    }
  }

  initializeTheme() {
    const savedTheme = localStorage.getItem('themeColor') || 'light-mode';
    this.isDark = savedTheme === 'dark-mode';
    this.isLight = !this.isDark;
    this.applyTheme();
  }

  toggleSidebar() {
    if (this.isMobile) {
      this.isSidebarOpen = !this.isSidebarOpen;
    }
  }

  closeSidebar() {
    this.isSidebarOpen = false;
  }

  toggleTheme() {
    this.isDark = !this.isDark;
    this.isLight = !this.isLight;
    const themeColor = this.isDark ? 'dark-mode' : 'light-mode';
    localStorage.setItem('themeColor', themeColor);
    this.sidebar.changeThemeColor(themeColor);
    this.applyTheme();
  }

  applyTheme() {
    const body = document.body;
    if (this.isDark) {
      body.classList.add('dark-mode');
      body.classList.remove('light-mode');
    } else {
      body.classList.add('light-mode');
      body.classList.remove('dark-mode');
    }
  }

  toggleSearch() {
    this.isSearch = !this.isSearch;
  }

  navigateToSearch() {
    this.router.navigate(['/search-doctor/search1']);
  }

  navigateToRoute(route: string) {
    this.router.navigate([route]);
    if (this.isMobile) {
      this.closeSidebar();
    }
  }

  closeSideMenu() {
    if (this.isMobile) {
      this.closeSidebar();
    }
  }

  redirectToFacility() {
    if (this.userData && this.userData.facilityId) {
      this.router.navigate([
        `/patient/follow-up/facility/${this.userData.facilityId}/doctors`,
      ]);
    }
    if (this.isMobile) {
      this.closeSidebar();
    }
  }

  async getPatientDetailsById(patientId: string) {
    if (!patientId) {
      this.toastr.error('Patient Id Invalid', '', { timeOut: 3000 });
      return;
    }

    try {
      const patientData: any = await this.userAccountService.getPatientById(
        patientId
      );
      if (patientData.length > 0) {
        this.user = patientData[0];

        if (patientData[0].photoLink) {
          const pathname = this.getNormalizedPathname(patientData[0].photoLink);
          const securedUrl = await this.fetchSecuredUrl(pathname);
          this.user.photoLinkUrl = securedUrl;
        }
      } else {
        this.toastr.error('No patient data found', '', { timeOut: 3000 });
      }
    } catch (error) {
      console.error('Error fetching patient details:', error);
      this.toastr.error('Error fetching patient details', '', {
        timeOut: 3000,
      });
    }
  }

  async fetchSecuredUrl(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.userAccountService.getSecureImgUrl({ fileUrl: url }).subscribe({
        next: (response: any) => {
          resolve(response['url']);
        },
        error: (error: any) => {
          this.toastr.error('', error.error.message, { timeOut: 3000 });
          reject(error);
        },
      });
    });
  }

  private getNormalizedPathname(filePath: string): string {
    if (!filePath) {
      console.error('Invalid file path:', filePath);
      return '';
    }

    try {
      const url = new URL(filePath, window.location.origin);
      return url.pathname.replace(/^\/+/, '');
    } catch (error) {
      console.error('Error parsing URL:', error, 'File Path:', filePath);
      return '';
    }
  }

  markNotificationAsRead(notificationId: number) {
    const notification = this.notifications.find(
      (n) => n.id === notificationId
    );
    if (notification) {
      notification.isRead = true;
    }
  }

  getUnreadNotificationsCount(): number {
    return this.notifications.filter((n) => !n.isRead).length;
  }

  logout() {
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/login']);
    this.toastr.success('Logged out successfully!');
  }
}
