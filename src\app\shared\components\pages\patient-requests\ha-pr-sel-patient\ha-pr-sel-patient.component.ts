import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { AdminFriendlyPageWrapperComponent } from '../../../re-use/admin-friendly-page-wrapper/admin-friendly-page-wrapper.component';
import { LoadingSpinnerComponent } from 'src/app/shared/loading-spinner/loading-spinner.component';
import { ErrorAlertComponent } from 'src/app/shared/error-alert/error-alert.component';
import { PageWithSearchPaginationComponent } from 'src/app/shared/page-with-search-pagination/page-with-search-pagination.component';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { AuthService } from 'src/app/shared/auth/auth.service';
import { AdminService } from 'src/app/admin/service/admin.service';
import { HealthAdvisorService } from 'src/app/feature-module/health-advisor/service/health-advisor.service';
import { UtilFunctions } from 'src/app/shared/utils/common-functions';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-ha-pr-sel-patient',
  imports: [
    CommonModule,
    AdminFriendlyPageWrapperComponent,
    LoadingSpinnerComponent,
    ErrorAlertComponent,
    PageWithSearchPaginationComponent,
  ],
  templateUrl: './ha-pr-sel-patient.component.html',
  styleUrl: './ha-pr-sel-patient.component.scss',
})
export class HaPrSelPatientComponent {
  authService = inject(AuthService);
  adminService = inject(AdminService);
  haService = inject(HealthAdvisorService);
  util = inject(UtilFunctions);
  router = inject(Router);
  activeRoute = inject(ActivatedRoute);

  user = this.authService.getDataFromSession('user');

  filteredPatients = signal<any[]>([]);

  constructor() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Quries
  patients = injectQuery(() => ({
    queryKey: ['all-patients'],
    queryFn: async () => {
      const res: any = await this.haService.getAllInternalPatients();

      await Promise.all(
        res.map(async (user: any) => {
          if (user.image) {
            const pathname = this.util.getNormalizedPathname(user.image);
            const securedUrl = await this.util.fetchSecuredUrl(pathname);
            user.image = securedUrl;
          }
          return user;
        })
      );

      return res
        .filter((user: any) =>
          user.profileName.includes(this.util.ROLES.PATIENT)
        )
        .map((data: any) => {
          return {
            ...data,
            userId: data.patientId,
          };
        });
    },

    refetchOnWindowFocus: false,
  }));

  // Mutations

  // Functions

  onPatientClick = (patient: any) => {
    this.router.navigate([
      '/health-advisor/patient-requests/',
      this.adminService.encrypt(patient.userId),
    ]);
  };
}
