import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { BehaviorSubject, firstValueFrom, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class PatientService {
  private apiUrl: string = environment.apiUrl;

  constructor(private http: HttpClient) {}
  // Get
  getPatHealthAdvisorByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/gethealthadvisorbypatientid`;
    return firstValueFrom(this.http.post(endPoint, { patientId }));
  }

  getLabTestOrderByVisitId(visitId: any) {
    const endPoint = this.apiUrl + `patients/getlabtestordersbyvisitid`;
    return this.http.post(endPoint, { visitId });
  }

  getVisitDetailsByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getvisitdetailbypatientid`;
    return this.http.post(endPoint, { patientId });
  }

  getRecentPatients7Days15Days30Days() {
    const endPoint = this.apiUrl + `patients/getrecentpatientsfor7or15or30days`;
    return firstValueFrom(this.http.get(endPoint));
  }

  getRecentPatients7Days15Days30DaysByDocId(docId: any, facilityId: any) {
    const endPoint = this.apiUrl + `patients/getrecentpatientsfor30days`;
    return firstValueFrom(
      this.http.post(endPoint, { doctorId: docId, facilityId: facilityId })
    );
  }

  getAllInvestigationTypes() {
    const endPoint = this.apiUrl + `patients/getallinvestigationtypes`;
    return firstValueFrom(this.http.get(endPoint));
  }

  getAllVaccines() {
    const endPoint = this.apiUrl + `patients/getallvaccines`;
    return firstValueFrom(this.http.get(endPoint));
  }

  getPatLifeStyleSocialHistoryByPatientId(patientId: any) {
    const endPoint =
      this.apiUrl + `patients/getpatlifestyleandsocialhisbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getAllDocsByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getalldocumentlistbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getAllSharedDocsByPatientId() {
    const endPoint = this.apiUrl + `patients/getallshareddocumentlist`;
    return this.http.get(endPoint);
  }

  uploadDocument(payload: any) {
    const endPoint = this.apiUrl + `patients/uploaddocumentfile`;
    return this.http.post(endPoint, payload);
  }

  shareDocument(payload: any) {
    const endPoint = this.apiUrl + `patients/sharedocumentfile`;
    return this.http.post(endPoint, payload);
  }

  getRecentVitalsHealthRecordByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getrecentvitalshealthrecord`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getVitalSignsListByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatvitalsignbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getClinicalVisitByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatclinicalvisitsbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getClinicalVisitByPatientIdForQuery(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatclinicalvisitsbypatientid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { patientId: patientId })
    );
  }

  getComplaintsByComplaintId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatcheifcomplaintsbyid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getComplaintsByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatcheifcomplaintsbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getDiagnosisByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getdiagnosisbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getAllPatientsByDocId(doctorId: any, facilityId: any) {
    const endPoint =
      this.apiUrl + `patients/getallpatientappointmentbydoctorid`;
    return this.http.post<any>(endPoint, {
      doctorId: doctorId,
      facilityId: facilityId,
    });
  }

  getPatVitalsByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatvitalsignbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatPresentIllnessById(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpathispresentillnessbyid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatPresentIllnessByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatpresillnessbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatSurgicalHistByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatsurgicalhistorybypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatHospitalHistByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpathoshistorybypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatFamilyHistByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatfamilyhistorybypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatAllergyByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatallergiesbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatClinicalNoteByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatclinicalnotesbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatInvestigationsByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatinvestigationbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatPrescriptionByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatprescriptionbypatientid`;
    return this.http.post<any>(endPoint, { patientId: patientId });
  }

  getAllDietician() {
    const endPoint = this.apiUrl + `activity/getalldietitian`;
    return firstValueFrom(this.http.get(endPoint));
  }

  getDieticianByVisitId(visitId: any) {
    const endPoint = this.apiUrl + `activity/getdietitianbyvisitid`;
    return firstValueFrom(this.http.post(endPoint, { visitId: visitId }));
  }

  getAllRehanForm() {
    const endPoint = this.apiUrl + `activity/geallrehabforms`;
    return firstValueFrom(this.http.get(endPoint));
  }

  getRehabByVisitId(visitId: any) {
    const endPoint = this.apiUrl + `activity/getrehabbyvisitid`;
    return firstValueFrom(this.http.post(endPoint, { visitId: visitId }));
  }

  getPatRefillListByPrescriptionId(prescriptionId: any) {
    const endPoint = this.apiUrl + `patients/getpatrefillrequestbyprescid`;
    return this.http.post(endPoint, { prescriptionId });
  }

  getPatientDetailsById(patientId: any) {
    const endPoint = this.apiUrl + `patients/getprofdappointdetailbypatientid`;
    return firstValueFrom(this.http.post(endPoint, { patientId: patientId }));
  }

  // Super Admin Prescriptions
  getAllPrescriptions() {
    const endpoint = `${this.apiUrl}patients/getallprescriptionlist`;
    return firstValueFrom(this.http.get(endpoint));
  }

  getPrescriptionByFacilityId(facilityId: any) {
    const endpoint = `${this.apiUrl}doctors/getrefillrequestbyfacilityid`;
    return this.http.post(endpoint, { facilityId });
  }

  getpatprescriptionbyfacilityid(facilityId: any) {
    const endpoint = `${this.apiUrl}patients/getpatprescriptionbyfacilityid`;
    return this.http.post(endpoint, { facilityId });
  }

  getPrescriptionByPrescriberId(prescribedBy: any) {
    const endpoint = `${this.apiUrl}doctors/getrefillrequestbyprescribedby`;
    return this.http.post(endpoint, { prescribedBy });
  }

  getpatprescriptionbydoctorid(doctorId: any, facilityId: any) {
    const endpoint = `${this.apiUrl}patients/getpatprescriptionbydoctorid`;
    return this.http.post(endpoint, { doctorId, facilityId });
  }

  getPrescriptionByPrescriptionId(prescriptionId: any): Observable<any> {
    const endpoint = `${this.apiUrl}patients/getallprescriptionbyprescriptionid`;
    return this.http.post<any>(endpoint, { prescriptionId });
  }

  getPresItemByPresciptionId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatprescriptionbypresid`;
    return this.http.post(endPoint, { prescriptionId: patientId });
  }

  getPatProcedureByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatproceduresbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatFollowUpsByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatfollowupsbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getAllAppointmentsSuperAdmin() {
    const endPoint = this.apiUrl + `patients/getallappointmentlist`;
    return firstValueFrom(this.http.get(endPoint));
  }

  getPatientReviewsList(patId: any) {
    const endPoint = this.apiUrl + `doctors/getdoctorreviewsbypatientid`;
    return firstValueFrom(this.http.post(endPoint, { patientId: patId }));
  }

  getAppointmentDetailsByAppointmentId(appId: any) {
    const endPoint =
      this.apiUrl + `patients/getappointmentdetailbyappointmentid`;
    return firstValueFrom(this.http.post(endPoint, { appointmentId: appId }));
  }

  getAppointmentDetailsByPatientId(pId: any) {
    const endPoint = this.apiUrl + `patients/getupcomingappointmentbypatientid`;
    return firstValueFrom(this.http.post(endPoint, { patientId: pId }));
  }

  getPatientHealthScore(pId: any) {
    const endPoint = this.apiUrl + `patients/getoverallhealthscore`;
    return firstValueFrom(this.http.post(endPoint, { patientId: pId }));
  }

  getPatientHistory(pId: any) {
    const endPoint = this.apiUrl + `patients/getpatienthistorybypatientid`;
    return firstValueFrom(this.http.post<any>(endPoint, { patientId: pId }));
  }

  getAllClinicalVisitsByPatientId(pId: any) {
    const endPoint = this.apiUrl + `patients/getallclinicalvisitbypatientid`;
    return firstValueFrom(this.http.post<any>(endPoint, { patientId: pId }));
  }

  getPatientHistoryByVisitId(pId: any, vId: any) {
    const endPoint =
      this.apiUrl + `patients/getpatienthistorybypatientidvisitid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { patientId: pId, visitId: vId })
    );
  }

  // Re-Fill List
  getPrescriptionReFillList() {
    const endPoint = this.apiUrl + `prescriptions/getrefillprescriptionlist`;
    return firstValueFrom(this.http.get(endPoint));
  }

  getRefillRequestById(id: any) {
    const endPoint = this.apiUrl + `patients/getrefillrequest`;
    return firstValueFrom(this.http.post(endPoint, { id }));
  }

  getAllProcedure() {
    const endPoint = this.apiUrl + `patients/getallprocedures`;
    return firstValueFrom(this.http.get(endPoint));
  }

  getPatImmunizationByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatimmunizationbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getPatClinicalAttachmentByPatientId(patientId: any) {
    const endPoint =
      this.apiUrl + `patients/getpatclinicalattachmentsbypatientid`;
    return this.http.post(endPoint, { patientId: patientId });
  }

  getClinicalVisitByVisitId(visitId: any) {
    const endPoint = this.apiUrl + `patients/getclinicalvisitsbyvisitid`;
    return this.http.post(endPoint, { visitId: visitId });
  }

  getHealthInsuranceListByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpathealthinsurbypatientid`;
    return firstValueFrom(this.http.post(endPoint, { patientId: patientId }));
  }

  getPrescriptionListByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getallprescriptionbypatientid`;
    return firstValueFrom(this.http.post(endPoint, { patientId: patientId }));
  }

  getDependentsByPatientId(patientId: any) {
    const endPoint = this.apiUrl + `patients/getpatdependentsbypatientid`;
    return firstValueFrom(this.http.post(endPoint, { patientId: patientId }));
  }

  getDoctorsList() {
    const endPoint = this.apiUrl + `doctors/getalldoctorslist`;
    return firstValueFrom(this.http.get(endPoint));
  }

  getCompleteDoctorDetailsByDocId(docId: any, facilityId: any) {
    const endPoint = this.apiUrl + `doctors/getcompletedocdetailbyid`;
    return firstValueFrom(
      this.http.post(endPoint, { doctorId: docId, facilityId: facilityId })
    );
  }

  getCompleteDoctorSlotsByDocId(
    docId: any,
    facilityId: any,
    date: any,
    todayDateTime: any
  ) {
    const endPoint =
      this.apiUrl + `doctors/getallshiftstimeslotsbydocidanddocdate`;
    return firstValueFrom(
      this.http.post(endPoint, {
        doctorId: docId,
        facilityId: facilityId,
        availableDate: date,
        todayDateTime: todayDateTime,
      })
    );
  }

  getTimeSlotBySlotId(slotId: any) {
    const endPoint = this.apiUrl + `doctors/getdoctimeslotdetails`;
    return firstValueFrom(this.http.post(endPoint, { timeSlotId: slotId }));
  }

  getPatientAppointmentDetails(patientId: any) {
    const endPoint = this.apiUrl + `patients/getallappointmentbypatientid`;
    return firstValueFrom(this.http.post(endPoint, { patientid: patientId }));
  }

  getPatientScheduledAppointmentDetails(patientId: any) {
    const endPoint = this.apiUrl + `patients/getappointscheduledstatus`;
    return firstValueFrom(this.http.post(endPoint, { patientid: patientId }));
  }

  getPatientChatDoctors(patId: any) {
    const endPoint = this.apiUrl + `doctors/getdoctordetailsbypatientid`;
    return firstValueFrom(this.http.post(endPoint, { patientId: patId }));
  }

  getAllQuriesByPatientId(patId: any) {
    const endPoint = this.apiUrl + `doctors/getallqueriesbypatient`;
    return firstValueFrom(this.http.post(endPoint, { patientId: patId }));
  }

  getAllQuriesByDoctorId(docId: any) {
    const endPoint = this.apiUrl + `doctors/getallqueriesfordoctor`;
    return firstValueFrom(this.http.post(endPoint, { doctorId: docId }));
  }

  getPendingQuriesByDoctorId(docId: any) {
    const endPoint = this.apiUrl + `doctors/getpendingqueriesfordoctor`;
    return firstValueFrom(this.http.post(endPoint, { doctorId: docId }));
  }

  getAllReviews(docId: any) {
    const endPoint = this.apiUrl + `doctors/getdoctorreviews`;
    return firstValueFrom(this.http.post(endPoint, { doctorId: docId }));
  }

  getFullPatientDetailsById(patientId: any) {
    const endPoint = this.apiUrl + `user-accounts/getpatientdetailbyid`;
    return firstValueFrom(this.http.post(endPoint, { patientid: patientId }));
  }

  getpatientphysexaminationbyvisitid(visitId: any) {
    const endPoint =
      this.apiUrl + `patients/getpatientphysexaminationbyvisitid`;
    return firstValueFrom(this.http.post(endPoint, { visitId: visitId }));
  }

  getpatienteyeexambyvisitid(visitId: any) {
    const endPoint = this.apiUrl + `patients/getpatienteyeexambyvisitid`;
    return firstValueFrom(this.http.post(endPoint, { visitId: visitId }));
  }

  getpatientDentalScreeningbyvisitid(visitId: any) {
    const endPoint =
      this.apiUrl + `patients/getpatientdentalscreeningbyvisitid`;
    return firstValueFrom(this.http.post(endPoint, { visitId: visitId }));
  }

  // Create
  createPatHealthAdvisor(payload: any) {
    const endPoint = this.apiUrl + `patients/createpathealthadvisor`;
    return this.http.post(endPoint, payload);
  }

  createPatLifeStyleSocialHistory(payload: any) {
    const endPoint = this.apiUrl + `patients/createlifestyleandsocialhistory`;
    return this.http.post(endPoint, payload);
  }

  createChiefComplaints(payload: any) {
    const endPoint = this.apiUrl + `patients/createchiefcomplaints`;
    return this.http.post(endPoint, payload);
  }

  createChiefDiagnosis(payload: any) {
    const endPoint = this.apiUrl + `patients/creatediagnosis`;
    return this.http.post(endPoint, payload);
  }

  createPatientAllegies(payload: any) {
    const endPoint = this.apiUrl + `patients/createpatientallergies`;
    return this.http.post(endPoint, payload);
  }

  createClinicalAttachment(payload: any) {
    const endPoint = this.apiUrl + `patients/createclinicalattachment`;
    return this.http.post(endPoint, payload);
  }

  createClinicalNotes(payload: any) {
    const endPoint = this.apiUrl + `patients/createclinicalnotes`;
    return this.http.post(endPoint, payload);
  }

  createClinicalVisits(payload: any) {
    const endPoint = this.apiUrl + `patients/createclinicalvisits`;
    return this.http.post(endPoint, payload);
  }

  updatePatientVitals(payload: any) {
    const endPoint = this.apiUrl + `patients/updatevitalsigns`;
    return this.http.post(endPoint, payload);
  }

  createPatientVitals(payload: any) {
    const endPoint = this.apiUrl + `patients/createvitalsigns`;
    return this.http.post(endPoint, payload);
  }

  createPatientVitals2(payload: any) {
    const endPoint = this.apiUrl + `patients/createvitalsigns`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  createFamilyHistory(payload: any) {
    const endPoint = this.apiUrl + `patients/createfamilyhistory`;
    return this.http.post(endPoint, payload);
  }

  createFollowups(payload: any) {
    const endPoint = this.apiUrl + `patients/createfollowups`;
    return this.http.post(endPoint, payload);
  }

  createGynechistory(payload: any) {
    const endPoint = this.apiUrl + `patients/creategynechistory`;
    return this.http.post(endPoint, payload);
  }

  createHealthInsurance(payload: any) {
    const endPoint = this.apiUrl + `patients/createhealthinsurance`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  createPatientPresillness(payload: any) {
    const endPoint = this.apiUrl + `patients/createpatientpresillness`;
    return this.http.post(endPoint, payload);
  }

  createPatientSurgicalHistory(payload: any) {
    const endPoint = this.apiUrl + `patients/createsurgicalhistory`;
    return this.http.post(endPoint, payload);
  }

  createPatientHospitalHistory(payload: any) {
    const endPoint = this.apiUrl + `patients/createhospitalhistory`;
    return this.http.post(endPoint, payload);
  }

  createPatientPrescription(payload: any) {
    const endPoint = this.apiUrl + `patients/createprescriptions`;
    return this.http.post(endPoint, payload);
  }

  createPatientPrescriptionItem(payload: any) {
    const endPoint = this.apiUrl + `patients/createprescriptionitem`;
    return this.http.post(endPoint, payload);
  }

  createPatientMedicine(payload: any) {
    const endPoint = this.apiUrl + `prescriptions/createprescmedicines`;
    return this.http.post(endPoint, payload);
  }

  createPresRefill(payload: any) {
    const endPoint = this.apiUrl + `prescriptions/refill-request`;
    return this.http.post(endPoint, payload);
  }

  createPatientImmunization(payload: any) {
    const endPoint = this.apiUrl + `patients/createimmunization`;
    return this.http.post(endPoint, payload);
  }

  createPatientInvestigations(payload: any) {
    const endPoint = this.apiUrl + `patients/createinvestigations`;
    return this.http.post(endPoint, payload);
  }

  createPatientProcedures(payload: any) {
    const endPoint = this.apiUrl + `patients/createprocedure`;
    return this.http.post(endPoint, payload);
  }

  createPatienEyeCheckup(payload: any) {
    const endPoint = this.apiUrl + `patients/createpatienteyeexamination`;
    return this.http.post(endPoint, payload);
  }

  createPatienPhysicalExamination(payload: any) {
    const endPoint = this.apiUrl + `patients/createpatientphysexamination`;
    return this.http.post(endPoint, payload);
  }

  createPatienDentalScreening(payload: any) {
    const endPoint = this.apiUrl + `patients/createpatdentalscreening`;
    return this.http.post(endPoint, payload);
  }

  createPatientDietician(payload: any) {
    const endPoint = this.apiUrl + `activity/createdietitianvisit`;
    return this.http.post(endPoint, payload);
  }

  createPatientRehab(payload: any) {
    const endPoint = this.apiUrl + `activity/createrehabform`;
    return this.http.post(endPoint, payload);
  }

  createPatientDependant(payload: any) {
    const endPoint = this.apiUrl + `patients/createdependents`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  createPatientAppointment(payload: any) {
    const endPoint = this.apiUrl + `patients/createpatientappointment`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  sendMessage(payload: any) {
    const endPoint = this.apiUrl + `messages/create`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  createQuery(payload: any) {
    const endPoint = this.apiUrl + `doctors/createqueryfordoctor`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  createReview(payload: any) {
    const endPoint = this.apiUrl + `doctors/createdocreviews`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  // Update
  updatePatHealthAdvisor(payload: any) {
    const endPoint = this.apiUrl + `patients/updatepathealthadvisor`;
    return this.http.post(endPoint, payload);
  }

  updateClinicalVisits(payload: any) {
    const endPoint = this.apiUrl + `patients/updateclinicalvisits`;
    return this.http.post(endPoint, payload);
  }

  updatePatSurgicalHistory(payload: any) {
    const endPoint = this.apiUrl + `patients/updatesurgicalhistory`;
    return this.http.post(endPoint, payload);
  }

  updatePatComplaints(payload: any) {
    const endPoint = this.apiUrl + `patients/updatechiefcomplaints`;
    return this.http.post(endPoint, payload);
  }

  updatePatEyeExamination(payload: any) {
    const endPoint = this.apiUrl + `patients/updatepateyeexamination`;
    return this.http.post(endPoint, payload);
  }

  updatePatDentalScreening(payload: any) {
    const endPoint = this.apiUrl + `patients/updatepatdentalscreening`;
    return this.http.post(endPoint, payload);
  }

  updatePatPhysicalExamination(payload: any) {
    const endPoint = this.apiUrl + `patients/updatepatientphysexamination`;
    return this.http.post(endPoint, payload);
  }

  updatePatDiagnosis(payload: any) {
    const endPoint = this.apiUrl + `patients/updatediagnosis`;
    return this.http.post(endPoint, payload);
  }

  updatePatHospitalHistory(payload: any) {
    const endPoint = this.apiUrl + `patients/updatehospitalhistory`;
    return this.http.post(endPoint, payload);
  }

  updateClinicalNotes(payload: any) {
    const endPoint = this.apiUrl + `patients/updateclinicalnotes`;
    return this.http.post(endPoint, payload);
  }

  updateFamilyHistory(payload: any) {
    const endPoint = this.apiUrl + `patients/updatefamilyhistory`;
    return this.http.post(endPoint, payload);
  }

  updatePresentIllness(payload: any) {
    const endPoint = this.apiUrl + `patients/updatepatientpresillness`;
    return this.http.post(endPoint, payload);
  }

  updatePatInvestigation(payload: any) {
    const endPoint = this.apiUrl + `patients/updateinvestigations`;
    return this.http.post(endPoint, payload);
  }

  updatePatAllergies(payload: any) {
    const endPoint = this.apiUrl + `patients/updatepatientallergies`;
    return this.http.post(endPoint, payload);
  }

  updatePatPrescription(payload: any) {
    const endPoint = this.apiUrl + `patients/updateprescriptions`;
    return this.http.post(endPoint, payload);
  }

  updatePatPrescriptionWithPrescriptionItem(payload: any) {
    const endPoint = this.apiUrl + `prescriptions/updateprescriptiondetail`;
    return this.http.put(endPoint, payload);
  }

  approveRejctRefill(payload: any) {
    const endPoint = this.apiUrl + `prescriptions/doctorrefillapodecresponse`;
    return this.http.post(endPoint, payload);
  }

  updatePatPrescriptionItem(payload: any) {
    const endPoint = this.apiUrl + `patients/updateprescriptionitem`;
    return this.http.post(endPoint, payload);
  }

  updateRefillItem(payload: any) {
    const endPoint = this.apiUrl + `patients/updaterefillrequest`;
    return this.http.post(endPoint, payload);
  }

  updatePatProcedure(payload: any) {
    const endPoint = this.apiUrl + `patients/updateprocedure`;
    return this.http.post(endPoint, payload);
  }

  updatePatImmunization(payload: any) {
    const endPoint = this.apiUrl + `patients/updateimmunization`;
    return this.http.post(endPoint, payload);
  }

  updatePatFollowUps(payload: any) {
    const endPoint = this.apiUrl + `patients/updatefollowups`;
    return this.http.post(endPoint, payload);
  }

  updateClinicalAttachment(payload: any) {
    const endPoint = this.apiUrl + `patients/updateclinicalattachment`;
    return this.http.post(endPoint, payload);
  }

  updateHealthInsurance(payload: any) {
    const endPoint = this.apiUrl + `patients/updatehealthinsurance`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  updateDependant(payload: any) {
    const endPoint = this.apiUrl + `patients/updatedependents`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  bookAppointment(payload: any) {
    const endPoint = this.apiUrl + `doctors/booktimeslotforpatient`;
    return firstValueFrom(this.http.put(endPoint, payload));
  }

  getMeetLink(id: any, roomName: string) {
    const endPoint =
      this.apiUrl + `common/livekit/token?identity=${id}&room=${roomName}`;
    return firstValueFrom(this.http.get(endPoint));
  }

  cancelPatientAppointment(payload: any) {
    const endPoint = this.apiUrl + `patients/updatestatusinpatientappointment`;
    return firstValueFrom(this.http.put(endPoint, payload));
  }

  reschedulePatientAppointment(payload: any) {
    const endPoint = this.apiUrl + `patients/rescheduledpatientappointment`;
    return firstValueFrom(this.http.put(endPoint, payload));
  }

  sendQueryResponse(payload: any) {
    const endPoint = this.apiUrl + `doctors/doctorrespondtoquery`;
    return firstValueFrom(this.http.put(endPoint, payload));
  }

  // Delete
  deletePatHealthAdvisor(id: any) {
    const endPoint = this.apiUrl + `patients/deletehealthadvisor/${id}`;
    return this.http.delete(endPoint);
  }

  deleteVisits(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatclinicalvisits/${id}`;
    return this.http.delete(endPoint);
  }

  deleteVitals(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatvitalsign/${id}`;
    return this.http.delete(endPoint);
  }

  deleteComplaints(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatchiefcomplaints/${id}`;
    return this.http.delete(endPoint);
  }

  deleteDiagnosis(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatdiagnosis/${id}`;
    return this.http.delete(endPoint);
  }

  deletePresentIllness(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatpresentillness/${id}`;
    return this.http.delete(endPoint);
  }

  deleteSurgicalHistory(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatsurgicalhistory/${id}`;
    return this.http.delete(endPoint);
  }

  deleteHospitalHistory(id: any) {
    const endPoint = this.apiUrl + `patients/deletepathospitalhistory/${id}`;
    return this.http.delete(endPoint);
  }

  deleteFamilyHistory(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatfamilyhistory/${id}`;
    return this.http.delete(endPoint);
  }

  deleteAllergies(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatallergies/${id}`;
    return this.http.delete(endPoint);
  }

  deleteClinicalNotes(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatclinicalnotes/${id}`;
    return this.http.delete(endPoint);
  }

  deleteInvestigation(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatinvestigation/${id}`;
    return this.http.delete(endPoint);
  }

  deletePrescription(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatprescription/${id}`;
    return this.http.delete(endPoint);
  }

  deletePrescriptionItem(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatprescriptionitem/${id}`;
    return this.http.delete(endPoint);
  }

  deleteRefill(id: any) {
    const endPoint = this.apiUrl + `patients/deleterefillrequest/${id}`;
    return this.http.delete(endPoint);
  }

  deleteImmunization(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatimmunization/${id}`;
    return this.http.delete(endPoint);
  }

  deleteProcedure(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatprocedures/${id}`;
    return this.http.delete(endPoint);
  }

  deleteFollowsUps(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatfollowups/${id}`;
    return this.http.delete(endPoint);
  }

  deleteClinicalAttachment(id: any) {
    const endPoint =
      this.apiUrl + `patients/deletepatclinicalattachments/${id}`;
    return this.http.delete(endPoint);
  }

  deleteHealthInsurance(id: any) {
    const endPoint = this.apiUrl + `patients/deletepathealthinsurance/${id}`;
    return firstValueFrom(this.http.delete(endPoint));
  }

  deleteDependant(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatdependents/${id}`;
    return firstValueFrom(this.http.delete(endPoint));
  }

  deleteReview(id: any) {
    const endPoint = this.apiUrl + `doctors/deletedocreviews/${id}`;
    return firstValueFrom(this.http.delete(endPoint));
  }

  deleteDocument(id: any) {
    const endPoint = this.apiUrl + `patients/deletedocumentbypatientid`;
    return this.http.post(endPoint, { id });
  }

  deleteSharedDocument(id: any) {
    const endPoint = this.apiUrl + `patients/deleteshareddocumentbyid`;
    return this.http.post(endPoint, { id });
  }

  deleteEyeCheckUp(id: any) {
    const endPoint = this.apiUrl + `patients/deletepateyeexamination/${id}`;
    return this.http.delete(endPoint);
  }

  deleteDentalScreening(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatdentalscreening/${id}`;
    return this.http.delete(endPoint);
  }

  deletePhysicalExamination(id: any) {
    const endPoint = this.apiUrl + `patients/deletepatphysexamination/${id}`;
    return this.http.delete(endPoint);
  }

  // Payments
  createPayment(payload: any) {
    const endPoint = this.apiUrl + `payments/createpaypalorder`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  capturePayment(payload: any) {
    const endPoint = this.apiUrl + `payments/capturepaypalorder`;
    return firstValueFrom(this.http.post(endPoint, payload));
  }

  generateAIReport(patientId: any) {
    const endPoint = this.apiUrl + `patients/getaiinputpatientoverview`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { patientId: patientId })
    );
  }

  addAiReportCount(payload: any) {
    const endPoint = this.apiUrl + `patients/createpatientreportscan`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  updateAiReportCount(payload: any) {
    const endPoint = this.apiUrl + `patients/updatepatientreportscan`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  deleteAiReportCount(id: any) {
    const endPoint = this.apiUrl + `patients/deletereportscan/${id}`;
    return firstValueFrom(this.http.delete<any>(endPoint));
  }

  getAiReportCount(pId: any) {
    const endPoint = this.apiUrl + `patients/getreportscanbypatientid`;
    return firstValueFrom(this.http.post<any>(endPoint, { patientId: pId }));
  }

  getPatientFavourites(patientId: any) {
    const endPoint = this.apiUrl + `patients/getfavouritesdrlistbypatientid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { patientId: patientId })
    );
  }

  getPatientHeartRate(patientId: any) {
    const endPoint =
      this.apiUrl + `patients/getheartratelast7visitsbypatientid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { patientId: patientId })
    );
  }

  getPatientBloodPressure(patientId: any) {
    const endPoint =
      this.apiUrl + `patients/getbloodpressurelast7visitsbypatientid`;
    return firstValueFrom(
      this.http.post<any>(endPoint, { patientId: patientId })
    );
  }

  addPrescriptionFeedBack(payload: any) {
    const endPoint = this.apiUrl + `prescriptions/createfollouppresc`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  getAllActivityBasedVisits(payload: any) {
    const endPoint =
      this.apiUrl + `patients/getpatienthistorybypatientidwoapptid`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }

  createPatientAvailableSlots(payload: any) {
    const endPoint = this.apiUrl + `patients/patientbookslotsforappointment`;
    return firstValueFrom(this.http.post<any>(endPoint, payload));
  }
}
