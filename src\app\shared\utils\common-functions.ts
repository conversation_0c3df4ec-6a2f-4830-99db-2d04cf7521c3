import { ToastrService } from 'ngx-toastr';
import { UserAccountsService } from '../services/user-accounts.service';
import {
  format,
  formatDistanceToNow,
  isAfter,
  isEqual,
  parse,
  parseISO,
} from 'date-fns';
import { inject, Injectable } from '@angular/core';
import Swal from 'sweetalert2';
import { AuthService } from '../auth/auth.service';
import { AdminService } from 'src/app/admin/service/admin.service';
import { HealthAdvisorService } from 'src/app/feature-module/health-advisor/service/health-advisor.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { NurseService } from 'src/app/feature-module/nurse/services/nurse.service';
import { DoctorService } from 'src/app/feature-module/doctor/services/doctor.service';
import { PatientService } from 'src/app/feature-module/patient/patient.service';
import { FacilityService } from 'src/app/feature-module/facility/facility.service';
import { LabService } from 'src/app/feature-module/lab-manager/services/lab.service';
import { DietitianService } from 'src/app/feature-module/dietitian/services/dietitian.service';
import { PhysioRehabService } from 'src/app/feature-module/physio-rehab-coordinator/services/physio-rehab.service';
import { PhilebotomistService } from 'src/app/feature-module/philebotomist/service/philebotomist.service';
import { I_APPOINTMENT_FOR_CONFIRMATION } from '../components/pages/appointments/view/model';
import { Meta, Title } from '@angular/platform-browser';

@Injectable({ providedIn: 'root' })
export class UtilFunctions {
  constructor() {}

  authService = inject(AuthService);
  adminService = inject(AdminService);
  haService = inject(HealthAdvisorService);
  nurseService = inject(NurseService);
  doctorService = inject(DoctorService);
  patientService = inject(PatientService);
  facilityService = inject(FacilityService);
  labService = inject(LabService);
  userAccountService = inject(UserAccountsService);
  dietitianService = inject(DietitianService);
  physioRehabService = inject(PhysioRehabService);
  philebotomistService = inject(PhilebotomistService);
  router = inject(Router);
  location = inject(Location);
  toastr = inject(ToastrService);
  swal = Swal;
  appTitle = inject(Title);
  appMeta = inject(Meta);

  user = this.authService.getDataFromSession('user');

  ROLES = {
    ADMIN: 'ROLE_EMR_SUPER_ADMIN',
    ADMIN_MANAGER: 'ROLE_ADMIN_MANAGER',
    DOCTOR: 'ROLE_DOCTOR',
    NURSE: 'ROLE_NURSE',
    PATIENT: 'ROLE_PATIENT',
    SECRETARY: 'ROLE_SECRETARY',
    LAB_TECHNICIAN: 'ROLE_LAB_TECHNICIAN',
    PHARMACISTS: 'ROLE_PHARMACISTS',
    MEDICAL_RECORDS: 'ROLE_MEDICAL_RECORDS',
    FINANCE: 'ROLE_FINANCE',
    HR: 'ROLE_HR',
    HEALTH_ADVISOR: 'ROLE_HEALTH_ADVISOR',
    DIETITIAN: 'ROLE_DIETITIAN',
    REHAB_COORDINATOR: 'ROLE_PHYSIO_REHAB_COORDINATOR',
    PHLEBOTOMIST: 'ROLE_PHLEBOTOMIST',
    LAB_MANAGER: 'ROLE_LAB_MANAGER',
    PHYSIOTHERAPIST: 'ROLE_PHYSIO_REHAB_COORDINATOR',
  };

  ROLES_DETAILS = {
    ADMIN: {
      name: 'Super Admin',
      roleName: 'ROLE_EMR_SUPER_ADMIN',
      roleDescription: 'Super Admin',
      route: '/admin',
    },
    ADMIN_MANAGER: {
      name: 'Admin Manager',
      roleName: 'ROLE_ADMIN_MANAGER',
      roleDescription: 'Admin Manager',
      route: '/admin/manager',
    },
    DOCTOR: {
      name: 'Doctor',
      roleName: 'ROLE_DOCTOR',
      roleDescription: 'Doctor',
      route: '/doctor',
    },
    NURSE: {
      name: 'Nurse',
      roleName: 'ROLE_NURSE',
      roleDescription: 'Nurse',
      route: '/nurse',
    },
    PATIENT: {
      name: 'Patient',
      roleName: 'ROLE_PATIENT',
      roleDescription: 'Patient',
      route: '/patient',
    },
    SECRETARY: {
      name: 'Secretary',
      roleName: 'ROLE_SECRETARY',
      roleDescription: 'Secretary',
      route: '/secretary',
    },
    LAB_TECHNICIAN: {
      name: 'Lab Technician',
      roleName: 'ROLE_LAB_TECHNICIAN',
      roleDescription: 'Lab Technician',
      route: '/labtech',
    },
    PHARMACISTS: {
      name: 'Pharmacists',
      roleName: 'ROLE_PHARMACISTS',
      roleDescription: 'Pharmacists',
      route: '/pharmacist',
    },
    MEDICAL_RECORDS: {
      name: 'Medical Records',
      roleName: 'ROLE_MEDICAL_RECORDS',
      roleDescription: 'Medical Records',
      route: '/medical-records',
    },
    FINANCE: {
      name: 'Finance',
      roleName: 'ROLE_FINANCE',
      roleDescription: 'Finance',
      route: '/finance',
    },
    HR: {
      name: 'HR',
      roleName: 'ROLE_HR',
      roleDescription: 'HR',
      route: '/hr',
    },
    HEALTH_ADVISOR: {
      name: 'Health Advisor',
      roleName: 'ROLE_HEALTH_ADVISOR',
      roleDescription: 'Health Advisor',
      route: '/health-advisor',
    },
    DIETITIAN: {
      name: 'Dietitian',
      roleName: 'ROLE_DIETITIAN',
      roleDescription: 'Dietitian',
      route: '/dietitian',
    },
    REHAB_COORDINATOR: {
      name: 'Rehab Care Coordinator',
      roleName: 'ROLE_PHYSIO_REHAB_COORDINATOR',
      roleDescription: 'Rehab Care Coordinator',
      route: '/physio-rehab-coordinator',
    },
    PHLEBOTOMIST: {
      name: 'Phlebotomist',
      roleName: 'ROLE_PHLEBOTOMIST',
      roleDescription: 'Phlebotomist',
      route: '/phlebotomist',
    },
    LAB_MANAGER: {
      name: 'Lab Manager',
      roleName: 'ROLE_LAB_MANAGER',
      roleDescription: 'Lab Manager',
      route: '/lab-manager',
    },
    PHYSIOTHERAPIST: {
      name: 'Physiotherapist',
      roleName: 'ROLE_PHYSIO_REHAB_COORDINATOR',
      roleDescription: 'Physiotherapist',
      route: '/physio-rehab-coordinator',
    },
  };

  getCurrentUserRoute() {
    if (this.user.roleName.includes(this.ROLES.PATIENT)) {
      return this.ROLES_DETAILS.PATIENT.route;
    }

    if (this.user.roleName.includes(this.ROLES.HEALTH_ADVISOR)) {
      return this.ROLES_DETAILS.HEALTH_ADVISOR.route;
    }

    if (this.user.roleName.includes(this.ROLES.ADMIN)) {
      return this.ROLES_DETAILS.ADMIN.route;
    }

    if (this.user.roleName.includes(this.ROLES.DOCTOR)) {
      return this.ROLES_DETAILS.DOCTOR.route;
    }

    if (this.user.roleName.includes(this.ROLES.NURSE)) {
      return this.ROLES_DETAILS.NURSE.route;
    }

    if (this.user.roleName.includes(this.ROLES.DIETITIAN)) {
      return this.ROLES_DETAILS.DIETITIAN.route;
    }

    if (this.user.roleName.includes(this.ROLES.PHYSIOTHERAPIST)) {
      return this.ROLES_DETAILS.PHYSIOTHERAPIST.route;
    }

    if (this.user.roleName.includes(this.ROLES.PHLEBOTOMIST)) {
      return this.ROLES_DETAILS.PHLEBOTOMIST.route;
    }

    if (this.user.roleName.includes(this.ROLES.ADMIN_MANAGER)) {
      return this.ROLES_DETAILS.ADMIN_MANAGER.route;
    }

    if (this.user.roleName.includes(this.ROLES.LAB_MANAGER)) {
      return this.ROLES_DETAILS.LAB_MANAGER.route;
    }

    if (this.user.roleName.includes(this.ROLES.LAB_TECHNICIAN)) {
      return this.ROLES_DETAILS.LAB_TECHNICIAN.route;
    }

    if (this.user.roleName.includes(this.ROLES.PHARMACISTS)) {
      return this.ROLES_DETAILS.PHARMACISTS.route;
    }

    if (this.user.roleName.includes(this.ROLES.MEDICAL_RECORDS)) {
      return this.ROLES_DETAILS.MEDICAL_RECORDS.route;
    }

    if (this.user.roleName.includes(this.ROLES.FINANCE)) {
      return this.ROLES_DETAILS.FINANCE.route;
    }

    if (this.user.roleName.includes(this.ROLES.HR)) {
      return this.ROLES_DETAILS.HR.route;
    }

    if (this.user.roleName.includes(this.ROLES.SECRETARY)) {
      return this.ROLES_DETAILS.SECRETARY.route;
    }

    if (this.user.roleName.includes(this.ROLES.REHAB_COORDINATOR)) {
      return this.ROLES_DETAILS.REHAB_COORDINATOR.route;
    }

    return '';
  }

  APPOINTMENT_TYPES = [
    {
      id: 'online',
      name: 'Online Consultation',
    },
    {
      id: 'clinic',
      name: 'Clinic Visit',
    },
    {
      id: 'nurse',
      name: 'Nurse Home Visit',
    },
    {
      id: 'hospital',
      name: 'Hospital Visit',
    },
    {
      id: 'sample',
      name: 'Blood Sample Collection',
    },
  ];

  VISIT_TYPES = [
    { name: 'OP (Outpatient Visit)', id: 'OP (Outpatient Visit)' },
    { name: 'IP (Inpatient Visit)', id: 'IP (Inpatient Visit)' },
    { name: 'Telemedicine', id: 'Telemedicine' },
    { name: 'Medical Camp', id: 'Medical Camp' },
    { name: 'Walk-in', id: 'Walk-in' },
    { name: 'Care Plans', id: 'Care Plans' },
    { name: 'Specialist', id: 'Specialist' },
    { name: 'GP', id: 'GP' },
    { name: 'Nurse', id: 'Nurse' },
    { name: 'Dietitian', id: 'Dietitian' },
    { name: 'Physiotherapist', id: 'Physiotherapist' },
    { name: 'Lab Tests', id: 'Lab Tests' },
    { name: 'Online', id: 'Online' },
  ];

  encrypt(data: any) {
    return this.adminService.encrypt(data);
  }

  decrypt(data: any) {
    return this.adminService.decrypt(data);
  }

  addAppTitle(title: string) {
    this.appTitle.setTitle(title);
  }

  addAppMeta(meta: any) {
    this.appMeta.addTags(meta);
  }

  async blobFromBase64(
    base64Data: string,
    contentType = 'application/pdf'
  ): Promise<Blob> {
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: contentType });
  }

  async fetchSecuredUrl(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.userAccountService.getSecureImgUrl({ fileUrl: url }).subscribe({
        next: (response: any) => {
          resolve(response['url']);
        },
        error: (error: any) => {
          this.toastr.error('', error.error.message, { timeOut: 3000 });
          reject(error);
        },
      });
    });
  }

  // private getNormalizedPathname(filePath: string): string {
  //   return new URL(filePath).pathname.replace(/^\/+/, '');
  // }

  getNormalizedPathname(filePath: string): string {
    if (!filePath) {
      console.error('Invalid file path:', filePath);
      return '';
    }

    try {
      const url = new URL(filePath, window.location.origin); // Handle relative paths
      return url.pathname.replace(/^\/+/, '');
    } catch (error) {
      console.error('Error parsing URL:', error, 'File Path:', filePath);
      return '';
    }
  }

  convertTo12HourFormat(time: string): string {
    const [hourStr, minuteStr] = time?.split(':');
    let hour = parseInt(hourStr, 10);
    const minute = minuteStr;
    const ampm = hour >= 12 ? 'PM' : 'AM';

    hour = hour % 12;
    hour = hour === 0 ? 12 : hour;

    const formattedHour = hour.toString().padStart(2, '0');

    return `${formattedHour}:${minute} ${ampm}`;
  }

  convertDateTo12HourFormat(date: string): string {
    const parsedDate = new Date(date);
    const formattedTime = format(parsedDate, 'hh:mm a'); // use date-fns format directly

    return formattedTime.toUpperCase(); // to ensure AM/PM is uppercase
  }

  convertDaysToYearsMonthsDays(totalDays: number): string {
    const years = Math.floor(totalDays / 365);
    const remainingDaysAfterYears = totalDays % 365;

    const months = Math.floor(remainingDaysAfterYears / 30);
    const days = remainingDaysAfterYears % 30;

    const parts = [];

    if (years > 0) parts.push(`${years} year${years !== 1 ? 's' : ''}`);
    if (months > 0) parts.push(`${months} month${months !== 1 ? 's' : ''}`);
    if (days > 0) parts.push(`${days} day${days !== 1 ? 's' : ''}`);

    return parts.length ? parts.join(', ') : '0 days';
  }

  timeAgo(isoString: string): string {
    if (!isoString) return 'N/A';
    try {
      const date = parseISO(isoString);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (err) {
      return 'Invalid date';
    }
  }

  calculateAge(dobString: string): string {
    const dob = new Date(dobString);
    const today = new Date();

    let years = today.getFullYear() - dob.getFullYear();
    let months = today.getMonth() - dob.getMonth();
    let days = today.getDate() - dob.getDate();

    if (days < 0) {
      months--;
      const prevMonth = new Date(today.getFullYear(), today.getMonth(), 0);
      days += prevMonth.getDate();
    }
    if (months < 0) {
      years--;
      months += 12;
    }

    if (years === 0) {
      if (months === 0) {
        return `${days} days old`;
      }
      return `${months} months, ${days} days old`;
    }

    if (days === 0) {
      return `${years} years, ${months} months old`;
    }

    return `${years} years, ${months} months, ${days} days old`;
  }

  getNormalAvatar = (): string => {
    // return 'assets/images/avatar-image.webp';
    return 'assets/images/avatar-image.webp';
  };

  getGenderBasedAvatar = (gender: string, name: string): string => {
    if (gender === 'Male' || gender === 'male') {
      return 'assets/images/avatar-image.webp';
    }
    if (gender === 'Female' || gender === 'female') {
      return 'assets/images/avatar-image.webp';
    }
    return 'assets/images/avatar-image.webp';
  };

  getGenderBasedDoctorAvatar = (gender: string, name: string): string => {
    if (gender === 'Male' || gender === 'male') {
      return 'assets/images/avatar-image.webp';
    }
    if (gender === 'Female' || gender === 'female') {
      return 'assets/images/avatar-image.webp';
    }
    return 'assets/images/avatar-image.webp';
  };

  getNameBasedDoctorAvatar = (name: string): string => {
    return 'assets/images/avatar-image.webp';
  };

  getFullName = (firstName: string, lastName: string): string => {
    if (!firstName && !lastName) return 'N/A';
    if (!firstName) return lastName;
    if (!lastName) return firstName;
    return firstName + ' ' + lastName;
  };

  getToday = (): Date => {
    return new Date();
  };

  getCurrentTime = (): string => {
    let time = format(new Date(), 'hh : mm : ss a');

    setInterval(() => {
      time = format(new Date(), 'hh : mm : ss a');
    }, 1000);

    return time;
  };

  getForattedDate(date: any): string {
    return format(new Date(date), 'yyyy-MM-dd');
  }

  async deleteModal1(): Promise<boolean> {
    const swalWithBootstrapButtons = Swal.mixin({
      customClass: {
        confirmButton: 'btn btn-success',
        cancelButton: 'btn btn-danger',
      },
      buttonsStyling: false,
    });

    const result = await swalWithBootstrapButtons.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
      reverseButtons: true,
    });

    if (result.isConfirmed) {
      return true;
    } else if (result.dismiss === Swal.DismissReason.cancel) {
      return false;
    }
    return false;
  }

  async deleteModal(): Promise<boolean> {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    });

    if (result.isConfirmed) {
      return true;
    }

    return false;
  }

  checkArray(array: any[]): boolean {
    return Array.isArray(array) && array.length > 0;
  }

  isArray(data: any): data is any[] {
    return Array.isArray(data);
  }

  capitalizeFirstLetter(sentence: string) {
    if (!sentence) return '';
    return sentence.charAt(0).toUpperCase() + sentence.slice(1);
  }

  wrapLongText(text: string | '', length: number = 30): string {
    if (!text) return '';
    const regex = new RegExp(`.{1,${length}}`, 'g');
    return text.match(regex)?.join('<br/>') ?? text;
  }

  formatDate(date: string, formatString: string = 'dd MMM yyyy'): string {
    return format(new Date(date), formatString);
  }

  getAppointmentTypeBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'clinic':
        return 'badge-success';
      case 'online':
        return 'badge-warning';
      case 'hospital':
        return 'badge-info';
      case 'sample':
        return 'badge-danger';
      case 'nurse':
        return 'badge-primary';
      default:
        return 'badge-secondary';
    }
  }

  getAppointmentTypeIcon(type: string): string {
    switch (type.toLowerCase()) {
      case 'online':
        return 'fas fa-video';
      case 'clinic':
        return 'fas fa-clinic-medical';
      case 'hospital':
        return 'fas fa-hospital';
      case 'nurse':
        return 'fas fa-user-nurse';
      case 'sample':
        return 'fas fa-tint';
      default:
        return 'fas fa-calendar';
    }
  }

  getConfirmationIcons(confirmationStatus: string): string {
    switch (confirmationStatus.toLowerCase()) {
      case 'pending':
        return 'bi bi-hourglass-split text-warning';
      case 'confirmed':
        return 'bi bi-check-circle-fill text-success';
      case 'cancelled':
        return 'bi bi-x-circle-fill text-danger';
      default:
        return 'bi bi-question-circle';
    }
  }

  splitCamelCase(str: string) {
    return str
      .split(/(?=[A-Z])/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  isFutureOrTodayAppointment(
    appointment: I_APPOINTMENT_FOR_CONFIRMATION
  ): boolean {
    const appointmentDateTime = parse(
      `${appointment.appointmentDate} ${appointment.appointmentTime}`,
      'yyyy-MM-dd HH:mm',
      new Date()
    );

    const now = new Date();

    // console.log('Appointment Date Time:', appointmentDateTime);
    // console.log('Now:', now);

    return (
      isAfter(appointmentDateTime, now) || isEqual(appointmentDateTime, now)
    );
  }

  joinMeeting(meetLink: string) {
    // Open meeting link in new tab
    window.open(meetLink, '_blank');
  }

  async getCurrentCoords(): Promise<{ lat: number; lon: number }> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (pos) =>
          resolve({
            lat: pos.coords.latitude,
            lon: pos.coords.longitude,
          }),
        (err) => reject(err)
      );
    });
  }

  async getCoordsFromCity(
    city: string
  ): Promise<{ lat: number; lon: number } | null> {
    try {
      const res = await fetch(
        `https://nominatim.openstreetmap.org/search?city=${encodeURIComponent(
          city
        )}&format=json&limit=1`
      );
      const data = await res.json();
      if (data.length > 0) {
        return {
          lat: parseFloat(data[0].lat),
          lon: parseFloat(data[0].lon),
        };
      }
      return null;
    } catch (err) {
      console.error('Geocode failed for city:', city, err);
      return null;
    }
  }

  getDistanceKm(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) *
        Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private deg2rad(deg: number) {
    return deg * (Math.PI / 180);
  }
}
