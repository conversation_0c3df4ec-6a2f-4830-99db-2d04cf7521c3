body {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.main-container {
  max-width: 100%;
  margin: 0 auto;
  // padding: 20px;
}

.appointment-card {
  //   background: white;
  //   border-radius: 8px;
  //   box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  //   border: 1px solid #e9ecef;
}

/* Header Styling */
.page-header {
  background: #fff;
  border-bottom: 1px solid #e9ecef;
  // padding: 24px 30px;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
}

/* Progress Steps */
.progress-section {
  background: #f8f9fa;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.step-buttons {
  display: flex;
  gap: 0;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #dee2e6;
}

.step-btn {
  flex: 1;
  padding: 12px 20px;
  background: white;
  border: none;
  border-right: 1px solid #dee2e6;
  color: #6c757d;
  font-weight: 500;
  transition: all 0.2s ease;
}

.step-btn:last-child {
  border-right: none;
}

.step-btn.active {
  background: #0d6efd;
  color: white;
}

.step-btn.completed {
  background: #198754;
  color: white;
}

.step-btn:hover:not(.active):not(.completed) {
  background: #f8f9fa;
}

/* Content Area */
.content-area {
  // padding: 30px;
}

.step-content {
  display: none;
}

.step-content.active {
  display: block;
}

.step-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.step-actions {
  display: flex;
  gap: 10px;
}

/* Form Styling */
.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 6px;
}

.form-control {
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 10px 12px;
  font-size: 0.95rem;
}

.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-icon {
  position: relative;
}

.form-icon .icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  cursor: pointer;
}

.form-control[type="date"] {
  padding-right: 40px;
}

/* Time Slot Sections */
.time-slot-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.slot-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.slot-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  margin-right: 12px;
}

.slot-badge.primary {
  background-color: #0d6efd;
}

.slot-badge.secondary {
  background-color: #6f42c1;
}

.slot-badge.tertiary {
  background-color: #fd7e14;
}

.slot-description {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0;
}

/* Button Styling */
.btn {
  padding: 8px 20px;
  border-radius: 4px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #0d6efd;
  color: white;
}

.btn-primary:hover {
  background-color: #0b5ed7;
}

.btn-success {
  background-color: #198754;
  color: white;
}

.btn-success:hover {
  background-color: #157347;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #bb2d3b;
}

/* Error Messages */
.text-danger {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 4px;
}

/* Confirmation Summary */
.summary-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  margin-top: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-weight: 500;
  color: #495057;
}

.summary-value {
  color: #2c3e50;
}

/* Alert Styling */
.alert {
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 20px;
}

.alert-info {
  background-color: #cff4fc;
  border: 1px solid #b3d7ff;
  color: #055160;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-container {
    padding: 15px;
  }

  .page-header {
    padding: 20px;
  }

  .content-area {
    padding: 20px;
  }

  .step-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .step-actions {
    display: flex;
    justify-content: space-between;
  }

  .step-btn {
    font-size: 0.875rem;
    padding: 10px 15px;
  }
}
