.card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }
}

.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
  font-size: 0.75rem;
  padding: 0.35em 0.65em;
}

.btn {
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
  }
}

.summary-card {
  border: none;
  border-radius: 10px;
  overflow: hidden;
}

.icon-large {
  font-size: 2.5rem;
  opacity: 0.7;
}

.text-primary {
  color: #0d6efd !important;
}

.text-success {
  color: #198754 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-info {
  color: #0dcaf0 !important;
}

.border-success {
  border-color: #198754 !important;
}

.border-warning {
  border-color: #ffc107 !important;
}

.bg-success {
  &.bg-opacity-10 {
    background-color: rgba(25, 135, 84, 0.1) !important;
  }
}

.bg-warning {
  &.bg-opacity-10 {
    background-color: rgba(255, 193, 7, 0.1) !important;
  }
}

// Custom animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.card {
  animation: fadeInUp 0.5s ease-out;
}

// Responsive improvements
@media (max-width: 768px) {
  .card-body {
    padding: 1rem 0.75rem;
  }

  .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

// Status specific styling
.status-pending {
  border-left: 4px solid #ffc107;
}

.status-confirmed {
  border-left: 4px solid #198754;
}

.status-cancelled {
  border-left: 4px solid #dc3545;
}

// Loading and empty states
.empty-state {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  margin: 2rem 0;
}
