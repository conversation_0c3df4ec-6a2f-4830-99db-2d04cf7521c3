<div class="row aos" data-aos="fade-up">
  <!-- Count Cards  -->
  <div class="col-xl-4 d-flex">
    <div class="dashboard-box-col w-100">
      <div class="dashboard-widget-box">
        <div class="dashboard-content-info">
          <h6>Total Patient</h6>
          <h4>{{ totalPatients }}</h4>
          <span class="text-success"
            ><i class="fa-solid fa-arrow-up"></i>15% From Last Week</span
          >
        </div>
        <div class="dashboard-widget-icon">
          <span class="dash-icon-box"
            ><i class="fa-solid fa-user-injured"></i
          ></span>
        </div>
      </div>
      <div class="dashboard-widget-box">
        <div class="dashboard-content-info">
          <h6>Patients Today</h6>
          <h4>{{ todayPatients }}</h4>
          <span class="text-danger"
            ><i class="fa-solid fa-arrow-up"></i>15% From Yesterday</span
          >
        </div>
        <div class="dashboard-widget-icon">
          <span class="dash-icon-box"
            ><i class="fa-solid fa-user-clock"></i
          ></span>
        </div>
      </div>
      <div class="dashboard-widget-box">
        <div class="dashboard-content-info">
          <h6>Appointments Today</h6>
          <h4>{{ upcomingAppointments.length }}</h4>
          <span class="text-success"
            ><i class="fa-solid fa-arrow-up"></i>20% From Yesterday</span
          >
        </div>
        <div class="dashboard-widget-icon">
          <span class="dash-icon-box"
            ><i class="fa-solid fa-calendar-days"></i
          ></span>
        </div>
      </div>
    </div>
  </div>
  <!-- Upcoming / Past Appointments -->
  <div class="col-md-8">
    <div class="dashboard-card w-100">
      <div class="dashboard-card-head">
        <div
          class="header-title d-flex justify-content-between align-items-center w-100"
        >
          <h5>Appointments List</h5>
          <div>
            <a
              class="btn btn-primary float-end mx-2"
              (click)="viewAllAppointments()"
            >
              Expand
            </a>
          </div>
        </div>
      </div>

      <div class="dashboard-card-body">
        <!-- Tabs Header -->
        <ul class="nav nav-tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <a
              class="nav-link active"
              data-bs-toggle="tab"
              href="#upcoming"
              role="tab"
              >Upcoming</a
            >
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" data-bs-toggle="tab" href="#past" role="tab"
              >Past</a
            >
          </li>
        </ul>

        <!-- Tabs Content -->
        <div class="tab-content">
          <!-- Upcoming Appointments -->
          <div class="tab-pane fade show active" id="upcoming" role="tabpanel">
            <LoadingSpinner
              [isLoading]="getUpcomingLimitAppointments.isLoading()"
            ></LoadingSpinner>

            <PageWithSearchPagination
              *ngIf="
                getUpcomingLimitAppointments.isSuccess() &&
                getUpcomingLimitAppointments.data()?.length
              "
              [data]="getUpcomingLimitAppointments.data()"
              (filteredDataChange)="filteredUpcomingAppointments = $event"
              [isPagination]="true"
              [itemsPerPage]="3"
              [isSearch]="true"
              searchPlaceHolder="Search by Patient Name"
              [isItemsPerPage]="false"
            >
              <div class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light d-none">
                    <tr>
                      <th class="border-0 ps-4">
                        <i class="fas fa-user me-2 text-muted"></i>Patient
                        Information
                      </th>
                      <th class="border-0">
                        <i class="fas fa-calendar-alt me-2 text-muted"></i
                        >Appointment Details
                      </th>
                      <th class="border-0">
                        <i class="fas fa-info-circle me-2 text-muted"></i>Status
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let app of filteredUpcomingAppointments;
                        let i = index
                      "
                      (click)="upcomingAppointmentNavigation(app)"
                      class="cursor-pointer appointment-row"
                      [style.animation-delay]="i * 0.1 + 's'"
                    >
                      <td class="ps-4 py-3">
                        <div class="d-flex align-items-center">
                          <div class="position-relative me-3">
                            <img
                              [src]="app.photoLink || util.getNormalAvatar()"
                              [alt]="app.patientName + ' Avatar'"
                              class="rounded-circle border border-2 border-light shadow-sm"
                              style="
                                width: 50px;
                                height: 50px;
                                object-fit: cover;
                              "
                              onerror="this.src=util.getNormalAvatar()"
                            />
                            <span
                              class="position-absolute bottom-0 end-0 translate-middle p-1 bg-success border border-white rounded-circle"
                            >
                              <span class="visually-hidden">Online</span>
                            </span>
                          </div>
                          <div class="patient-details">
                            <h6 class="mb-1 fw-semibold text-dark">
                              {{ app.patientName || "N/A" }}
                            </h6>
                            <div class="d-flex align-items-center">
                              <i
                                class="fas fa-hospital text-muted me-1"
                                style="font-size: 12px"
                              ></i>
                              <span class="text-muted small">
                                {{
                                  app.facilityName
                                    ? (app.facilityName | titlecase)
                                    : "No Facility"
                                }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="py-3">
                        <div class="appointment-info">
                          <div class="d-flex align-items-center mb-1">
                            <i class="fas fa-calendar text-primary me-2"></i>
                            <span class="fw-medium text-dark">
                              {{
                                app.appointmentDate
                                  ? (app.appointmentDate
                                    | date : "MMM dd, yyyy")
                                  : "N/A"
                              }}
                            </span>
                          </div>
                          <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-clock text-success me-2"></i>
                            <span class="text-muted">
                              {{
                                app.appointmentTime
                                  ? convertTo12HourFormat(app.appointmentTime)
                                  : "N/A"
                              }}
                            </span>
                          </div>
                          <span
                            class="badge rounded-pill px-3 py-1 fw-normal"
                            [ngClass]="{
                              'bg-primary':
                                app.appointmentType?.toLowerCase() === 'clinic',
                              'bg-success':
                                app.appointmentType?.toLowerCase() === 'online',
                              'bg-warning':
                                app.appointmentType?.toLowerCase() === 'nurse',
                              'bg-info':
                                app.appointmentType?.toLowerCase() ===
                                'hospital',
                              'bg-danger':
                                app.appointmentType?.toLowerCase() === 'sample',
                              'bg-secondary': app.appointmentType
                            }"
                          >
                            <i class="fas fa-stethoscope me-1"></i>
                            {{
                              app.appointmentType
                                ? (app.appointmentType | titlecase)
                                : "N/A"
                            }}
                          </span>
                        </div>
                      </td>

                      <td class="py-3">
                        <div class="d-flex align-items-center">
                          <div class="status-indicator me-2">
                            <span
                              class="badge bg-warning text-dark rounded-pill px-3 py-2"
                            >
                              <i class="fas fa-clock me-1"></i>
                              {{
                                app.appointmentStatus
                                  ? (app.appointmentStatus | titlecase)
                                  : "N/A"
                              }}
                            </span>
                          </div>
                        </div>
                        <small class="text-muted d-block mt-1">
                          <i class="fas fa-map-marker-alt me-1"></i>
                          {{ app.facilityName ? "On-site" : "Virtual" }}
                        </small>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </PageWithSearchPagination>

            <ErrorAlert
              error="No Upcoming Appointments Found"
              *ngIf="getUpcomingLimitAppointments.isError()"
              color="blue"
            ></ErrorAlert>
          </div>

          <!-- Past Appointments -->
          <div class="tab-pane fade" id="past" role="tabpanel">
            <LoadingSpinner
              [isLoading]="getPastLimitAppointments.isLoading()"
            ></LoadingSpinner>

            <PageWithSearchPagination
              *ngIf="
                getPastLimitAppointments.isSuccess() &&
                getPastLimitAppointments.data()?.length
              "
              [data]="getPastLimitAppointments.data()"
              (filteredDataChange)="filteredPastAppointments = $event"
              [isPagination]="true"
              [itemsPerPage]="3"
              [isSearch]="true"
              searchPlaceHolder="Search by Patient Name"
              [isItemsPerPage]="false"
            >
              <div class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light d-none">
                    <tr>
                      <th class="border-0 ps-4">
                        <i class="fas fa-user me-2 text-muted"></i>Patient
                        Information
                      </th>
                      <th class="border-0">
                        <i class="fas fa-calendar-alt me-2 text-muted"></i
                        >Appointment Details
                      </th>
                      <th class="border-0">
                        <i class="fas fa-info-circle me-2 text-muted"></i>Status
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="
                        let app of filteredPastAppointments;
                        let i = index
                      "
                      (click)="upcomingAppointmentNavigation(app)"
                      class="cursor-pointer appointment-row"
                      [style.animation-delay]="i * 0.1 + 's'"
                    >
                      <td class="ps-4 py-3">
                        <div class="d-flex align-items-center">
                          <div class="position-relative me-3">
                            <img
                              [src]="app.photoLink || util.getNormalAvatar()"
                              [alt]="app.patientName + ' Avatar'"
                              class="rounded-circle border border-2 border-light shadow-sm"
                              style="
                                width: 50px;
                                height: 50px;
                                object-fit: cover;
                              "
                              onerror="this.src=util.getNormalAvatar()"
                            />
                            <span
                              class="position-absolute bottom-0 end-0 translate-middle p-1 bg-success border border-white rounded-circle"
                            >
                              <span class="visually-hidden">Online</span>
                            </span>
                          </div>
                          <div class="patient-details">
                            <h6 class="mb-1 fw-semibold text-dark">
                              {{ app.patientName || "N/A" }}
                            </h6>
                            <div class="d-flex align-items-center">
                              <i
                                class="fas fa-hospital text-muted me-1"
                                style="font-size: 12px"
                              ></i>
                              <span class="text-muted small">
                                {{
                                  app.facilityName
                                    ? (app.facilityName | titlecase)
                                    : "No Facility"
                                }}
                              </span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="py-3">
                        <div class="appointment-info">
                          <div class="d-flex align-items-center mb-1">
                            <i class="fas fa-calendar text-primary me-2"></i>
                            <span class="fw-medium text-dark">
                              {{
                                app.appointmentDate
                                  ? (app.appointmentDate
                                    | date : "MMM dd, yyyy")
                                  : "N/A"
                              }}
                            </span>
                          </div>
                          <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-clock text-success me-2"></i>
                            <span class="text-muted">
                              {{
                                app.appointmentTime
                                  ? convertTo12HourFormat(app.appointmentTime)
                                  : "N/A"
                              }}
                            </span>
                          </div>
                          <span
                            class="badge rounded-pill px-3 py-1 fw-normal"
                            [ngClass]="{
                              'bg-primary':
                                app.appointmentType?.toLowerCase() === 'clinic',
                              'bg-success':
                                app.appointmentType?.toLowerCase() === 'online',
                              'bg-warning':
                                app.appointmentType?.toLowerCase() === 'nurse',
                              'bg-info':
                                app.appointmentType?.toLowerCase() ===
                                'hospital',
                              'bg-danger':
                                app.appointmentType?.toLowerCase() === 'sample',
                              'bg-secondary': app.appointmentType
                            }"
                          >
                            <i class="fas fa-stethoscope me-1"></i>
                            {{
                              app.appointmentType
                                ? (app.appointmentType | titlecase)
                                : "N/A"
                            }}
                          </span>
                        </div>
                      </td>

                      <td class="py-3">
                        <div class="d-flex align-items-center">
                          <div class="status-indicator me-2">
                            <span
                              class="badge bg-warning text-dark rounded-pill px-3 py-2"
                            >
                              <i class="fas fa-clock me-1"></i>
                              {{
                                app.appointmentStatus
                                  ? (app.appointmentStatus | titlecase)
                                  : "N/A"
                              }}
                            </span>
                          </div>
                        </div>
                        <small class="text-muted d-block mt-1">
                          <i class="fas fa-map-marker-alt me-1"></i>
                          {{ app.facilityName ? "On-site" : "Virtual" }}
                        </small>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <!-- 🔴 Reuse your table HTML here with `filteredPast` -->
            </PageWithSearchPagination>

            <ErrorAlert
              error="No Past Appointments Found"
              *ngIf="getPastLimitAppointments.isError()"
              color="blue"
            ></ErrorAlert>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Weekly Overview,  -->
  <div class="col-xl-5 d-flex">
    <div class="dashboard-chart-col w-100">
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head border-0">
          <div class="header-title">
            <h5>Weekly Overview</h5>
          </div>
          <!-- <div class="chart-create-date">
            <h6>Mar 14 - Mar 21</h6>
          </div> -->
        </div>
        <div class="dashboard-card-body">
          <div class="chart-tab">
            <ul
              class="nav nav-pills product-licence-tab"
              id="pills-tab2"
              role="tablist"
            >
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link active"
                  id="pills-revenue-tab"
                  data-bs-toggle="pill"
                  data-bs-target="#pills-revenue"
                  type="button"
                  role="tab"
                  aria-controls="pills-revenue"
                  aria-selected="false"
                >
                  Revenue
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link"
                  id="pills-appointment-tab"
                  data-bs-toggle="pill"
                  data-bs-target="#pills-appointment"
                  type="button"
                  role="tab"
                  aria-controls="pills-appointment"
                  aria-selected="true"
                >
                  Appointments
                </button>
              </li>
            </ul>
            <div class="tab-content w-100" id="v-pills-tabContent">
              <div
                class="tab-pane fade show active"
                id="pills-revenue"
                role="tabpanel"
                aria-labelledby="pills-revenue-tab"
              >
                <div *ngIf="doctorWeeklyRevenue.isPending()">Loading...</div>

                <div *ngIf="doctorWeeklyRevenue.isError()">Error</div>
                <div
                  *ngIf="
                    doctorWeeklyRevenue.isSuccess() &&
                    doctorWeeklyRevenue.data()
                  "
                >
                  <div
                    id="revenue-chart"
                    *ngIf="doctorWeeklyRevenue.data().length > 0"
                  >
                    <apx-chart
                      [series]="chartOptions1.series"
                      [chart]="chartOptions1.chart"
                      [dataLabels]="chartOptions1.dataLabels"
                      [plotOptions]="chartOptions1.plotOptions"
                      [xaxis]="chartOptions1.xaxis"
                    ></apx-chart>
                  </div>

                  <div *ngIf="doctorWeeklyRevenue.data().length == 0">
                    No Recent Revenue
                  </div>
                </div>
              </div>
              <div
                class="tab-pane fade"
                id="pills-appointment"
                role="tabpanel"
                aria-labelledby="pills-appointment-tab"
              >
                <div *ngIf="doctorWeeklyAppointments.isPending()">
                  Loading...
                </div>

                <div *ngIf="doctorWeeklyAppointments.isError()">Error</div>

                <div
                  *ngIf="
                    doctorWeeklyAppointments.isSuccess() &&
                    doctorWeeklyAppointments.data()
                  "
                >
                  <div
                    id="appointment-chart"
                    *ngIf="doctorWeeklyAppointments.data().length > 0"
                  >
                    <apx-chart
                      [series]="chartOptions2.series"
                      [chart]="chartOptions2.chart"
                      [dataLabels]="chartOptions2.dataLabels"
                      [plotOptions]="chartOptions2.plotOptions"
                      [xaxis]="chartOptions2.xaxis"
                    ></apx-chart>
                  </div>

                  <div *ngIf="doctorWeeklyAppointments.data().length == 0">
                    No Recent Appointments
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Upcoming Appointment, Recent Patients   -->
  <div class="col-xl-7 d-flex flex-column">
    <div class="dashboard-main-col w-100">
      <div
        class="upcoming-appointment-card"
        *ngIf="getUpcomingData() && getUpcomingData().length"
      >
        <div class="title-card">
          <h5>Upcoming Appointment</h5>
        </div>
        <div class="upcoming-patient-info">
          <div class="info-details">
            <span class="img-avatar">
              <!-- <img
                src="assets/img/doctors-dashboard/profile-01.jpg"
                alt="Img" /> -->
              <img
                src="assets/images/avatar-image.webp"
                alt="Avatar image"
                class="img-thumbnail"
                *ngIf="!getUpcomingData()[0].photoLink" />
              <img
                [src]="getUpcomingData()[0].photoLink"
                alt="Avatar image"
                class="img-thumbnail"
                *ngIf="getUpcomingData()[0].photoLink"
            /></span>
            <div class="name-info">
              <span>#{{ getUpcomingData()[0].location || "N/A" }}</span>
              <h6>
                {{ getUpcomingData()[0].patientName || "N/A" }}
              </h6>
            </div>
          </div>
          <div class="date-details">
            <span>General visit</span>
            <h6>
              {{
                getUpcomingData()[0].appointmentDate
                  ? (getUpcomingData()[0].appointmentDate
                    | date : "dd MMM yyyy")
                  : "N/A"
              }}
              {{
                getUpcomingData()[0].appointmentTime
                  ? convertTo12HourFormat(getUpcomingData()[0].appointmentTime)
                  : "N/A"
              }}
            </h6>
          </div>
          <div class="circle-bg">
            <img src="assets/img/bg/dashboard-circle-bg.png" alt="" />
          </div>
        </div>
        <div class="appointment-card-footer">
          <h5 *ngIf="getUpcomingData()[0].appointmentType === 'online'">
            <i class="fa-solid fa-video"></i>Video Appointment
          </h5>
          <h5 *ngIf="getUpcomingData()[0].appointmentType === 'clinic'">
            <i class="fa-solid fa-clinic-medical"></i>Clinic Appointment
          </h5>
          <div class="btn-appointments">
            <!-- <a  class="btn">Chat Now</a> -->
            <a
              class="btn"
              (click)="viewAppointment(getUpcomingData()[0].appointmentId)"
              >View Appointment</a
            >
          </div>
        </div>
      </div>
      <div
        class="upcoming-appointment-card"
        *ngIf="!getUpcomingData() || !getUpcomingData().length"
      >
        No Upcoming Appointments
      </div>
    </div>

    <div class="dashboard-card w-100">
      <div class="dashboard-card-head">
        <div class="header-title">
          <h5>Recent Patients</h5>
        </div>
      </div>
      <div class="dashboard-card-body" *ngIf="!isRecentPatientsLoading">
        <div class="d-flex recent-patient-grid-boxes overflow-auto">
          <div
            class="recent-patient-grid pointer"
            *ngFor="let patient of recentPatients"
            (click)="
              viewAppointmetVisit(
                patient?.patientId,
                patient?.appointmentId,
                patient?.visitId
              )
            "
          >
            <a class="patient-img">
              <img
                src="assets/images/avatar-image.webp"
                alt="Avatar image"
                class="img-thumbnail"
                *ngIf="!patient.photoUrl && !patient.photoLink"
              />
              <img
                [src]="patient.photoUrl"
                alt="Profile image"
                class="img-thumbnail"
                *ngIf="patient.photoUrl && patient.photoLink"
              />
            </a>
            <h5>
              <a (click)="viewPatientAppointments(patient?.patientId)">{{
                patient.patientName || "N/A"
              }}</a>
            </h5>
            <span>Patient ID : {{ patient.patientId || "N/A" }}</span>
          </div>
        </div>
        <div *ngIf="recentPatients && recentPatients.length == 0">
          No Patients
        </div>
      </div>
      <div
        class="dashboard-card-body d-flex justify-content-center align-items-center"
        *ngIf="isRecentPatientsLoading"
      >
        Patients Loading...
      </div>
    </div>
  </div>
  <!-- My Patients  -->
  <div class="col-xl-12 d-flex">
    <div class="dashboard-card w-100">
      <div class="dashboard-card-head border-0">
        <div
          class="header-title d-flex justify-content-between align-items-center w-100"
        >
          <h5>My Patients ({{ allPatientsList && allPatientsList.length }})</h5>
          <div>
            <a
              class="btn btn-primary float-end mx-2"
              [routerLink]="['/doctor/my-patient-img']"
              >Expand</a
            >
          </div>
        </div>
      </div>
      <div class="dashboard-card-body p-0">
        <div class="custom-table p-0">
          <app-dynamic-table
            [dataList]="allPatientsList"
            [columns]="patientsCols"
            [showSerialNumber]="false"
            [showActions]="false"
          />
        </div>
      </div>
    </div>
  </div>
  <!-- Re-Fill Prescriptions List  -->
  <div class="col-xl-12 d-none">
    <div class="dashboard-card w-100">
      <div class="dashboard-card-head border-0">
        <div class="header-title">
          <h5>Follow ups ({{ refillList && refillList.length }})</h5>
        </div>
      </div>
      <div class="dashboard-card-body p-0">
        <div class="search-header d-flex align-items-baseline gap-2 gap-lg-5">
          <div class="search-field">
            <select
              name="selectPageCount"
              id="selectPageCount"
              class="form-control"
              [(ngModel)]="itemsPerPage"
              (change)="onItemsPerPageChange()"
            >
              <option value="5" selected>
                5 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
              </option>
              <option value="10">
                10 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
              </option>
              <option value="15">
                15 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
              </option>
              <option value="25">
                25 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
              </option>
              <option value="50">
                50 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
              </option>
              <option value="50">
                100 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
              </option>
            </select>
          </div>
          <div class="search-field flex-grow-1">
            <input
              type="text"
              class="form-control"
              placeholder="Search"
              [(ngModel)]="searchQuery"
              (input)="onSearchChange()"
            />
            <span class="search-icon"
              ><i class="fa-solid fa-magnifying-glass"></i
            ></span>
          </div>
        </div>

        <div class="custom-table p-0">
          <div class="table-responsive">
            <table class="table table-center mb-0">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Facility Name</th>
                  <th>Patient Name</th>
                  <th>Prescribed by</th>
                  <th>Prescription Date</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                <ng-container
                  *ngIf="
                    filteredRefillList && filteredRefillList.length;
                    else noUsers
                  "
                >
                  <tr
                    *ngFor="
                      let refill of filteredRefillList;
                      trackBy: trackByRefillId;
                      let i = index
                    "
                  >
                    <td>
                      <a
                        href="javascript:void(0);"
                        class="text-blue-600"
                        data-bs-toggle="modal"
                        data-bs-target="#invoice_view"
                        >{{ i + 1 }}</a
                      >
                    </td>
                    <td>
                      {{ refill.facilityName ? refill.facilityName : "N/A" }}
                    </td>
                    <td>
                      {{ refill.patientName ? refill.patientName : "N/A" }}
                    </td>
                    <td>
                      Dr.{{ refill.doctorName ? refill.doctorName : "N/A" }}
                    </td>
                    <td>
                      {{
                        refill.prescriptionDate
                          ? (refill.prescriptionDate | date : "dd MMM YYYY")
                          : "N/A"
                      }}
                    </td>
                    <td>
                      <button
                        class="btn btn-primary"
                        title="Refill"
                        (click)="onReviewClick(refill)"
                      >
                        Review
                      </button>
                    </td>
                  </tr>
                </ng-container>
                <ng-template #noUsers>
                  <tr>
                    <td colspan="7" class="text-center">No users available</td>
                  </tr>
                </ng-template>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Pagination -->
        <div
          class="pagination-container d-flex justify-content-center align-items-center mt-3"
        >
          <ul class="pagination custom-pagination mb-0">
            <!-- Previous Button -->
            <li
              class="page-item"
              [class.disabled]="currentPage === 1"
              (click)="goToPage(currentPage - 1)"
            >
              <a class="page-link">
                <i class="fa fa-angle-left me-1"></i> Prev
              </a>
            </li>

            <!-- Page Numbers -->
            <li
              *ngFor="let page of visiblePages"
              class="page-item"
              [class.active]="page === currentPage"
              [class.disabled]="page === '...'"
              (click)="page !== '...' && goToPage(page)"
            >
              <a class="page-link">{{ page }}</a>
            </li>

            <!-- Next Button -->
            <li
              class="page-item"
              [class.disabled]="currentPage === totalPages()"
              (click)="goToPage(currentPage + 1)"
            >
              <a class="page-link">
                Next <i class="fa fa-angle-right ms-1"></i>
              </a>
            </li>
          </ul>
        </div>
        <!-- /Pagination -->
      </div>
    </div>
  </div>
  <!-- Invoices -->
  <!-- <div class="col-xl-12 d-flex">
    <div class="dashboard-card w-100">
      <div class="dashboard-card-head">
        <div class="header-title">
          <h5>Recent Invoices</h5>
        </div>
        <div class="card-view-link">
          <a routerLink="routes.invoice">View All</a>
        </div>
      </div>
      <div class="dashboard-card-body">
        <div class="table-responsive">
          <table class="table dashboard-table">
            <tbody>
              <tr>
                <td>
                  <div class="patient-info-profile">
                    <a routerLink="routes.invoice" class="table-avatar">
                      <img
                        src="assets/img/doctors-dashboard/profile-01.jpg"
                        alt="Img"
                      />
                    </a>
                    <div class="patient-name-info">
                      <h5>
                        <a routerLink="routes.invoice">Adrian</a>
                      </h5>
                      <span>#Apt0001</span>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <span class="paid-text">Amount</span>
                    <h6>$450</h6>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <span class="paid-text">Paid On</span>
                    <h6>11 Nov 2024</h6>
                  </div>
                </td>
                <td>
                  <div class="apponiment-view d-flex align-items-center">
                    <a routerLink="routes.invoiceView"
                      ><i class="isax isax-eye4"></i
                    ></a>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="patient-info-profile">
                    <a href="javascript:void(0);" class="table-avatar">
                      <img
                        src="assets/img/doctors-dashboard/profile-02.jpg"
                        alt="Img"
                      />
                    </a>
                    <div class="patient-name-info">
                      <h5><a href="javascript:void(0);">Kelly</a></h5>
                      <span>#Apt0002</span>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <span class="paid-text">Paid On</span>
                    <h6>10 Nov 2024</h6>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <span class="paid-text">Amount</span>
                    <h6>$500</h6>
                  </div>
                </td>
                <td>
                  <div class="apponiment-view d-flex align-items-center">
                    <a href="javascript:void(0);"
                      ><i class="isax isax-eye4"></i
                    ></a>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="patient-info-profile">
                    <a href="javascript:void(0);" class="table-avatar">
                      <img
                        src="assets/img/doctors-dashboard/profile-03.jpg"
                        alt="Img"
                      />
                    </a>
                    <div class="patient-name-info">
                      <h5>
                        <a href="javascript:void(0);">Samuel</a>
                      </h5>
                      <span>#Apt0003</span>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <span class="paid-text">Paid On</span>
                    <h6>03 Nov 2024</h6>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <span class="paid-text">Amount</span>
                    <h6>$320</h6>
                  </div>
                </td>
                <td>
                  <div class="apponiment-view d-flex align-items-center">
                    <a href="javascript:void(0);"
                      ><i class="isax isax-eye4"></i
                    ></a>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="patient-info-profile">
                    <a href="javascript:void(0);" class="table-avatar">
                      <img
                        src="assets/img/doctors-dashboard/profile-04.jpg"
                        alt="Img"
                      />
                    </a>
                    <div class="patient-name-info">
                      <h5>
                        <a href="javascript:void(0);">Catherine</a>
                      </h5>
                      <span>#Apt0004</span>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <span class="paid-text">Paid On</span>
                    <h6>01 Nov 2024</h6>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <span class="paid-text">Amount</span>
                    <h6>$240</h6>
                  </div>
                </td>
                <td>
                  <div class="apponiment-view d-flex align-items-center">
                    <a href="javascript:void(0);"
                      ><i class="isax isax-eye4"></i
                    ></a>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="patient-info-profile">
                    <a href="javascript:void(0);" class="table-avatar">
                      <img
                        src="assets/img/doctors-dashboard/profile-05.jpg"
                        alt="Img"
                      />
                    </a>
                    <div class="patient-name-info">
                      <h5>
                        <a href="javascript:void(0);">Robert</a>
                      </h5>
                      <span>#Apt0005</span>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <span class="paid-text">Paid On</span>
                    <h6>28 Oct 2024</h6>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <span class="paid-text">Amount</span>
                    <h6>$380</h6>
                  </div>
                </td>
                <td>
                  <div class="apponiment-view d-flex align-items-center">
                    <a href="javascript:void(0);"
                      ><i class="isax isax-eye4"></i
                    ></a>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div> 
  </div>-->
</div>
