<AdminFriendlyPageWrapper>
  <main>
    <header class="d-flex justify-content-between align-items-center">
      <div>
        <h3>Patient Requests</h3>
        <p class="m-0 pt-2">Select a patient</p>
      </div>
      <div class="d-flex gap-2 justify-content-end">
        <!-- <button class="btn btn-primary rounded" (click)="back()">Back</button> -->
      </div>
    </header>
    <hr />

    <LoadingSpinner
      *ngIf="patients.isLoading() || patients.isPending()"
    ></LoadingSpinner>

    <ErrorAlert *ngIf="patients.isError()"> </ErrorAlert>

    <ErrorAlert
      error="No Patients Found"
      *ngIf="
        patients.isSuccess() && patients.data() && patients.data().length === 0
      "
      color="blue"
    ></ErrorAlert>

    <PageWithSearchPagination
      [isSearch]="patients.data().length > 0"
      searchPlaceHolder="Search by Patient Name"
      [data]="patients.data()"
      (filteredDataChange)="filteredPatients.set($event)"
      [isPageLoading]="patients.isLoading()"
      [isPagination]="patients.data().length > 0"
      [itemsPerPage]="12"
      *ngIf="
        patients.isSuccess() && patients.data() && patients.data().length > 0
      "
    >
      <article class="row">
        <div
          class="p-1 col-md-4 col-lg-3"
          *ngFor="let patient of filteredPatients()"
        >
          <div
            class="card p-2 patient pointer"
            (click)="onPatientClick(patient)"
          >
            <div class="card-header mx-auto">
              <img
                [src]="patient.image || util.getNormalAvatar()"
                alt="Image"
                class="img-thumbnail rounded-circle image"
                style="width: 7.5rem; height: 7.5rem"
              />
            </div>
            <div class="card-body mx-auto">
              <p class="m-0 fw-bold">
                {{ patient.fullName }}
              </p>
            </div>
          </div>
        </div>

        <ErrorAlert
          error="No Patients Found"
          *ngIf="filteredPatients().length === 0"
          color="blue"
        ></ErrorAlert>
      </article>
    </PageWithSearchPagination>
  </main>
</AdminFriendlyPageWrapper>
