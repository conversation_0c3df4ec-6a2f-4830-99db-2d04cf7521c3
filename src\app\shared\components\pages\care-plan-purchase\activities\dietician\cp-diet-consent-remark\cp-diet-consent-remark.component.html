<form [formGroup]="patientConsenForm">
  <!-- Consent & Remarks -->
  <div
    class="card mb-4 shadow-sm border-0 rounded-3"
    style="background-color: whitesmoke"
  >
    <div
      class="card-header border-0 pb-3 d-flex justify-content-between align-items-center"
    >
      <h5 class="card-title mb-0 fw-bold text-primary">
        <i class="bi bi-clipboard-check me-2"></i> Consent & Remarks
      </h5>
      <!-- Action Buttons -->
      <div class="d-flex align-items-center">
        <button
          class="btn btn-outline-secondary rounded-pill px-4 me-2 shadow-sm"
          (click)="patientConsenForm.reset()"
        >
          <i class="bi bi-x-circle me-1"></i> Cancel
        </button>
        <button
          class="btn btn-primary rounded-pill px-4 shadow-sm"
          (click)="savePatientConsent()"
          [disabled]="isCompleted"
        >
          <i class="bi bi-check-circle me-1"></i>
          {{ patientConsentData !== null ? "Update" : "Save" }}
        </button>
      </div>
    </div>

    <div class="card-body">
      <div class="row g-4">
        <!-- Entry Date -->
        <div class="col-md-6">
          <div class="mb-3 position-relative">
            <label class="form-label fw-semibold" for="entryDate">
              <i class="bi bi-calendar-event me-1"></i> Entry Date
            </label>
            <div class="input-group shadow-sm">
              <input
                type="text"
                class="form-control form-control-sm datetimepicker"
                id="entryDate"
                placeholder="DD MMM YYYY"
                [formControl]="entryDate"
                name="entryDate"
                bsDatepicker
                #entryDatePicker="bsDatepicker"
                [bsConfig]="datepickerConfig"
                autocomplete="off"
                autocorrect="off"
                spellcheck="false"
              />
              <button
                class="btn btn-outline-secondary btn-sm"
                type="button"
                (click)="openCalendar('entryDatePicker')"
              >
                <i class="bi bi-calendar3"></i>
              </button>
            </div>
            <div
              *ngIf="
                (entryDate?.touched || entryDate?.dirty) &&
                (entryDate?.invalid || entryDate?.errors)
              "
              class="mt-1"
            >
              <small class="text-danger">Entry Date is required</small>
            </div>
          </div>
        </div>

        <!-- Patient Consent Type -->
        <div class="col-md-6">
          <div class="mb-3">
            <kt-select-search
              [items]="[
                { name: 'Verbal', value: 'Verbal' },
                { name: 'Written', value: 'Written' },
                { name: 'Both', value: 'Both' }
              ]"
              label="Patient Consent Type"
              placeholder="Select Patient Consent Type"
              [formControl]="
                asFormControl(patientConsenForm.get('consentType'))
              "
              [required]="false"
            />
          </div>
        </div>

        <!-- Dietician Remarks -->
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label fw-semibold">
              <i class="bi bi-journal-text me-1"></i> Dietician Remarks
            </label>
            <textarea
              rows="3"
              class="form-control shadow-sm"
              [formControl]="dieticianRemarks"
              placeholder="Any additional notes"
            ></textarea>
          </div>
        </div>

        <!-- Patient Questions -->
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label fw-semibold">
              <i class="bi bi-question-circle me-1"></i> Patient Questions
            </label>
            <textarea
              rows="3"
              class="form-control shadow-sm"
              [formControl]="patientQuestions"
              placeholder="Any patient questions logged"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
