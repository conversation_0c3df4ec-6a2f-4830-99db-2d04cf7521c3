<div class="row aos" data-aos="fade-up">
  <!-- Widgets, Appointments List -->
  <div class="row">
    <!-- Widgets -->
    <div class="col-md-4">
      <div class="dashboard-box-col w-100">
        <div class="dashboard-widget-box">
          <div class="dashboard-content-info">
            <h6>Total Patient</h6>
            <h4>{{ allPatientsCount }}</h4>
            <span class="text-success"
              ><i class="fa-solid fa-arrow-up"></i>15% From Last Week</span
            >
          </div>
          <div class="dashboard-widget-icon">
            <span class="dash-icon-box"
              ><i class="fa-solid fa-user-injured"></i
            ></span>
          </div>
        </div>
        <div class="dashboard-widget-box">
          <div class="dashboard-content-info">
            <h6>Total Doctors</h6>
            <h4>{{ allDoctorsCount }}</h4>
            <span class="text-success"
              ><i class="fa-solid fa-arrow-up"></i>20% From Yesterday</span
            >
          </div>
          <div class="dashboard-widget-icon">
            <span class="dash-icon-box"
              ><i class="fa-solid fa-user-doctor"></i
            ></span>
          </div>
        </div>
        <div class="dashboard-widget-box">
          <div class="dashboard-content-info">
            <h6>Patients Today</h6>
            <h4>{{ todayPatientsCount }}</h4>
            <span class="text-danger"
              ><i class="fa-solid fa-arrow-up"></i>15% From Yesterday</span
            >
          </div>
          <div class="dashboard-widget-icon">
            <span class="dash-icon-box"
              ><i class="fa-solid fa-user-clock"></i
            ></span>
          </div>
        </div>
      </div>
    </div>
    <!-- Appointments List  -->
    <div class="col-md-8">
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head">
          <div
            class="header-title d-flex justify-content-between align-items-center w-100"
          >
            <h5>Appointments List</h5>
            <div>
              <a
                class="btn btn-primary float-end mx-2"
                (click)="viewAllAppointments()"
              >
                Expand
              </a>
            </div>
          </div>
        </div>

        <div class="dashboard-card-body">
          <!-- Tabs Header -->
          <ul class="nav nav-tabs" role="tablist">
            <li class="nav-item" role="presentation">
              <a
                class="nav-link active"
                data-bs-toggle="tab"
                href="#upcoming"
                role="tab"
                >Upcoming</a
              >
            </li>
            <li class="nav-item" role="presentation">
              <a class="nav-link" data-bs-toggle="tab" href="#past" role="tab"
                >Past</a
              >
            </li>
          </ul>

          <!-- Tabs Content -->
          <div class="tab-content">
            <!-- Upcoming Appointments -->
            <div
              class="tab-pane fade show active"
              id="upcoming"
              role="tabpanel"
            >
              <LoadingSpinner
                [isLoading]="getUpcomingLimitAppointments.isLoading()"
              ></LoadingSpinner>

              <PageWithSearchPagination
                *ngIf="
                  getUpcomingLimitAppointments.isSuccess() &&
                  getUpcomingLimitAppointments.data()?.length
                "
                [data]="getUpcomingLimitAppointments.data()"
                (filteredDataChange)="filteredUpcomingAppointments = $event"
                [isPagination]="true"
                [itemsPerPage]="3"
                [isSearch]="true"
                searchPlaceHolder="Search by Patient Name"
                [isItemsPerPage]="false"
              >
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead class="table-light d-none">
                      <tr>
                        <th class="border-0 ps-4">
                          <i class="fas fa-user me-2 text-muted"></i>Patient
                          Information
                        </th>
                        <th class="border-0">
                          <i class="fas fa-calendar-alt me-2 text-muted"></i
                          >Appointment Details
                        </th>
                        <th class="border-0">
                          <i class="fas fa-info-circle me-2 text-muted"></i
                          >Status
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let app of filteredUpcomingAppointments;
                          let i = index
                        "
                        (click)="onAppointmentClick(app)"
                        class="cursor-pointer appointment-row"
                        [style.animation-delay]="i * 0.1 + 's'"
                      >
                        <td class="ps-4 py-3">
                          <div class="d-flex align-items-center">
                            <div class="position-relative me-3">
                              <img
                                [src]="
                                  app.photoLink ||
                                  'assets/img/default-avatar.png'
                                "
                                [alt]="app.patientName + ' Avatar'"
                                class="rounded-circle border border-2 border-light shadow-sm"
                                style="
                                  width: 50px;
                                  height: 50px;
                                  object-fit: cover;
                                "
                                onerror="this.src='assets/img/default-avatar.png'"
                              />
                              <span
                                class="position-absolute bottom-0 end-0 translate-middle p-1 bg-success border border-white rounded-circle"
                              >
                                <span class="visually-hidden">Online</span>
                              </span>
                            </div>
                            <div class="patient-details">
                              <h6 class="mb-1 fw-semibold text-dark">
                                {{ app.patientName || "N/A" }}
                              </h6>
                              <div class="d-flex align-items-center">
                                <i
                                  class="fas fa-hospital text-muted me-1"
                                  style="font-size: 12px"
                                ></i>
                                <span class="text-muted small">
                                  {{
                                    app.facilityName
                                      ? (app.facilityName | titlecase)
                                      : "No Facility"
                                  }}
                                </span>
                              </div>
                            </div>
                          </div>
                        </td>
                        <td class="py-3">
                          <div class="appointment-info">
                            <div class="d-flex align-items-center mb-1">
                              <i class="fas fa-calendar text-primary me-2"></i>
                              <span class="fw-medium text-dark">
                                {{
                                  app.appointmentDate
                                    ? (app.appointmentDate
                                      | date : "MMM dd, yyyy")
                                    : "N/A"
                                }}
                              </span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                              <i class="fas fa-clock text-success me-2"></i>
                              <span class="text-muted">
                                {{
                                  app.appointmentTime
                                    ? convertTo12HourFormat(app.appointmentTime)
                                    : "N/A"
                                }}
                              </span>
                            </div>
                            <span
                              class="badge rounded-pill px-3 py-1 fw-normal"
                              [ngClass]="{
                                'bg-primary':
                                  app.appointmentType?.toLowerCase() ===
                                  'clinic',
                                'bg-success':
                                  app.appointmentType?.toLowerCase() ===
                                  'online',
                                'bg-warning':
                                  app.appointmentType?.toLowerCase() ===
                                  'nurse',
                                'bg-info':
                                  app.appointmentType?.toLowerCase() ===
                                  'hospital',
                                'bg-danger':
                                  app.appointmentType?.toLowerCase() ===
                                  'sample',
                                'bg-secondary': app.appointmentType
                              }"
                            >
                              <i class="fas fa-stethoscope me-1"></i>
                              {{
                                app.appointmentType
                                  ? (app.appointmentType | titlecase)
                                  : "N/A"
                              }}
                            </span>
                          </div>
                        </td>

                        <td class="py-3">
                          <div class="d-flex align-items-center">
                            <div class="status-indicator me-2">
                              <span
                                class="badge bg-warning text-dark rounded-pill px-3 py-2"
                              >
                                <i class="fas fa-clock me-1"></i>
                                {{
                                  app.appointmentStatus
                                    ? (app.appointmentStatus | titlecase)
                                    : "N/A"
                                }}
                              </span>
                            </div>
                          </div>
                          <small class="text-muted d-block mt-1">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ app.facilityName ? "On-site" : "Virtual" }}
                          </small>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </PageWithSearchPagination>

              <ErrorAlert
                error="No Upcoming Appointments Found"
                *ngIf="getUpcomingLimitAppointments.isError()"
                color="blue"
              ></ErrorAlert>
            </div>

            <!-- Past Appointments -->
            <div class="tab-pane fade" id="past" role="tabpanel">
              <LoadingSpinner
                [isLoading]="getPastLimitAppointments.isLoading()"
              ></LoadingSpinner>

              <PageWithSearchPagination
                *ngIf="
                  getPastLimitAppointments.isSuccess() &&
                  getPastLimitAppointments.data()?.length
                "
                [data]="getPastLimitAppointments.data()"
                (filteredDataChange)="filteredPastAppointments = $event"
                [isPagination]="true"
                [itemsPerPage]="3"
                [isSearch]="true"
                searchPlaceHolder="Search by Patient Name"
                [isItemsPerPage]="false"
              >
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead class="table-light d-none">
                      <tr>
                        <th class="border-0 ps-4">
                          <i class="fas fa-user me-2 text-muted"></i>Patient
                          Information
                        </th>
                        <th class="border-0">
                          <i class="fas fa-calendar-alt me-2 text-muted"></i
                          >Appointment Details
                        </th>
                        <th class="border-0">
                          <i class="fas fa-info-circle me-2 text-muted"></i
                          >Status
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let app of filteredPastAppointments;
                          let i = index
                        "
                        (click)="onAppointmentClick(app)"
                        class="cursor-pointer appointment-row"
                        [style.animation-delay]="i * 0.1 + 's'"
                      >
                        <td class="ps-4 py-3">
                          <div class="d-flex align-items-center">
                            <div class="position-relative me-3">
                              <img
                                [src]="
                                  app.photoLink ||
                                  'assets/img/default-avatar.png'
                                "
                                [alt]="app.patientName + ' Avatar'"
                                class="rounded-circle border border-2 border-light shadow-sm"
                                style="
                                  width: 50px;
                                  height: 50px;
                                  object-fit: cover;
                                "
                                onerror="this.src='assets/img/default-avatar.png'"
                              />
                              <span
                                class="position-absolute bottom-0 end-0 translate-middle p-1 bg-success border border-white rounded-circle"
                              >
                                <span class="visually-hidden">Online</span>
                              </span>
                            </div>
                            <div class="patient-details">
                              <h6 class="mb-1 fw-semibold text-dark">
                                {{ app.patientName || "N/A" }}
                              </h6>
                              <div class="d-flex align-items-center">
                                <i
                                  class="fas fa-hospital text-muted me-1"
                                  style="font-size: 12px"
                                ></i>
                                <span class="text-muted small">
                                  {{
                                    app.facilityName
                                      ? (app.facilityName | titlecase)
                                      : "No Facility"
                                  }}
                                </span>
                              </div>
                            </div>
                          </div>
                        </td>
                        <td class="py-3">
                          <div class="appointment-info">
                            <div class="d-flex align-items-center mb-1">
                              <i class="fas fa-calendar text-primary me-2"></i>
                              <span class="fw-medium text-dark">
                                {{
                                  app.appointmentDate
                                    ? (app.appointmentDate
                                      | date : "MMM dd, yyyy")
                                    : "N/A"
                                }}
                              </span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                              <i class="fas fa-clock text-success me-2"></i>
                              <span class="text-muted">
                                {{
                                  app.appointmentTime
                                    ? convertTo12HourFormat(app.appointmentTime)
                                    : "N/A"
                                }}
                              </span>
                            </div>
                            <span
                              class="badge rounded-pill px-3 py-1 fw-normal"
                             [ngClass]="{
                                'bg-primary':
                                  app.appointmentType?.toLowerCase() ===
                                  'clinic',
                                'bg-success':
                                  app.appointmentType?.toLowerCase() ===
                                  'online',
                                'bg-warning':
                                  app.appointmentType?.toLowerCase() ===
                                  'nurse',
                                'bg-info':
                                  app.appointmentType?.toLowerCase() ===
                                  'hospital',
                                'bg-danger':
                                  app.appointmentType?.toLowerCase() ===
                                  'sample',
                                'bg-secondary': app.appointmentType
                              }"
                            >
                              <i class="fas fa-stethoscope me-1"></i>
                              {{
                                app.appointmentType
                                  ? (app.appointmentType | titlecase)
                                  : "N/A"
                              }}
                            </span>
                          </div>
                        </td>

                        <td class="py-3">
                          <div class="d-flex align-items-center">
                            <div class="status-indicator me-2">
                              <span
                                class="badge bg-warning text-dark rounded-pill px-3 py-2"
                              >
                                <i class="fas fa-clock me-1"></i>
                                {{
                                  app.appointmentStatus
                                    ? (app.appointmentStatus | titlecase)
                                    : "N/A"
                                }}
                              </span>
                            </div>
                          </div>
                          <small class="text-muted d-block mt-1">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ app.facilityName ? "On-site" : "Virtual" }}
                          </small>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!-- 🔴 Reuse your table HTML here with `filteredPast` -->
              </PageWithSearchPagination>

              <ErrorAlert
                error="No Past Appointments Found"
                *ngIf="getPastLimitAppointments.isError()"
                color="blue"
              ></ErrorAlert>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Weekly Overview, Followups -->
  <div class="row">
    <!-- Weekly Overview -->
    <div class="col-4">
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head border-0">
          <div class="header-title">
            <h5>Weekly Overview</h5>
          </div>
          <!-- <div class="chart-create-date">
            <h6>Mar 14 - Mar 21</h6>
          </div> -->
        </div>
        <div class="dashboard-card-body">
          <div class="chart-tab">
            <ul
              class="nav nav-pills product-licence-tab"
              id="pills-tab2"
              role="tablist"
            >
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link active"
                  id="pills-revenue-tab"
                  data-bs-toggle="pill"
                  data-bs-target="#pills-revenue"
                  type="button"
                  role="tab"
                  aria-controls="pills-revenue"
                  aria-selected="false"
                >
                  Revenue
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link"
                  id="pills-appointment-tab"
                  data-bs-toggle="pill"
                  data-bs-target="#pills-appointment"
                  type="button"
                  role="tab"
                  aria-controls="pills-appointment"
                  aria-selected="true"
                >
                  Appointments
                </button>
              </li>
            </ul>
            <div class="tab-content w-100" id="v-pills-tabContent">
              <div
                class="tab-pane fade show active"
                id="pills-revenue"
                role="tabpanel"
                aria-labelledby="pills-revenue-tab"
              >
                <div *ngIf="facilityWeeklyRevenue.isError()">
                  Error fetching data...
                </div>

                <div
                  *ngIf="
                    facilityWeeklyAppointments.isPending() &&
                    facilityWeeklyRevenue.isPending()
                  "
                >
                  Loading...
                </div>

                <div
                  *ngIf="
                    facilityWeeklyRevenue.isSuccess() &&
                    facilityWeeklyRevenue.data()
                  "
                >
                  <div
                    id="revenue-chart"
                    *ngIf="facilityWeeklyRevenue.data().length > 0"
                  >
                    <apx-chart
                      [series]="chartOptions1.series"
                      [chart]="chartOptions1.chart"
                      [dataLabels]="chartOptions1.dataLabels"
                      [plotOptions]="chartOptions1.plotOptions"
                      [xaxis]="chartOptions1.xaxis"
                    ></apx-chart>
                  </div>

                  <div *ngIf="facilityWeeklyRevenue.data().length == 0">
                    No Recent Revenue
                  </div>
                </div>
              </div>

              <div
                class="tab-pane fade"
                id="pills-appointment"
                role="tabpanel"
                aria-labelledby="pills-appointment-tab"
              >
                <div *ngIf="facilityWeeklyAppointments.isError()">
                  Error fetching data...
                </div>

                <div *ngIf="facilityWeeklyAppointments.isPending()">
                  Loading...
                </div>

                <div
                  *ngIf="
                    facilityWeeklyAppointments.isSuccess() &&
                    facilityWeeklyAppointments.data()
                  "
                >
                  <div
                    id="appointment-chart"
                    *ngIf="facilityWeeklyAppointments.data().length > 0"
                  >
                    <apx-chart
                      [series]="chartOptions2.series"
                      [chart]="chartOptions2.chart"
                      [dataLabels]="chartOptions2.dataLabels"
                      [plotOptions]="chartOptions2.plotOptions"
                      [xaxis]="chartOptions2.xaxis"
                    ></apx-chart>
                  </div>
                  <div *ngIf="facilityWeeklyAppointments.data().length == 0">
                    No Recent Appointments
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Follow ups -->
    <div class="col-8">
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head border-0">
          <div class="header-title">
            <h5>Follow ups ({{ refillList && refillList.length }})</h5>
          </div>
        </div>
        <div class="dashboard-card-body p-0">
          <div class="search-header d-flex align-items-baseline gap-2 gap-lg-5">
            <div class="search-field">
              <select
                name="selectPageCount"
                id="selectPageCount"
                class="form-control"
                [(ngModel)]="itemsPerPage"
                (change)="onItemsPerPageChange()"
              >
                <option value="5" selected>
                  5 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                </option>
                <option value="10">
                  10 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                </option>
                <option value="15">
                  15 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                </option>
                <option value="25">
                  25 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                </option>
                <option value="50">
                  50 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                </option>
                <option value="50">
                  100 &nbsp;&nbsp;&nbsp;&nbsp; Items Per Page
                </option>
              </select>
            </div>
            <div class="search-field flex-grow-1">
              <input
                type="text"
                class="form-control"
                placeholder="Search"
                [(ngModel)]="searchQuery"
                (input)="onSearchChange()"
              />
              <span class="search-icon"
                ><i class="fa-solid fa-magnifying-glass"></i
              ></span>
            </div>
          </div>
          <div class="custom-table p-0">
            <div class="table-responsive">
              <table class="table table-center mb-0">
                <thead>
                  <tr>
                    <th>S.No</th>
                    <th>Facility Name</th>
                    <th>Patient Name</th>
                    <th>Location</th>
                    <!-- <th>Prescribed by</th> -->
                    <!-- <th>Follow-Up Date</th> -->
                    <th>Prescription Date</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container
                    *ngIf="
                      filteredRefillList && filteredRefillList.length;
                      else noUsers
                    "
                  >
                    <tr
                      *ngFor="
                        let refill of filteredRefillList;
                        trackBy: trackByRefillId;
                        let i = index
                      "
                    >
                      <td>
                        <a
                          href="javascript:void(0);"
                          class="text-blue-600"
                          data-bs-toggle="modal"
                          data-bs-target="#invoice_view"
                          >{{ i + 1 }}</a
                        >
                      </td>
                      <td>
                        {{ refill.facilityName ? refill.facilityName : "N/A" }}
                      </td>
                      <td>
                        <a
                          style="color: blue; font-weight: 600"
                          (click)="viewPatientProfile(refill)"
                        >
                          {{ refill.patientName ? refill.patientName : "N/A" }}
                        </a>
                      </td>

                      <td>
                        {{ refill.cityName ? refill.cityName : "N/A" }}
                      </td>
                      <!-- <td>N/A</td> -->
                      <td>
                        {{
                          refill.prescriptionDate
                            ? (refill.prescriptionDate | date : "dd MMM YYYY")
                            : "N/A"
                        }}
                      </td>
                      <td>
                        <button
                          class="btn btn-primary"
                          title="Refill"
                          (click)="onReviewClick(refill)"
                        >
                          Review
                        </button>
                      </td>
                    </tr>
                  </ng-container>
                  <ng-template #noUsers>
                    <tr>
                      <td colspan="7" class="text-center">
                        No Record Available
                      </td>
                    </tr>
                  </ng-template>
                </tbody>
              </table>
            </div>
          </div>
          <!-- Pagination -->
          <div
            class="pagination-container d-flex justify-content-center align-items-center mt-3"
          >
            <ul class="pagination custom-pagination mb-0">
              <!-- Previous Button -->
              <li
                class="page-item"
                [class.disabled]="currentPage === 1"
                (click)="goToPage(currentPage - 1)"
              >
                <a class="page-link">
                  <i class="fa fa-angle-left me-1"></i> Prev
                </a>
              </li>

              <!-- Page Numbers -->
              <li
                *ngFor="let page of visiblePages"
                class="page-item"
                [class.active]="page === currentPage"
                [class.disabled]="page === '...'"
                (click)="page !== '...' && goToPage(page)"
              >
                <a class="page-link">{{ page }}</a>
              </li>

              <!-- Next Button -->
              <li
                class="page-item"
                [class.disabled]="currentPage === totalPages()"
                (click)="goToPage(currentPage + 1)"
              >
                <a class="page-link">
                  Next <i class="fa fa-angle-right ms-1"></i>
                </a>
              </li>
            </ul>
          </div>
          <!-- /Pagination -->
        </div>
      </div>
    </div>
  </div>
  <!-- Recent Patients, Upcoming Appointments -->
  <div class="row">
    <div class="col-6">
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head">
          <div class="header-title">
            <h5>Recent Patients</h5>
          </div>
        </div>
        <div
          class="dashboard-card-body"
          *ngIf="recentPatients && recentPatients.length"
        >
          <div
            class="d-flex recent-patient-grid-boxes overflow-auto"
            *ngIf="!loader['recentPatients']"
          >
            <div
              class="recent-patient-grid"
              *ngFor="let patient of recentPatients"
              (click)="
                viewVisitDetails(
                  patient?.patientId,
                  patient?.visitId,
                  patient?.doctorId
                )
              "
            >
              <a class="patient-img">
                <img
                  src="assets/images/avatar-image.webp"
                  alt="Avatar image"
                  class="img-thumbnail"
                  *ngIf="!patient.photoLink"
                />
                <img
                  [src]="patient.photoLink"
                  alt="Avatar image"
                  class="img-thumbnail"
                  *ngIf="patient.photoLink"
                />
              </a>
              <h5>
                <a>{{ patient.patientName || "N/A" }}</a>
              </h5>
              <span>Patient ID : {{ patient.patientId || "N/A" }}</span>
            </div>
          </div>
          <div
            class="d-flex justify-content-center align-items-center"
            *ngIf="loader['recentPatients']"
          >
            Loading...
          </div>
        </div>
        <div *ngIf="recentPatients && recentPatients.length <= 0">
          No Patients
        </div>
      </div>
    </div>
    <div class="col-6">
      <div class="dashboard-card w-100">
        <div
          class="upcoming-appointment-card"
          *ngIf="getUpcomingData() && getUpcomingData().length"
        >
          <div class="title-card">
            <h5>Upcoming Appointment</h5>
          </div>
          <div class="upcoming-patient-info">
            <div class="info-details">
              <span class="img-avatar">
                <!-- <img
              src="assets/img/doctors-dashboard/profile-01.jpg"
              alt="Img" /> -->
                <img
                  src="assets/images/avatar-image.webp"
                  alt="Avatar image"
                  class="img-thumbnail"
                  *ngIf="!getUpcomingData()[0].photoLink" />
                <img
                  [src]="getUpcomingData()[0].photoLink"
                  alt="Avatar image"
                  class="img-thumbnail"
                  *ngIf="getUpcomingData()[0].photoLink"
              /></span>
              <div class="name-info">
                <span>#{{ getUpcomingData()[0].location || "N/A" }}</span>
                <h6>{{ getUpcomingData()[0].doctorName || "N/A" }}</h6>
              </div>
            </div>
            <div class="date-details">
              <span>General visit</span>
              <h6>
                {{
                  getUpcomingData()[0].appointmentDate
                    ? (getUpcomingData()[0].appointmentDate
                      | date : "dd MMM yyyy")
                    : "N/A"
                }}
                {{
                  getUpcomingData()[0].appointmentTime
                    ? convertTo12HourFormat(
                        getUpcomingData()[0].appointmentTime
                      )
                    : "N/A"
                }}
              </h6>
            </div>
            <div class="circle-bg">
              <img src="assets/img/bg/dashboard-circle-bg.png" alt="" />
            </div>
          </div>
          <div class="appointment-card-footer">
            <h5 *ngIf="getUpcomingData()[0].appointmentType === 'online'">
              <i class="fa-solid fa-video"></i>Video Appointment
            </h5>
            <h5 *ngIf="getUpcomingData()[0].appointmentType === 'clinic'">
              <i class="fa-solid fa-clinic-medical"></i>Clinic Appointment
            </h5>
            <div class="btn-getUpcomingData()">
              <!-- <a  class="btn">Chat Now</a> -->
              <a
                class="btn"
                [routerLink]="[
                  '/admin/manager/view-appointment',
                  getUpcomingData()[0].id
                ]"
                >View Appointment</a
              >
            </div>
          </div>
        </div>
        <div
          class="upcoming-appointment-card"
          *ngIf="!getUpcomingData() || !getUpcomingData().length"
        >
          No Upcoming Appointments
        </div>
      </div>
    </div>
  </div>
  <!--  Doctors List, Patients List -->
  <div class="row">
    <!-- Doctors List -->
    <div class="col-12" *ngIf="allDoctorsList.length > 0">
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head border-0">
          <div class="header-title">
            <h5>
              Doctors List ({{ allDoctorsList && allDoctorsList.length }})
            </h5>
          </div>
        </div>
        <div class="dashboard-card-body">
          <div class="custom-table border-0">
            <app-dynamic-table
              [dataList]="allDoctorsList"
              [columns]="doctorsCols"
              [showSerialNumber]="false"
              [showActions]="false"
              [isDataLoading]="loader['doctors']"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- Patients List -->
    <div class="col-12" *ngIf="allPatientsList.length > 0">
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head border-0">
          <div class="header-title">
            <h5>
              Patients List ({{ allPatientsList && allPatientsList.length }})
            </h5>
          </div>
        </div>
        <div class="dashboard-card-body border-0">
          <div class="custom-table border-0">
            <app-dynamic-table
              [dataList]="allPatientsList"
              [columns]="patientsCols"
              [showSerialNumber]="false"
              [showActions]="false"
              [isDataLoading]="loader['patients']"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Clinics/Hospitals -->
  <div class="row d-none">
    <div class="col-12 d-flex flex-column">
      <div class="dashboard-card w-100 shadow-sm rounded-3 bg-white border">
        <div
          class="dashboard-card-head border-bottom px-3 py-2 bg-primary-subtle rounded-top"
        >
          <div class="header-title">
            <h5 class="mb-0 text-dark fw-semibold">Hospital Details</h5>
          </div>
        </div>

        <div
          class="dashboard-card-body p-3"
          *ngIf="clinics && clinics.length; else noClinics"
        >
          <div class="clinic-available mb-3" *ngFor="let clinic of clinics">
            <div
              class="clinic-head p-3 bg-light rounded-3 border d-flex justify-content-between align-items-center shadow-sm"
            >
              <!-- Image + Name -->
              <div class="d-flex align-items-center gap-3 flex-grow-1">
                <img
                  src="assets/images/clinic.webp"
                  alt="Clinic"
                  class="rounded-2 shadow-sm"
                  style="width: 60px; height: 60px; object-fit: cover"
                />
                <div>
                  <h6 class="mb-1 fw-semibold text-dark">
                    {{ clinic.locationName }}
                  </h6>
                  <span class="text-primary small"
                    >#{{ clinic.locationType }}</span
                  >
                </div>
              </div>

              <!-- Address -->
              <div class="flex-grow-1 text-muted" style="font-size: 1rem">
                <strong class="text-dark">Address:</strong>
                {{ clinic.address || "N/A" }}, {{ clinic.cityName || "N/A" }},
                {{ clinic.stateName || "N/A" }},
                {{ clinic.countryName || "N/A" }} -
                {{ clinic.pincode || "N/A" }}
              </div>

              <!-- Fee -->
              <div class="clinic-charge text-end">
                <span
                  class="badge bg-primary text-white px-3 py-2"
                  style="font-size: 1rem"
                >
                  ₹ {{ clinic.consultationFee }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- No Clinics Template -->
        <ng-template #noClinics>
          <div class="clinic-available p-4 text-center text-muted">
            <h6 class="mb-0">No Clinics Found</h6>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</div>

<!-- <div class="row aos" data-aos="fade-up">
  <div class="col-xl-4 d-flex">
    <div class="dashboard-box-col w-100">
      <div class="dashboard-widget-box">
        <div class="dashboard-content-info">
          <h6>Total Patient</h6>
          <h4>978</h4>
          <span class="text-success"
            ><i class="fa-solid fa-arrow-up"></i>15% From Last Week</span
          >
        </div>
        <div class="dashboard-widget-icon">
          <span class="dash-icon-box"
            ><i class="fa-solid fa-user-injured"></i
          ></span>
        </div>
      </div>
      <div class="dashboard-widget-box">
        <div class="dashboard-content-info">
          <h6>Patients Today</h6>
          <h4>80</h4>
          <span class="text-danger"
            ><i class="fa-solid fa-arrow-up"></i>15% From Yesterday</span
          >
        </div>
        <div class="dashboard-widget-icon">
          <span class="dash-icon-box"
            ><i class="fa-solid fa-user-clock"></i
          ></span>
        </div>
      </div>
      <div class="dashboard-widget-box">
        <div class="dashboard-content-info">
          <h6>Appointments Today</h6>
          <h4>50</h4>
          <span class="text-success"
            ><i class="fa-solid fa-arrow-up"></i>20% From Yesterday</span
          >
        </div>
        <div class="dashboard-widget-icon">
          <span class="dash-icon-box"
            ><i class="fa-solid fa-calendar-days"></i
          ></span>
        </div>
      </div>
    </div>
  </div>
  <div class="col-xl-8 d-flex">
    <div class="dashboard-card w-100">
      <div class="dashboard-card-head">
        <div class="header-title">
          <h5>Appointment</h5>
        </div>
        <div class="dropdown header-dropdown">
          <a
            class="dropdown-toggle nav-tog"
            data-bs-toggle="dropdown"
            href="javascript:void(0);"
          >
            Last 7 Days
          </a>
          <div class="dropdown-menu dropdown-menu-end">
            <a href="javascript:void(0);" class="dropdown-item"> Today </a>
            <a href="javascript:void(0);" class="dropdown-item"> This Month </a>
            <a href="javascript:void(0);" class="dropdown-item">
              Last 7 Days
            </a>
          </div>
        </div>
      </div>
      <div class="dashboard-card-body">
        <div class="table-responsive">
          <table class="table dashboard-table appoint-table">
            <tbody>
              <tr>
                <td>
                  <div class="patient-info-profile">
                    <a
                      routerLink="routes.appointmentsListUser"
                      class="table-avatar"
                    >
                      <img
                        src="assets/img/doctors-dashboard/profile-01.jpg"
                        alt="Img"
                      />
                    </a>
                    <div class="patient-name-info">
                      <span>#Apt0001</span>
                      <h5>
                        <a routerLink="routes.appointmentsListUser"
                          >Adrian Marshall</a
                        >
                      </h5>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <h6>11 Nov 2024 10.45 AM</h6>
                    <span class="badge table-badge">General</span>
                  </div>
                </td>
                <td>
                  <div class="apponiment-actions d-flex align-items-center">
                    <a href="javascript:void(0);" class="text-success me-2"
                      ><i class="fa-solid fa-check"></i
                    ></a>
                    <a href="javascript:void(0);" class="text-danger"
                      ><i class="fa-solid fa-xmark"></i
                    ></a>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="patient-info-profile">
                    <a
                      routerLink="routes.appointmentsListUser"
                      class="table-avatar"
                    >
                      <img
                        src="assets/img/doctors-dashboard/profile-02.jpg"
                        alt="Img"
                      />
                    </a>
                    <div class="patient-name-info">
                      <span>#Apt0002</span>
                      <h5>
                        <a routerLink="routes.appointmentsListUser"
                          >Kelly Stevens</a
                        >
                      </h5>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <h6>10 Nov 2024 11.00 AM</h6>
                    <span class="badge table-badge">Clinic Consulting</span>
                  </div>
                </td>
                <td>
                  <div class="apponiment-actions d-flex align-items-center">
                    <a href="javascript:void(0);" class="text-success me-2"
                      ><i class="fa-solid fa-check"></i
                    ></a>
                    <a href="javascript:void(0);" class="text-danger"
                      ><i class="fa-solid fa-xmark"></i
                    ></a>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="patient-info-profile">
                    <a
                      routerLink="routes.appointmentsListUser"
                      class="table-avatar"
                    >
                      <img
                        src="assets/img/doctors-dashboard/profile-03.jpg"
                        alt="Img"
                      />
                    </a>
                    <div class="patient-name-info">
                      <span>#Apt0003</span>
                      <h5>
                        <a routerLink="routes.appointmentsListUser"
                          >Samuel Anderson</a
                        >
                      </h5>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <h6>03 Nov 2024 02.00 PM</h6>
                    <span class="badge table-badge">General</span>
                  </div>
                </td>
                <td>
                  <div class="apponiment-actions d-flex align-items-center">
                    <a href="javascript:void(0);" class="text-success me-2"
                      ><i class="fa-solid fa-check"></i
                    ></a>
                    <a href="javascript:void(0);" class="text-danger"
                      ><i class="fa-solid fa-xmark"></i
                    ></a>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="patient-info-profile">
                    <a
                      routerLink="routes.appointmentsListUser"
                      class="table-avatar"
                    >
                      <img
                        src="assets/img/doctors-dashboard/profile-04.jpg"
                        alt="Img"
                      />
                    </a>
                    <div class="patient-name-info">
                      <span>#Apt0004</span>
                      <h5>
                        <a routerLink="routes.appointmentsListUser"
                          >Catherine Griffin</a
                        >
                      </h5>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <h6>01 Nov 2024 04.00 PM</h6>
                    <span class="badge table-badge">Clinic Consulting</span>
                  </div>
                </td>
                <td>
                  <div class="apponiment-actions d-flex align-items-center">
                    <a href="javascript:void(0);" class="text-success me-2"
                      ><i class="fa-solid fa-check"></i
                    ></a>
                    <a href="javascript:void(0);" class="text-danger"
                      ><i class="fa-solid fa-xmark"></i
                    ></a>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="patient-info-profile">
                    <a
                      routerLink="routes.appointmentsListUser"
                      class="table-avatar"
                    >
                      <img
                        src="assets/img/doctors-dashboard/profile-05.jpg"
                        alt="Img"
                      />
                    </a>
                    <div class="patient-name-info">
                      <span>#Apt0005</span>
                      <h5>
                        <a routerLink="routes.appointmentsListUser"
                          >Robert Hutchinson</a
                        >
                      </h5>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="appointment-date-created">
                    <h6>28 Oct 2024 05.30 PM</h6>
                    <span class="badge table-badge">General</span>
                  </div>
                </td>
                <td>
                  <div class="apponiment-actions d-flex align-items-center">
                    <a href="javascript:void(0);" class="text-success me-2"
                      ><i class="fa-solid fa-check"></i
                    ></a>
                    <a href="javascript:void(0);" class="text-danger"
                      ><i class="fa-solid fa-xmark"></i
                    ></a>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="col-xl-5 d-flex">
    <div class="dashboard-chart-col w-100">
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head border-0">
          <div class="header-title">
            <h5>Weekly Overview</h5>
          </div>
          <div class="chart-create-date">
            <h6>Mar 14 - Mar 21</h6>
          </div>
        </div>
        <div class="dashboard-card-body">
          <div class="chart-tab">
            <ul
              class="nav nav-pills product-licence-tab"
              id="pills-tab2"
              role="tablist"
            >
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link active"
                  id="pills-revenue-tab"
                  data-bs-toggle="pill"
                  data-bs-target="#pills-revenue"
                  type="button"
                  role="tab"
                  aria-controls="pills-revenue"
                  aria-selected="false"
                >
                  Revenue
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button
                  class="nav-link"
                  id="pills-appointment-tab"
                  data-bs-toggle="pill"
                  data-bs-target="#pills-appointment"
                  type="button"
                  role="tab"
                  aria-controls="pills-appointment"
                  aria-selected="true"
                >
                  Appointments
                </button>
              </li>
            </ul>
            <div class="tab-content w-100" id="v-pills-tabContent">
              <div
                class="tab-pane fade show active"
                id="pills-revenue"
                role="tabpanel"
                aria-labelledby="pills-revenue-tab"
              >
                <div id="revenue-chart">
                  <apx-chart
                    [series]="chartOptions1.series"
                    [chart]="chartOptions1.chart"
                    [dataLabels]="chartOptions1.dataLabels"
                    [plotOptions]="chartOptions1.plotOptions"
                    [xaxis]="chartOptions1.xaxis"
                  ></apx-chart>
                </div>
              </div>
              <div
                class="tab-pane fade"
                id="pills-appointment"
                role="tabpanel"
                aria-labelledby="pills-appointment-tab"
              >
                <div id="appointment-chart">
                  <apx-chart
                    [series]="chartOptions2.series"
                    [chart]="chartOptions2.chart"
                    [dataLabels]="chartOptions2.dataLabels"
                    [plotOptions]="chartOptions2.plotOptions"
                    [xaxis]="chartOptions2.xaxis"
                  ></apx-chart>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head">
          <div class="header-title">
            <h5>Recent Patients</h5>
          </div>
          <div class="card-view-link">
            <a routerLink="routes.myPatients">View All</a>
          </div>
        </div>
        <div class="dashboard-card-body">
          <div class="d-flex recent-patient-grid-boxes">
            <div class="recent-patient-grid">
              <a routerLink="routes.patientDetails" class="patient-img">
                <img
                  src="assets/img/doctors-dashboard/profile-01.jpg"
                  alt="Img"
                />
              </a>
              <h5>
                <a routerLink="routes.patientDetails">Adrian Marshall</a>
              </h5>
              <span>Patient ID : P0001</span>
              <div class="date-info">
                <h6>Last Appointment 15 Mar 2024</h6>
              </div>
            </div>
            <div class="recent-patient-grid">
              <a routerLink="routes.patientDetails" class="patient-img">
                <img
                  src="assets/img/doctors-dashboard/profile-02.jpg"
                  alt="Img"
                />
              </a>
              <h5><a routerLink="routes.patientDetails">Kelly Stevens</a></h5>
              <span>Patient ID : P0002</span>
              <div class="date-info">
                <h6>Last Appointment 13 Mar 2024</h6>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-xl-7 d-flex">
    <div class="dashboard-main-col w-100">
      <div class="upcoming-appointment-card">
        <div class="title-card">
          <h5>Upcoming Appointment</h5>
        </div>
        <div class="upcoming-patient-info">
          <div class="info-details">
            <span class="img-avatar"
              ><img src="assets/img/doctors-dashboard/profile-01.jpg" alt="Img"
            /></span>
            <div class="name-info">
              <span>#Apt0001</span>
              <h6>Adrian Marshall</h6>
            </div>
          </div>
          <div class="date-details">
            <span>General visit</span>
            <h6>Today, 10:45 AM</h6>
          </div>
          <div class="circle-bg">
            <img src="assets/img/bg/dashboard-circle-bg.png" alt="" />
          </div>
        </div>
        <div class="appointment-card-footer">
          <h5><i class="fa-solid fa-video"></i>Video Appointment</h5>
          <div class="btn-appointments">
            <a routerLink="routes.chatDoctor" class="btn">Chat Now</a>
            <a routerLink="routes.doctorAppointmentStart" class="btn"
              >Start Appointment</a
            >
          </div>
        </div>
      </div>
      <div class="dashboard-card w-100">
        <div class="dashboard-card-head">
          <div class="header-title">
            <h5>Recent Invoices</h5>
          </div>
          <div class="card-view-link">
            <a routerLink="routes.invoice">View All</a>
          </div>
        </div>
        <div class="dashboard-card-body">
          <div class="table-responsive">
            <table class="table dashboard-table">
              <tbody>
                <tr>
                  <td>
                    <div class="patient-info-profile">
                      <a routerLink="routes.invoice" class="table-avatar">
                        <img
                          src="assets/img/doctors-dashboard/profile-01.jpg"
                          alt="Img"
                        />
                      </a>
                      <div class="patient-name-info">
                        <h5>
                          <a routerLink="routes.invoice">Adrian</a>
                        </h5>
                        <span>#Apt0001</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="appointment-date-created">
                      <span class="paid-text">Amount</span>
                      <h6>$450</h6>
                    </div>
                  </td>
                  <td>
                    <div class="appointment-date-created">
                      <span class="paid-text">Paid On</span>
                      <h6>11 Nov 2024</h6>
                    </div>
                  </td>
                  <td>
                    <div class="apponiment-view d-flex align-items-center">
                      <a routerLink="routes.invoiceView"
                        ><i class="isax isax-eye4"></i
                      ></a>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="patient-info-profile">
                      <a href="javascript:void(0);" class="table-avatar">
                        <img
                          src="assets/img/doctors-dashboard/profile-02.jpg"
                          alt="Img"
                        />
                      </a>
                      <div class="patient-name-info">
                        <h5><a href="javascript:void(0);">Kelly</a></h5>
                        <span>#Apt0002</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="appointment-date-created">
                      <span class="paid-text">Paid On</span>
                      <h6>10 Nov 2024</h6>
                    </div>
                  </td>
                  <td>
                    <div class="appointment-date-created">
                      <span class="paid-text">Amount</span>
                      <h6>$500</h6>
                    </div>
                  </td>
                  <td>
                    <div class="apponiment-view d-flex align-items-center">
                      <a href="javascript:void(0);"
                        ><i class="isax isax-eye4"></i
                      ></a>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="patient-info-profile">
                      <a href="javascript:void(0);" class="table-avatar">
                        <img
                          src="assets/img/doctors-dashboard/profile-03.jpg"
                          alt="Img"
                        />
                      </a>
                      <div class="patient-name-info">
                        <h5>
                          <a href="javascript:void(0);">Samuel</a>
                        </h5>
                        <span>#Apt0003</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="appointment-date-created">
                      <span class="paid-text">Paid On</span>
                      <h6>03 Nov 2024</h6>
                    </div>
                  </td>
                  <td>
                    <div class="appointment-date-created">
                      <span class="paid-text">Amount</span>
                      <h6>$320</h6>
                    </div>
                  </td>
                  <td>
                    <div class="apponiment-view d-flex align-items-center">
                      <a href="javascript:void(0);"
                        ><i class="isax isax-eye4"></i
                      ></a>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="patient-info-profile">
                      <a href="javascript:void(0);" class="table-avatar">
                        <img
                          src="assets/img/doctors-dashboard/profile-04.jpg"
                          alt="Img"
                        />
                      </a>
                      <div class="patient-name-info">
                        <h5>
                          <a href="javascript:void(0);">Catherine</a>
                        </h5>
                        <span>#Apt0004</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="appointment-date-created">
                      <span class="paid-text">Paid On</span>
                      <h6>01 Nov 2024</h6>
                    </div>
                  </td>
                  <td>
                    <div class="appointment-date-created">
                      <span class="paid-text">Amount</span>
                      <h6>$240</h6>
                    </div>
                  </td>
                  <td>
                    <div class="apponiment-view d-flex align-items-center">
                      <a href="javascript:void(0);"
                        ><i class="isax isax-eye4"></i
                      ></a>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div class="patient-info-profile">
                      <a href="javascript:void(0);" class="table-avatar">
                        <img
                          src="assets/img/doctors-dashboard/profile-05.jpg"
                          alt="Img"
                        />
                      </a>
                      <div class="patient-name-info">
                        <h5>
                          <a href="javascript:void(0);">Robert</a>
                        </h5>
                        <span>#Apt0005</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="appointment-date-created">
                      <span class="paid-text">Paid On</span>
                      <h6>28 Oct 2024</h6>
                    </div>
                  </td>
                  <td>
                    <div class="appointment-date-created">
                      <span class="paid-text">Amount</span>
                      <h6>$380</h6>
                    </div>
                  </td>
                  <td>
                    <div class="apponiment-view d-flex align-items-center">
                      <a href="javascript:void(0);"
                        ><i class="isax isax-eye4"></i
                      ></a>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-xl-7 d-flex">
    <div class="dashboard-card w-100">
      <div class="dashboard-card-head">
        <div class="header-title">
          <h5>Notifications</h5>
        </div>
        <div class="card-view-link">
          <a href="javascript:void(0);">View All</a>
        </div>
      </div>
      <div class="dashboard-card-body">
        <div class="table-responsive">
          <table class="table dashboard-table">
            <tbody>
              <tr>
                <td>
                  <div class="table-noti-info">
                    <div class="table-noti-icon color-violet">
                      <i class="fa-solid fa-bell"></i>
                    </div>

                    <div class="table-noti-message">
                      <h6>
                        <a href="javascript:void(0);"
                          >Booking Confirmed on <span> 21 Mar 2024 </span> 10:30
                          AM</a
                        >
                      </h6>
                      <span class="message-time">Just Now</span>
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="table-noti-info">
                    <div class="table-noti-icon color-blue">
                      <i class="fa-solid fa-star"></i>
                    </div>

                    <div class="table-noti-message">
                      <h6>
                        <a href="javascript:void(0);"
                          >You have a <span> New </span> Review for your
                          Appointment
                        </a>
                      </h6>
                      <span class="message-time">5 Days ago</span>
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="table-noti-info">
                    <div class="table-noti-icon color-red">
                      <i class="fa-solid fa-calendar-check"></i>
                    </div>

                    <div class="table-noti-message">
                      <h6>
                        <a href="javascript:void(0);"
                          >You have Appointment with <span> Ahmed </span> by
                          01:20 PM
                        </a>
                      </h6>
                      <span class="message-time">12:55 PM</span>
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="table-noti-info">
                    <div class="table-noti-icon color-yellow">
                      <i class="fa-solid fa-money-bill-1-wave"></i>
                    </div>

                    <div class="table-noti-message">
                      <h6>
                        <a href="javascript:void(0);"
                          >Sent an amount of <span> $200 </span> for an
                          Appointment by 01:20 PM
                        </a>
                      </h6>
                      <span class="message-time">2 Days ago</span>
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="table-noti-info">
                    <div class="table-noti-icon color-blue">
                      <i class="fa-solid fa-star"></i>
                    </div>

                    <div class="table-noti-message">
                      <h6>
                        <a href="javascript:void(0);"
                          >You have a <span> New </span> Review for your
                          Appointment
                        </a>
                      </h6>
                      <span class="message-time">5 Days ago</span>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="col-xl-5 d-flex">
    <div class="dashboard-card w-100">
      <div class="dashboard-card-head">
        <div class="header-title">
          <h5>Clinics & Availability</h5>
        </div>
      </div>
      <div class="dashboard-card-body">
        <div class="clinic-available">
          <div class="clinic-head">
            <div class="clinic-info">
              <span class="clinic-img">
                <img
                  src="assets/img/doctors-dashboard/clinic-02.jpg"
                  alt="Img"
                />
              </span>
              <h6>Sofi’s Clinic</h6>
            </div>
            <div class="clinic-charge">
              <span>$900</span>
            </div>
          </div>
          <div class="available-time">
            <ul>
              <li>
                <span>Tue :</span>
                07:00 AM - 09:00 PM
              </li>
              <li>
                <span>Wed : </span>
                07:00 AM - 09:00 PM
              </li>
            </ul>
            <div class="change-time">
              <a href="javascript:void(0);">Change </a>
            </div>
          </div>
        </div>
        <div class="clinic-available mb-0">
          <div class="clinic-head">
            <div class="clinic-info">
              <span class="clinic-img">
                <img
                  src="assets/img/doctors-dashboard/clinic-01.jpg"
                  alt="Img"
                />
              </span>
              <h6>The Family Dentistry Clinic</h6>
            </div>
            <div class="clinic-charge">
              <span>$600</span>
            </div>
          </div>
          <div class="available-time">
            <ul>
              <li>
                <span>Sat :</span>
                07:00 AM - 09:00 PM
              </li>
              <li>
                <span>Tue : </span>
                07:00 AM - 09:00 PM
              </li>
            </ul>
            <div class="change-time">
              <a href="javascript:void(0);">Change </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> -->
